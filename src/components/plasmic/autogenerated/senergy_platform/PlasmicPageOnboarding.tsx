/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: cW7vJ_ZjQaBV

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>ice<PERSON>rg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import OnboardingOnboardingModal from "../../OnboardingOnboardingModal"; // plasmic-import: ptWaGH-zd0tJ/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicPageOnboarding.module.css"; // plasmic-import: cW7vJ_ZjQaBV/css

createPlasmicElementProxy;

export type PlasmicPageOnboarding__VariantMembers = {};
export type PlasmicPageOnboarding__VariantsArgs = {};
type VariantPropType = keyof PlasmicPageOnboarding__VariantsArgs;
export const PlasmicPageOnboarding__VariantProps = new Array<VariantPropType>();

export type PlasmicPageOnboarding__ArgsType = {};
type ArgPropType = keyof PlasmicPageOnboarding__ArgsType;
export const PlasmicPageOnboarding__ArgProps = new Array<ArgPropType>();

export type PlasmicPageOnboarding__OverridesType = {
  root?: Flex__<"div">;
  onOnboardingModal?: Flex__<typeof OnboardingOnboardingModal>;
};

export interface DefaultPageOnboardingProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicPageOnboarding__RenderFunc(props: {
  variants: PlasmicPageOnboarding__VariantsArgs;
  args: PlasmicPageOnboarding__ArgsType;
  overrides: PlasmicPageOnboarding__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "onOnboardingModal.firstName",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "onOnboardingModal.lastName",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "onOnboardingModal.location",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "onOnboardingModal.workingHours",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "onOnboardingModal.pitchIntro",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "onOnboardingModal.pitchRole",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "onOnboardingModal.pitchDescriptor",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <OnboardingOnboardingModal
        data-plasmic-name={"onOnboardingModal"}
        data-plasmic-override={overrides.onOnboardingModal}
        className={classNames("__wab_instance", sty.onOnboardingModal)}
        firstName={generateStateValueProp($state, [
          "onOnboardingModal",
          "firstName"
        ])}
        lastName={generateStateValueProp($state, [
          "onOnboardingModal",
          "lastName"
        ])}
        location={generateStateValueProp($state, [
          "onOnboardingModal",
          "location"
        ])}
        onFirstNameChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "onOnboardingModal",
            "firstName"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onLastNameChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "onOnboardingModal",
            "lastName"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onLocationChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "onOnboardingModal",
            "location"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPitchDescriptorChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "onOnboardingModal",
            "pitchDescriptor"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPitchIntroChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "onOnboardingModal",
            "pitchIntro"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPitchRoleChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "onOnboardingModal",
            "pitchRole"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onWorkingHoursChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "onOnboardingModal",
            "workingHours"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        pitchDescriptor={generateStateValueProp($state, [
          "onOnboardingModal",
          "pitchDescriptor"
        ])}
        pitchIntro={generateStateValueProp($state, [
          "onOnboardingModal",
          "pitchIntro"
        ])}
        pitchRole={generateStateValueProp($state, [
          "onOnboardingModal",
          "pitchRole"
        ])}
        workingHours={generateStateValueProp($state, [
          "onOnboardingModal",
          "workingHours"
        ])}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "onOnboardingModal"],
  onOnboardingModal: ["onOnboardingModal"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  onOnboardingModal: typeof OnboardingOnboardingModal;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicPageOnboarding__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicPageOnboarding__VariantsArgs;
    args?: PlasmicPageOnboarding__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicPageOnboarding__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicPageOnboarding__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPageOnboarding__ArgProps,
          internalVariantPropNames: PlasmicPageOnboarding__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicPageOnboarding__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicPageOnboarding";
  } else {
    func.displayName = `PlasmicPageOnboarding.${nodeName}`;
  }
  return func;
}

export const PlasmicPageOnboarding = Object.assign(
  // Top-level PlasmicPageOnboarding renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    onOnboardingModal: makeNodeComponent("onOnboardingModal"),

    // Metadata about props expected for PlasmicPageOnboarding
    internalVariantProps: PlasmicPageOnboarding__VariantProps,
    internalArgProps: PlasmicPageOnboarding__ArgProps
  }
);

export default PlasmicPageOnboarding;
/* prettier-ignore-end */
