/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: Pt5FPuEzirSe

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentTextInput.module.css"; // plasmic-import: Pt5FPuEzirSe/css

import AlertFilledIcon from "./icons/PlasmicIcon__AlertFilled"; // plasmic-import: fGdp1-n78qAE/icon

createPlasmicElementProxy;

export type PlasmicSubcomponentTextInput__VariantMembers = {
  color: "dark" | "light";
  editView: "core" | "subHeading" | "heading3" | "heading2" | "heading1";
  viewOnly: "viewOnly";
  bold: "bold";
  leftJustification: "leftJustification";
  pressEnterText: "pressEnterText";
  errorState: "errorState";
  required: "required";
  pressEnterText2: "pressEnterHidden" | "pressEnterShown";
};
export type PlasmicSubcomponentTextInput__VariantsArgs = {
  color?: SingleChoiceArg<"dark" | "light">;
  editView?: SingleChoiceArg<
    "core" | "subHeading" | "heading3" | "heading2" | "heading1"
  >;
  viewOnly?: SingleBooleanChoiceArg<"viewOnly">;
  bold?: SingleBooleanChoiceArg<"bold">;
  leftJustification?: SingleBooleanChoiceArg<"leftJustification">;
  pressEnterText?: SingleBooleanChoiceArg<"pressEnterText">;
  errorState?: SingleBooleanChoiceArg<"errorState">;
  required?: SingleBooleanChoiceArg<"required">;
  pressEnterText2?: SingleChoiceArg<"pressEnterHidden" | "pressEnterShown">;
};
type VariantPropType = keyof PlasmicSubcomponentTextInput__VariantsArgs;
export const PlasmicSubcomponentTextInput__VariantProps =
  new Array<VariantPropType>(
    "color",
    "editView",
    "viewOnly",
    "bold",
    "leftJustification",
    "pressEnterText",
    "errorState",
    "required",
    "pressEnterText2"
  );

export type PlasmicSubcomponentTextInput__ArgsType = {
  displayText?: string;
  inputValue?: string;
  onInputValueChange?: (val: string) => void;
  inputPlaceholder?: string;
  inputNameAsPlaceholder?: boolean;
  inputHoverText?: string;
  inputDisabled?: boolean;
  inputType?:
    | "text"
    | "password"
    | "hidden"
    | "number"
    | "date"
    | "datetime-local"
    | "time"
    | "email"
    | "tel";
  inputAutoComplete?:
    | { label: "Off"; value: "off" }
    | { label: "On"; value: "on" }
    | { label: "Name"; value: "name" }
    | { label: "Honorific-prefix"; value: "honorific-prefix" }
    | { label: "Given-name"; value: "given-name" }
    | { label: "Additional-name"; value: "additional-name" }
    | { label: "Family-name"; value: "family-name" }
    | { label: "Honorific-suffix"; value: "honorific-suffix" }
    | { label: "Nickname"; value: "nickname" }
    | { label: "Email"; value: "email" }
    | { label: "Username"; value: "username" }
    | { label: "New-password"; value: "new-password" }
    | { label: "Current-password"; value: "current-password" }
    | { label: "One-time-code"; value: "one-time-code" }
    | { label: "Organization-title"; value: "organization-title" }
    | { label: "Organization"; value: "organization" }
    | { label: "Street-address"; value: "street-address" }
    | { label: "Address-line1"; value: "address-line1" }
    | { label: "Address-line2"; value: "address-line2" }
    | { label: "Address-line3"; value: "address-line3" }
    | { label: "Address-level4"; value: "address-level4" }
    | { label: "Address-level3"; value: "address-level3" }
    | { label: "Address-level2"; value: "address-level2" }
    | { label: "Address-level1"; value: "address-level1" }
    | { label: "Country"; value: "country" }
    | { label: "Country-name"; value: "country-name" }
    | { label: "Postal-code"; value: "postal-code" }
    | { label: "Cc-name"; value: "cc-name" }
    | { label: "Cc-given-name"; value: "cc-given-name" }
    | { label: "Cc-additional-name"; value: "cc-additional-name" }
    | { label: "Cc-family-name"; value: "cc-family-name" }
    | { label: "Cc-number"; value: "cc-number" }
    | { label: "Cc-exp"; value: "cc-exp" }
    | { label: "Cc-exp-month"; value: "cc-exp-month" }
    | { label: "Cc-exp-year"; value: "cc-exp-year" }
    | { label: "Cc-csc"; value: "cc-csc" }
    | { label: "Cc-type"; value: "cc-type" }
    | { label: "Transaction-currency"; value: "transaction-currency" }
    | { label: "Transaction-amount"; value: "transaction-amount" }
    | { label: "Language"; value: "language" }
    | { label: "Bday"; value: "bday" }
    | { label: "Bday-day"; value: "bday-day" }
    | { label: "Bday-month"; value: "bday-month" }
    | { label: "Bday-year"; value: "bday-year" }
    | { label: "Sex"; value: "sex" }
    | { label: "Url"; value: "url" }
    | { label: "Photo"; value: "photo" }
    | { label: "Tel"; value: "tel" }
    | { label: "Tel-country-code"; value: "tel-country-code" }
    | { label: "Tel-national"; value: "tel-national" }
    | { label: "Tel-area-code"; value: "tel-area-code" }
    | { label: "Tel-local"; value: "tel-local" }
    | { label: "Tel-local-prefix"; value: "tel-local-prefix" }
    | { label: "Tel-local-suffix"; value: "tel-local-suffix" }
    | { label: "Tel-extension"; value: "tel-extension" }
    | { label: "Impp"; value: "impp" };
  inputTabIndex?: number;
  inputName?: string;
  inputAriaLabel?: string;
  inputAriaLabelledby?: string;
  inputAriaDescribedby?: string;
  inputAriaHidden?:
    | { label: "True"; value: "true" }
    | { label: "False"; value: "false" };
  errorMessage?: string;
  onErrorMessageChange?: (val: string) => void;
  fieldNameRemainVisible?: boolean;
};
type ArgPropType = keyof PlasmicSubcomponentTextInput__ArgsType;
export const PlasmicSubcomponentTextInput__ArgProps = new Array<ArgPropType>(
  "displayText",
  "inputValue",
  "onInputValueChange",
  "inputPlaceholder",
  "inputNameAsPlaceholder",
  "inputHoverText",
  "inputDisabled",
  "inputType",
  "inputAutoComplete",
  "inputTabIndex",
  "inputName",
  "inputAriaLabel",
  "inputAriaLabelledby",
  "inputAriaDescribedby",
  "inputAriaHidden",
  "errorMessage",
  "onErrorMessageChange",
  "fieldNameRemainVisible"
);

export type PlasmicSubcomponentTextInput__OverridesType = {
  textInputContainer?: Flex__<"div">;
  input?: Flex__<"input">;
  popupText?: Flex__<"div">;
  errorStateContainer?: Flex__<"div">;
  errorMessageDisplay?: Flex__<"div">;
};

export interface DefaultSubcomponentTextInputProps {
  displayText?: string;
  inputValue?: string;
  onInputValueChange?: (val: string) => void;
  inputPlaceholder?: string;
  inputNameAsPlaceholder?: boolean;
  inputHoverText?: string;
  inputDisabled?: boolean;
  inputType?:
    | "text"
    | "password"
    | "hidden"
    | "number"
    | "date"
    | "datetime-local"
    | "time"
    | "email"
    | "tel";
  inputAutoComplete?:
    | { label: "Off"; value: "off" }
    | { label: "On"; value: "on" }
    | { label: "Name"; value: "name" }
    | { label: "Honorific-prefix"; value: "honorific-prefix" }
    | { label: "Given-name"; value: "given-name" }
    | { label: "Additional-name"; value: "additional-name" }
    | { label: "Family-name"; value: "family-name" }
    | { label: "Honorific-suffix"; value: "honorific-suffix" }
    | { label: "Nickname"; value: "nickname" }
    | { label: "Email"; value: "email" }
    | { label: "Username"; value: "username" }
    | { label: "New-password"; value: "new-password" }
    | { label: "Current-password"; value: "current-password" }
    | { label: "One-time-code"; value: "one-time-code" }
    | { label: "Organization-title"; value: "organization-title" }
    | { label: "Organization"; value: "organization" }
    | { label: "Street-address"; value: "street-address" }
    | { label: "Address-line1"; value: "address-line1" }
    | { label: "Address-line2"; value: "address-line2" }
    | { label: "Address-line3"; value: "address-line3" }
    | { label: "Address-level4"; value: "address-level4" }
    | { label: "Address-level3"; value: "address-level3" }
    | { label: "Address-level2"; value: "address-level2" }
    | { label: "Address-level1"; value: "address-level1" }
    | { label: "Country"; value: "country" }
    | { label: "Country-name"; value: "country-name" }
    | { label: "Postal-code"; value: "postal-code" }
    | { label: "Cc-name"; value: "cc-name" }
    | { label: "Cc-given-name"; value: "cc-given-name" }
    | { label: "Cc-additional-name"; value: "cc-additional-name" }
    | { label: "Cc-family-name"; value: "cc-family-name" }
    | { label: "Cc-number"; value: "cc-number" }
    | { label: "Cc-exp"; value: "cc-exp" }
    | { label: "Cc-exp-month"; value: "cc-exp-month" }
    | { label: "Cc-exp-year"; value: "cc-exp-year" }
    | { label: "Cc-csc"; value: "cc-csc" }
    | { label: "Cc-type"; value: "cc-type" }
    | { label: "Transaction-currency"; value: "transaction-currency" }
    | { label: "Transaction-amount"; value: "transaction-amount" }
    | { label: "Language"; value: "language" }
    | { label: "Bday"; value: "bday" }
    | { label: "Bday-day"; value: "bday-day" }
    | { label: "Bday-month"; value: "bday-month" }
    | { label: "Bday-year"; value: "bday-year" }
    | { label: "Sex"; value: "sex" }
    | { label: "Url"; value: "url" }
    | { label: "Photo"; value: "photo" }
    | { label: "Tel"; value: "tel" }
    | { label: "Tel-country-code"; value: "tel-country-code" }
    | { label: "Tel-national"; value: "tel-national" }
    | { label: "Tel-area-code"; value: "tel-area-code" }
    | { label: "Tel-local"; value: "tel-local" }
    | { label: "Tel-local-prefix"; value: "tel-local-prefix" }
    | { label: "Tel-local-suffix"; value: "tel-local-suffix" }
    | { label: "Tel-extension"; value: "tel-extension" }
    | { label: "Impp"; value: "impp" };
  inputTabIndex?: number;
  inputName?: string;
  inputAriaLabel?: string;
  inputAriaLabelledby?: string;
  inputAriaDescribedby?: string;
  inputAriaHidden?:
    | { label: "True"; value: "true" }
    | { label: "False"; value: "false" };
  errorMessage?: string;
  onErrorMessageChange?: (val: string) => void;
  fieldNameRemainVisible?: boolean;
  color?: SingleChoiceArg<"dark" | "light">;
  editView?: SingleChoiceArg<
    "core" | "subHeading" | "heading3" | "heading2" | "heading1"
  >;
  viewOnly?: SingleBooleanChoiceArg<"viewOnly">;
  bold?: SingleBooleanChoiceArg<"bold">;
  leftJustification?: SingleBooleanChoiceArg<"leftJustification">;
  pressEnterText?: SingleBooleanChoiceArg<"pressEnterText">;
  errorState?: SingleBooleanChoiceArg<"errorState">;
  required?: SingleBooleanChoiceArg<"required">;
  pressEnterText2?: SingleChoiceArg<"pressEnterHidden" | "pressEnterShown">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentTextInput__RenderFunc(props: {
  variants: PlasmicSubcomponentTextInput__VariantsArgs;
  args: PlasmicSubcomponentTextInput__ArgsType;
  overrides: PlasmicSubcomponentTextInput__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          inputPlaceholder: ``,
          inputNameAsPlaceholder: true,
          inputType: "text",
          fieldNameRemainVisible: true
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "color",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.color
      },
      {
        path: "editView",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editView
      },
      {
        path: "viewOnly",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.viewOnly
      },
      {
        path: "input.value",
        type: "writable",
        variableType: "text",

        valueProp: "inputValue",
        onChangeProp: "onInputValueChange"
      },
      {
        path: "bold",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.bold
      },
      {
        path: "leftJustification",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.leftJustification
      },
      {
        path: "pressEnterText",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.pressEnterText
      },
      {
        path: "errorState",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.errorState
      },
      {
        path: "errorMessage",
        type: "writable",
        variableType: "text",

        valueProp: "errorMessage",
        onChangeProp: "onErrorMessageChange"
      },
      {
        path: "required",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.required
      },
      {
        path: "pressEnterText2",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.pressEnterText2
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const [
    isTextInputContainerFocusVisibleWithin,
    triggerTextInputContainerFocusVisibleWithinProps
  ] = useTrigger("useFocusVisibleWithin", {
    isTextInput: false
  });
  const [
    isTextInputContainerFocusWithin,
    triggerTextInputContainerFocusWithinProps
  ] = useTrigger("useFocusedWithin", {});
  const triggers = {
    focusVisibleWithin_textInputContainer:
      isTextInputContainerFocusVisibleWithin,
    focusWithin_textInputContainer: isTextInputContainerFocusWithin
  };

  return (
    <div
      data-plasmic-name={"textInputContainer"}
      data-plasmic-override={overrides.textInputContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.textInputContainer,
        {
          [sty.textInputContainer___focusVisibleWithin]:
            triggers.focusVisibleWithin_textInputContainer,
          [sty.textInputContainerbold]: hasVariant($state, "bold", "bold"),
          [sty.textInputContainercolor_dark]: hasVariant(
            $state,
            "color",
            "dark"
          ),
          [sty.textInputContainercolor_light]: hasVariant(
            $state,
            "color",
            "light"
          ),
          [sty.textInputContainereditView_core]: hasVariant(
            $state,
            "editView",
            "core"
          ),
          [sty.textInputContainereditView_heading1]: hasVariant(
            $state,
            "editView",
            "heading1"
          ),
          [sty.textInputContainereditView_heading2]: hasVariant(
            $state,
            "editView",
            "heading2"
          ),
          [sty.textInputContainereditView_heading3]: hasVariant(
            $state,
            "editView",
            "heading3"
          ),
          [sty.textInputContainereditView_subHeading]: hasVariant(
            $state,
            "editView",
            "subHeading"
          ),
          [sty.textInputContainererrorState]: hasVariant(
            $state,
            "errorState",
            "errorState"
          ),
          [sty.textInputContainerleftJustification]: hasVariant(
            $state,
            "leftJustification",
            "leftJustification"
          ),
          [sty.textInputContainerpressEnterText2_pressEnterHidden]: hasVariant(
            $state,
            "pressEnterText2",
            "pressEnterHidden"
          ),
          [sty.textInputContainerpressEnterText2_pressEnterShown]: hasVariant(
            $state,
            "pressEnterText2",
            "pressEnterShown"
          ),
          [sty.textInputContainerpressEnterText]: hasVariant(
            $state,
            "pressEnterText",
            "pressEnterText"
          ),
          [sty.textInputContainerrequired]: hasVariant(
            $state,
            "required",
            "required"
          ),
          [sty.textInputContainerviewOnly]: hasVariant(
            $state,
            "viewOnly",
            "viewOnly"
          ),
          [sty.textInputContainerviewOnly_bold]:
            hasVariant($state, "bold", "bold") &&
            hasVariant($state, "viewOnly", "viewOnly"),
          [sty.textInputContainerviewOnly_editView_heading1]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "heading1"),
          [sty.textInputContainerviewOnly_editView_heading2]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "heading2"),
          [sty.textInputContainerviewOnly_editView_heading3]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "heading3"),
          [sty.textInputContainerviewOnly_editView_subHeading]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "subHeading")
        }
      )}
      data-plasmic-trigger-props={[
        triggerTextInputContainerFocusVisibleWithinProps,
        triggerTextInputContainerFocusWithinProps
      ]}
    >
      <div
        className={classNames(projectcss.all, sty.freeBox__hgsOr, {
          [sty.freeBox___focusVisibleWithin__hgsOr1P9I]:
            triggers.focusVisibleWithin_textInputContainer,
          [sty.freeBoxerrorState__hgsOrtmMmW]: hasVariant(
            $state,
            "errorState",
            "errorState"
          ),
          [sty.freeBoxpressEnterText2_pressEnterHidden__hgsORcgoP8]: hasVariant(
            $state,
            "pressEnterText2",
            "pressEnterHidden"
          ),
          [sty.freeBoxpressEnterText2_pressEnterShown__hgsOrMsJi]: hasVariant(
            $state,
            "pressEnterText2",
            "pressEnterShown"
          ),
          [sty.freeBoxpressEnterText__hgsORo5XQz]: hasVariant(
            $state,
            "pressEnterText",
            "pressEnterText"
          ),
          [sty.freeBoxviewOnly__hgsOr0Hff2]: hasVariant(
            $state,
            "viewOnly",
            "viewOnly"
          ),
          [sty.freeBoxviewOnly_editView_heading1__hgsOr0Hff2UxriI]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "heading1"),
          [sty.freeBoxviewOnly_editView_heading2__hgsOr0Hff2UMhCt]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "heading2"),
          [sty.freeBoxviewOnly_editView_heading3__hgsOr0Hff2KpWkg]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "heading3"),
          [sty.freeBoxviewOnly_editView_subHeading__hgsOr0Hff21Uxi2]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "subHeading")
        })}
      >
        {(
          triggers.focusVisibleWithin_textInputContainer
            ? true
            : triggers.focusWithin_textInputContainer
            ? true
            : hasVariant($state, "viewOnly", "viewOnly")
            ? true
            : (() => {
                try {
                  return (
                    ($props.fieldNameRemainVisible === true &&
                      !(
                        $props.inputNameAsPlaceholder === true &&
                        ($state.input.value === null ||
                          $state.input.value === "" ||
                          $state.input.value === undefined)
                      )) ||
                    ($props.inputNameAsPlaceholder === true &&
                      $state.input.value != null &&
                      $state.input.value !== "" &&
                      $state.input.value !== undefined) ||
                    ($props.inputNameAsPlaceholder === false &&
                      ($state.input.value === null ||
                        $state.input.value === "" ||
                        $state.input.value === undefined))
                  );
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return false;
                  }
                  throw e;
                }
              })()
        ) ? (
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__hi727,
              {
                [sty.text___focusVisibleWithin__hi7271P9I]:
                  triggers.focusVisibleWithin_textInputContainer,
                [sty.texteditView_subHeading__hi7271Uxi2]: hasVariant(
                  $state,
                  "editView",
                  "subHeading"
                ),
                [sty.texterrorState__hi727TmMmW]: hasVariant(
                  $state,
                  "errorState",
                  "errorState"
                ),
                [sty.textrequired__hi727KKBr4]: hasVariant(
                  $state,
                  "required",
                  "required"
                ),
                [sty.textviewOnly__hi7270Hff2]: hasVariant(
                  $state,
                  "viewOnly",
                  "viewOnly"
                )
              }
            )}
          >
            {hasVariant($state, "required", "required") ? (
              <React.Fragment>
                {(() => {
                  try {
                    return (
                      $props.inputName +
                      ($props.inputPlaceholder &&
                      $props.inputPlaceholder !== "Enter Something..."
                        ? " - " + "(" + $props.inputPlaceholder + ")"
                        : "") +
                      "*"
                    );
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return "Placeholder Name (Ex: Something)";
                    }
                    throw e;
                  }
                })()}
              </React.Fragment>
            ) : (
              <React.Fragment>
                {(() => {
                  try {
                    return $props.inputName;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return "Placeholder Name (Ex: Something)";
                    }
                    throw e;
                  }
                })()}
              </React.Fragment>
            )}
          </div>
        ) : null}
      </div>
      <div
        className={classNames(
          projectcss.all,
          projectcss.__wab_text,
          sty.text__jNpeO,
          {
            [sty.texterrorState__jNpeOtmMmW]: hasVariant(
              $state,
              "errorState",
              "errorState"
            ),
            [sty.textpressEnterText2_pressEnterHidden__jNpeOcgoP8]: hasVariant(
              $state,
              "pressEnterText2",
              "pressEnterHidden"
            ),
            [sty.textpressEnterText2_pressEnterShown__jNpeOMsJi]: hasVariant(
              $state,
              "pressEnterText2",
              "pressEnterShown"
            ),
            [sty.textpressEnterText__jNpeOo5XQz]: hasVariant(
              $state,
              "pressEnterText",
              "pressEnterText"
            ),
            [sty.textviewOnly__jNpeO0Hff2]: hasVariant(
              $state,
              "viewOnly",
              "viewOnly"
            ),
            [sty.textviewOnly_bold__jNpeO0Hff2ZiRge]:
              hasVariant($state, "bold", "bold") &&
              hasVariant($state, "viewOnly", "viewOnly"),
            [sty.textviewOnly_editView_core__jNpeO0Hff2LjoO]:
              hasVariant($state, "editView", "core") &&
              hasVariant($state, "viewOnly", "viewOnly"),
            [sty.textviewOnly_editView_heading1__jNpeO0Hff2UxriI]:
              hasVariant($state, "viewOnly", "viewOnly") &&
              hasVariant($state, "editView", "heading1"),
            [sty.textviewOnly_editView_heading2__jNpeO0Hff2UMhCt]:
              hasVariant($state, "viewOnly", "viewOnly") &&
              hasVariant($state, "editView", "heading2"),
            [sty.textviewOnly_editView_heading3__jNpeO0Hff2KpWkg]:
              hasVariant($state, "viewOnly", "viewOnly") &&
              hasVariant($state, "editView", "heading3"),
            [sty.textviewOnly_editView_subHeading__jNpeO0Hff21Uxi2]:
              hasVariant($state, "viewOnly", "viewOnly") &&
              hasVariant($state, "editView", "subHeading")
          }
        )}
      >
        <React.Fragment>
          {(() => {
            try {
              return $props.displayText;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return "This is text.....";
              }
              throw e;
            }
          })()}
        </React.Fragment>
      </div>
      <div
        className={classNames(projectcss.all, sty.freeBox___20NMw, {
          [sty.freeBox___focusVisibleWithin___20NMw1P9I]:
            triggers.focusVisibleWithin_textInputContainer
        })}
      >
        <input
          data-plasmic-name={"input"}
          data-plasmic-override={overrides.input}
          aria-describedby={args.inputAriaDescribedby}
          aria-hidden={args.inputAriaHidden}
          aria-label={args.inputAriaLabel}
          aria-labelledby={args.inputAriaLabelledby}
          autoComplete={args.inputAutoComplete}
          className={classNames(projectcss.all, projectcss.input, sty.input, {
            [sty.input___focusVisibleWithin]:
              triggers.focusVisibleWithin_textInputContainer,
            [sty.inputbold]: hasVariant($state, "bold", "bold"),
            [sty.inputcolor_dark]: hasVariant($state, "color", "dark"),
            [sty.inputcolor_light]: hasVariant($state, "color", "light"),
            [sty.inputeditView_core]: hasVariant($state, "editView", "core"),
            [sty.inputeditView_heading1]: hasVariant(
              $state,
              "editView",
              "heading1"
            ),
            [sty.inputeditView_heading2]: hasVariant(
              $state,
              "editView",
              "heading2"
            ),
            [sty.inputeditView_heading2_pressEnterText]:
              hasVariant($state, "editView", "heading2") &&
              hasVariant($state, "pressEnterText", "pressEnterText"),
            [sty.inputeditView_heading3]: hasVariant(
              $state,
              "editView",
              "heading3"
            ),
            [sty.inputeditView_subHeading]: hasVariant(
              $state,
              "editView",
              "subHeading"
            ),
            [sty.inputerrorState]: hasVariant(
              $state,
              "errorState",
              "errorState"
            ),
            [sty.inputleftJustification]: hasVariant(
              $state,
              "leftJustification",
              "leftJustification"
            ),
            [sty.inputpressEnterText2_pressEnterHidden]: hasVariant(
              $state,
              "pressEnterText2",
              "pressEnterHidden"
            ),
            [sty.inputpressEnterText2_pressEnterShown]: hasVariant(
              $state,
              "pressEnterText2",
              "pressEnterShown"
            ),
            [sty.inputpressEnterText]: hasVariant(
              $state,
              "pressEnterText",
              "pressEnterText"
            ),
            [sty.inputrequired]: hasVariant($state, "required", "required"),
            [sty.inputviewOnly]: hasVariant($state, "viewOnly", "viewOnly")
          })}
          disabled={args.inputDisabled}
          name={args.inputName}
          onChange={async (...eventArgs: any) => {
            (e => {
              generateStateOnChangeProp($state, ["input", "value"])(
                e.target.value
              );
            }).apply(null, eventArgs);
          }}
          placeholder={
            triggers.focusVisibleWithin_textInputContainer
              ? (() => {
                  try {
                    return $props.inputPlaceholder;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
              : triggers.focusWithin_textInputContainer
              ? (() => {
                  try {
                    return $props.inputPlaceholder;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
              : (() => {
                  try {
                    return $props.inputNameAsPlaceholder
                      ? $props.inputName
                      : $props.inputPlaceholder;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
          }
          ref={ref => {
            $refs["input"] = ref;
          }}
          tabIndex={args.inputTabIndex}
          title={args.inputHoverText}
          type={args.inputType}
          value={generateStateValueProp($state, ["input", "value"]) ?? ""}
        />

        <svg
          className={classNames(projectcss.all, sty.svg___3PkS)}
          role={"img"}
        />
      </div>
      {(
        hasVariant($state, "pressEnterText2", "pressEnterShown")
          ? true
          : hasVariant($state, "pressEnterText2", "pressEnterHidden")
          ? true
          : hasVariant($state, "pressEnterText", "pressEnterText")
          ? (() => {
              try {
                return (
                  $state.input.value !== undefined &&
                  $state.input.value !== null &&
                  $state.input.value.trim() !== ""
                );
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return true;
                }
                throw e;
              }
            })()
          : true
      ) ? (
        <div
          data-plasmic-name={"popupText"}
          data-plasmic-override={overrides.popupText}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.popupText,
            {
              [sty.popupTexterrorState]: hasVariant(
                $state,
                "errorState",
                "errorState"
              ),
              [sty.popupTextpressEnterText2_pressEnterHidden]: hasVariant(
                $state,
                "pressEnterText2",
                "pressEnterHidden"
              ),
              [sty.popupTextpressEnterText2_pressEnterShown]: hasVariant(
                $state,
                "pressEnterText2",
                "pressEnterShown"
              ),
              [sty.popupTextpressEnterText]: hasVariant(
                $state,
                "pressEnterText",
                "pressEnterText"
              )
            }
          )}
        >
          {"Press Enter to Submit"}
        </div>
      ) : null}
      <div
        data-plasmic-name={"errorStateContainer"}
        data-plasmic-override={overrides.errorStateContainer}
        className={classNames(projectcss.all, sty.errorStateContainer, {
          [sty.errorStateContainererrorState]: hasVariant(
            $state,
            "errorState",
            "errorState"
          ),
          [sty.errorStateContainerpressEnterText2_pressEnterHidden]: hasVariant(
            $state,
            "pressEnterText2",
            "pressEnterHidden"
          ),
          [sty.errorStateContainerpressEnterText2_pressEnterShown]: hasVariant(
            $state,
            "pressEnterText2",
            "pressEnterShown"
          ),
          [sty.errorStateContainerpressEnterText]: hasVariant(
            $state,
            "pressEnterText",
            "pressEnterText"
          ),
          [sty.errorStateContainerviewOnly]: hasVariant(
            $state,
            "viewOnly",
            "viewOnly"
          ),
          [sty.errorStateContainerviewOnly_editView_heading1]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "heading1"),
          [sty.errorStateContainerviewOnly_editView_heading2]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "heading2"),
          [sty.errorStateContainerviewOnly_editView_heading3]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "heading3"),
          [sty.errorStateContainerviewOnly_editView_subHeading]:
            hasVariant($state, "viewOnly", "viewOnly") &&
            hasVariant($state, "editView", "subHeading")
        })}
      >
        <AlertFilledIcon
          className={classNames(projectcss.all, sty.svg__bncuj, {
            [sty.svgerrorState__bncujTmMmW]: hasVariant(
              $state,
              "errorState",
              "errorState"
            )
          })}
          role={"img"}
        />

        <div
          data-plasmic-name={"errorMessageDisplay"}
          data-plasmic-override={overrides.errorMessageDisplay}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.errorMessageDisplay,
            {
              [sty.errorMessageDisplayerrorState]: hasVariant(
                $state,
                "errorState",
                "errorState"
              ),
              [sty.errorMessageDisplaypressEnterText2_pressEnterHidden]:
                hasVariant($state, "pressEnterText2", "pressEnterHidden"),
              [sty.errorMessageDisplaypressEnterText2_pressEnterShown]:
                hasVariant($state, "pressEnterText2", "pressEnterShown"),
              [sty.errorMessageDisplaypressEnterText]: hasVariant(
                $state,
                "pressEnterText",
                "pressEnterText"
              )
            }
          )}
        >
          <React.Fragment>
            {(() => {
              try {
                return $state.errorMessage;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return "Error";
                }
                throw e;
              }
            })()}
          </React.Fragment>
        </div>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  textInputContainer: [
    "textInputContainer",
    "input",
    "popupText",
    "errorStateContainer",
    "errorMessageDisplay"
  ],
  input: ["input"],
  popupText: ["popupText"],
  errorStateContainer: ["errorStateContainer", "errorMessageDisplay"],
  errorMessageDisplay: ["errorMessageDisplay"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  textInputContainer: "div";
  input: "input";
  popupText: "div";
  errorStateContainer: "div";
  errorMessageDisplay: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentTextInput__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentTextInput__VariantsArgs;
    args?: PlasmicSubcomponentTextInput__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubcomponentTextInput__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentTextInput__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentTextInput__ArgProps,
          internalVariantPropNames: PlasmicSubcomponentTextInput__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentTextInput__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "textInputContainer") {
    func.displayName = "PlasmicSubcomponentTextInput";
  } else {
    func.displayName = `PlasmicSubcomponentTextInput.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentTextInput = Object.assign(
  // Top-level PlasmicSubcomponentTextInput renders the root element
  makeNodeComponent("textInputContainer"),
  {
    // Helper components rendering sub-elements
    input: makeNodeComponent("input"),
    popupText: makeNodeComponent("popupText"),
    errorStateContainer: makeNodeComponent("errorStateContainer"),
    errorMessageDisplay: makeNodeComponent("errorMessageDisplay"),

    // Metadata about props expected for PlasmicSubcomponentTextInput
    internalVariantProps: PlasmicSubcomponentTextInput__VariantProps,
    internalArgProps: PlasmicSubcomponentTextInput__ArgProps
  }
);

export default PlasmicSubcomponentTextInput;
/* prettier-ignore-end */
