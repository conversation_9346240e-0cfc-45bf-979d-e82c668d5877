/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: p6_6lEP1C3cJ

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileOverviewOverviewContentBlock from "../../ProfileOverviewOverviewContentBlock"; // plasmic-import: 8Z-KG0onltAY/component
import ProfileOverviewSeeMoreButton from "../../ProfileOverviewSeeMoreButton"; // plasmic-import: UWTH8LMACb3s/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileOverview.module.css"; // plasmic-import: p6_6lEP1C3cJ/css

createPlasmicElementProxy;

export type PlasmicProfileOverview__VariantMembers = {};
export type PlasmicProfileOverview__VariantsArgs = {};
type VariantPropType = keyof PlasmicProfileOverview__VariantsArgs;
export const PlasmicProfileOverview__VariantProps =
  new Array<VariantPropType>();

export type PlasmicProfileOverview__ArgsType = {};
type ArgPropType = keyof PlasmicProfileOverview__ArgsType;
export const PlasmicProfileOverview__ArgProps = new Array<ArgPropType>();

export type PlasmicProfileOverview__OverridesType = {
  root?: Flex__<"div">;
  pOverviewContentBlock?: Flex__<typeof ProfileOverviewOverviewContentBlock>;
};

export interface DefaultProfileOverviewProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileOverview__RenderFunc(props: {
  variants: PlasmicProfileOverview__VariantsArgs;
  args: PlasmicProfileOverview__ArgsType;
  overrides: PlasmicProfileOverview__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "pOverviewContentBlock.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pOverviewContentBlock.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pOverviewContentBlock.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pOverviewContentBlock.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pOverviewContentBlock.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pOverviewContentBlock.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pOverviewContentBlock.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <ProfileOverviewOverviewContentBlock
        data-plasmic-name={"pOverviewContentBlock"}
        data-plasmic-override={overrides.pOverviewContentBlock}
        allCertifications={generateStateValueProp($state, [
          "pOverviewContentBlock",
          "allCertifications"
        ])}
        allEducation={generateStateValueProp($state, [
          "pOverviewContentBlock",
          "allEducation"
        ])}
        allExperience={generateStateValueProp($state, [
          "pOverviewContentBlock",
          "allExperience"
        ])}
        allLicenses={generateStateValueProp($state, [
          "pOverviewContentBlock",
          "allLicenses"
        ])}
        allPatents={generateStateValueProp($state, [
          "pOverviewContentBlock",
          "allPatents"
        ])}
        allPublications={generateStateValueProp($state, [
          "pOverviewContentBlock",
          "allPublications"
        ])}
        allTrademarks={generateStateValueProp($state, [
          "pOverviewContentBlock",
          "allTrademarks"
        ])}
        onAllCertificationsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pOverviewContentBlock",
            "allCertifications"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onAllEducationChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pOverviewContentBlock",
            "allEducation"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onAllExperienceChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pOverviewContentBlock",
            "allExperience"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onAllLicensesChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pOverviewContentBlock",
            "allLicenses"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onAllPatentsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pOverviewContentBlock",
            "allPatents"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onAllPublicationsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pOverviewContentBlock",
            "allPublications"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onAllTrademarksChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pOverviewContentBlock",
            "allTrademarks"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "pOverviewContentBlock"],
  pOverviewContentBlock: ["pOverviewContentBlock"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  pOverviewContentBlock: typeof ProfileOverviewOverviewContentBlock;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileOverview__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileOverview__VariantsArgs;
    args?: PlasmicProfileOverview__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileOverview__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileOverview__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileOverview__ArgProps,
          internalVariantPropNames: PlasmicProfileOverview__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileOverview__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicProfileOverview";
  } else {
    func.displayName = `PlasmicProfileOverview.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileOverview = Object.assign(
  // Top-level PlasmicProfileOverview renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    pOverviewContentBlock: makeNodeComponent("pOverviewContentBlock"),

    // Metadata about props expected for PlasmicProfileOverview
    internalVariantProps: PlasmicProfileOverview__VariantProps,
    internalArgProps: PlasmicProfileOverview__ArgProps
  }
);

export default PlasmicProfileOverview;
/* prettier-ignore-end */
