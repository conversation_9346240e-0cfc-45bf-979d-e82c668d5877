/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: XngpIn4SLbDb

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsEducationBlock from "../../ProfileSectionsEducationBlock"; // plasmic-import: Z1o0_V1Y-Oht/component
import ProfileTileEducationTile from "../../ProfileTileEducationTile"; // plasmic-import: 6P769Hd_cTVa/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsUnderConstructionDisplay.module.css"; // plasmic-import: XngpIn4SLbDb/css

import GradCapIcon from "./icons/PlasmicIcon__GradCap"; // plasmic-import: i9bbaFOlpQVC/icon
import BuildingIcon from "./icons/PlasmicIcon__Building"; // plasmic-import: RuQrxOj-BhnB/icon
import PublicationBookIcon from "./icons/PlasmicIcon__PublicationBook"; // plasmic-import: 1vlHySSOKWkP/icon
import BarrierIcon from "./icons/PlasmicIcon__Barrier"; // plasmic-import: TBmN1RDMqt46/icon

createPlasmicElementProxy;

export type PlasmicProfileSectionsUnderConstructionDisplay__VariantMembers = {};
export type PlasmicProfileSectionsUnderConstructionDisplay__VariantsArgs = {};
type VariantPropType =
  keyof PlasmicProfileSectionsUnderConstructionDisplay__VariantsArgs;
export const PlasmicProfileSectionsUnderConstructionDisplay__VariantProps =
  new Array<VariantPropType>();

export type PlasmicProfileSectionsUnderConstructionDisplay__ArgsType = {};
type ArgPropType =
  keyof PlasmicProfileSectionsUnderConstructionDisplay__ArgsType;
export const PlasmicProfileSectionsUnderConstructionDisplay__ArgProps =
  new Array<ArgPropType>();

export type PlasmicProfileSectionsUnderConstructionDisplay__OverridesType = {
  formattingContainer?: Flex__<"div">;
  freeBox?: Flex__<"div">;
  pSectionsEducation?: Flex__<typeof ProfileSectionsEducationBlock>;
  tEducationTile?: Flex__<typeof ProfileTileEducationTile>;
};

export interface DefaultProfileSectionsUnderConstructionDisplayProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsUnderConstructionDisplay__RenderFunc(props: {
  variants: PlasmicProfileSectionsUnderConstructionDisplay__VariantsArgs;
  args: PlasmicProfileSectionsUnderConstructionDisplay__ArgsType;
  overrides: PlasmicProfileSectionsUnderConstructionDisplay__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "tEducationTile.degreeNameInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "tEducationTile.locationInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "tEducationTile.startDateInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "tEducationTile.endDateInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "tEducationTile.graduatedCheckboxValue",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "tEducationTile.deleteButtonClickStage",
        type: "private",
        variableType: "number",
        initFunc: ({ $props, $state, $queries, $ctx }) => 0
      },
      {
        path: "tEducationTile.deleteButtonDisabled",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "tEducationTile.educationalInstitutionInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "pSectionsEducation.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"formattingContainer"}
      data-plasmic-override={overrides.formattingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formattingContainer
      )}
    >
      <div
        className={classNames(
          projectcss.all,
          projectcss.__wab_text,
          sty.text__qqJ8Z
        )}
      >
        {
          "This user's profile is still under construction!\nTry coming back tomorrow."
        }
      </div>
      <div
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        className={classNames(projectcss.all, sty.freeBox)}
      >
        <section className={classNames(projectcss.all, sty.section__gcK74)}>
          <GradCapIcon
            className={classNames(projectcss.all, sty.svg__xIfa)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__taHij
            )}
          >
            {"Education"}
          </div>
        </section>
        {false ? (
          <section className={classNames(projectcss.all, sty.section__aLKpR)}>
            <BuildingIcon
              className={classNames(projectcss.all, sty.svg__ju8Cr)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__m3Q9P
              )}
            >
              {"Experience"}
            </div>
          </section>
        ) : null}
        {false ? (
          <section className={classNames(projectcss.all, sty.section__oLbqw)}>
            <PublicationBookIcon
              className={classNames(projectcss.all, sty.svg__kbP4J)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__wVMnu
              )}
            >
              {"Publication"}
            </div>
          </section>
        ) : null}
      </div>
      <ProfileSectionsEducationBlock
        data-plasmic-name={"pSectionsEducation"}
        data-plasmic-override={overrides.pSectionsEducation}
        allEducation={generateStateValueProp($state, [
          "pSectionsEducation",
          "allEducation"
        ])}
        className={classNames("__wab_instance", sty.pSectionsEducation)}
        editable={true}
        onAllEducationChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsEducation",
            "allEducation"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <ProfileTileEducationTile
        data-plasmic-name={"tEducationTile"}
        data-plasmic-override={overrides.tEducationTile}
        className={classNames("__wab_instance", sty.tEducationTile)}
        degreeNameInputValue={generateStateValueProp($state, [
          "tEducationTile",
          "degreeNameInputValue"
        ])}
        deleteButtonClickStage={generateStateValueProp($state, [
          "tEducationTile",
          "deleteButtonClickStage"
        ])}
        deleteButtonDisabled={generateStateValueProp($state, [
          "tEducationTile",
          "deleteButtonDisabled"
        ])}
        editable={true}
        educationalInstitutionInputValue={generateStateValueProp($state, [
          "tEducationTile",
          "educationalInstitutionInputValue"
        ])}
        endDateInputValue={generateStateValueProp($state, [
          "tEducationTile",
          "endDateInputValue"
        ])}
        graduatedCheckboxValue={generateStateValueProp($state, [
          "tEducationTile",
          "graduatedCheckboxValue"
        ])}
        locationInputValue={generateStateValueProp($state, [
          "tEducationTile",
          "locationInputValue"
        ])}
        onDegreeNameInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "tEducationTile",
            "degreeNameInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDeleteButtonClickStageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "tEducationTile",
            "deleteButtonClickStage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDeleteButtonDisabledChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "tEducationTile",
            "deleteButtonDisabled"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onEducationalInstitutionInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "tEducationTile",
            "educationalInstitutionInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onEndDateInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "tEducationTile",
            "endDateInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onGraduatedCheckboxValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "tEducationTile",
            "graduatedCheckboxValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onLocationInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "tEducationTile",
            "locationInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onStartDateInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "tEducationTile",
            "startDateInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        startDateInputValue={generateStateValueProp($state, [
          "tEducationTile",
          "startDateInputValue"
        ])}
      />

      <BarrierIcon
        className={classNames(projectcss.all, sty.svg__dLUhH)}
        role={"img"}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formattingContainer: [
    "formattingContainer",
    "freeBox",
    "pSectionsEducation",
    "tEducationTile"
  ],
  freeBox: ["freeBox"],
  pSectionsEducation: ["pSectionsEducation"],
  tEducationTile: ["tEducationTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formattingContainer: "div";
  freeBox: "div";
  pSectionsEducation: typeof ProfileSectionsEducationBlock;
  tEducationTile: typeof ProfileTileEducationTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsUnderConstructionDisplay__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsUnderConstructionDisplay__VariantsArgs;
    args?: PlasmicProfileSectionsUnderConstructionDisplay__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsUnderConstructionDisplay__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsUnderConstructionDisplay__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicProfileSectionsUnderConstructionDisplay__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsUnderConstructionDisplay__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsUnderConstructionDisplay__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formattingContainer") {
    func.displayName = "PlasmicProfileSectionsUnderConstructionDisplay";
  } else {
    func.displayName = `PlasmicProfileSectionsUnderConstructionDisplay.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsUnderConstructionDisplay = Object.assign(
  // Top-level PlasmicProfileSectionsUnderConstructionDisplay renders the root element
  makeNodeComponent("formattingContainer"),
  {
    // Helper components rendering sub-elements
    freeBox: makeNodeComponent("freeBox"),
    pSectionsEducation: makeNodeComponent("pSectionsEducation"),
    tEducationTile: makeNodeComponent("tEducationTile"),

    // Metadata about props expected for PlasmicProfileSectionsUnderConstructionDisplay
    internalVariantProps:
      PlasmicProfileSectionsUnderConstructionDisplay__VariantProps,
    internalArgProps: PlasmicProfileSectionsUnderConstructionDisplay__ArgProps
  }
);

export default PlasmicProfileSectionsUnderConstructionDisplay;
/* prettier-ignore-end */
