.formattingContainer {
  box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  position: relative;
  justify-self: flex-start;
  margin-bottom: 12px;
  background: var(--token-1AMvw6c2eIK7);
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
  padding: var(--token-M1l4keX1sfKm);
  border: 1px solid var(--token-5_Q90hFZ9CmK);
}
.text__qqJ8Z {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-AMP1angFPBtf);
  text-align: center;
  margin-left: 30px;
  margin-right: 30px;
  margin-bottom: 30px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.section__gcK74 {
  box-shadow: 0px 2px 20px -2px #8b8b8b33;
  width: 200px;
  height: 200px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
  border: 0.01cm solid #e4e4e7;
}
.svg__xIfa {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
  flex-shrink: 0;
}
.text__taHij {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
}
.section__aLKpR {
  box-shadow: 0px 2px 20px -2px #8b8b8b33;
  position: relative;
  width: 200px;
  height: 200px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: var(--token-sazGmnf7GWAk);
  border: 0.01cm solid #e4e4e7;
}
.svg__ju8Cr {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
  flex-shrink: 0;
}
.text__m3Q9P {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
}
.section__oLbqw {
  box-shadow: 0px 2px 20px -2px #8b8b8b33;
  position: relative;
  width: 200px;
  height: 200px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: var(--token-sazGmnf7GWAk);
  border: 0.01cm solid #e4e4e7;
}
.svg__kbP4J {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
  flex-shrink: 0;
}
.text__wVMnu {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
}
.pSectionsEducation:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.tEducationTile:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.svg__dLUhH {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
  margin-right: 0px;
  flex-shrink: 0;
}
