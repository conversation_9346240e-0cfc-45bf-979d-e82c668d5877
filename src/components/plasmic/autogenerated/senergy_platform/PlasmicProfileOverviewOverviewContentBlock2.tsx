/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: X8jYPMQOFST0

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileOverviewHighlightedWorksLayout from "../../ProfileOverviewHighlightedWorksLayout"; // plasmic-import: Vq_8v2qcFNSB/component
import ProfileOverviewSeeMoreButton from "../../ProfileOverviewSeeMoreButton"; // plasmic-import: UWTH8LMACb3s/component
import ProfileOverviewOverviewBlocks from "../../ProfileOverviewOverviewBlocks"; // plasmic-import: wf5-zG-i1Jzo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileOverviewOverviewContentBlock2.module.css"; // plasmic-import: X8jYPMQOFST0/css

createPlasmicElementProxy;

export type PlasmicProfileOverviewOverviewContentBlock2__VariantMembers = {};
export type PlasmicProfileOverviewOverviewContentBlock2__VariantsArgs = {};
type VariantPropType =
  keyof PlasmicProfileOverviewOverviewContentBlock2__VariantsArgs;
export const PlasmicProfileOverviewOverviewContentBlock2__VariantProps =
  new Array<VariantPropType>();

export type PlasmicProfileOverviewOverviewContentBlock2__ArgsType = {
  children?: React.ReactNode;
  allEducation?: any;
  allExperience?: any;
  allPublications?: any;
  allLicenses?: any;
  allCertifications?: any;
  allPatents?: any;
  allTrademarks?: any;
  onAllEducationChange?: (val: string) => void;
  onAllExperienceChange?: (val: string) => void;
  onAllPublicationsChange?: (val: string) => void;
  onAllLicensesChange?: (val: string) => void;
  onAllCertificationsChange?: (val: string) => void;
  onAllPatentsChange?: (val: string) => void;
  onAllTrademarksChange?: (val: string) => void;
  slot?: React.ReactNode;
};
type ArgPropType = keyof PlasmicProfileOverviewOverviewContentBlock2__ArgsType;
export const PlasmicProfileOverviewOverviewContentBlock2__ArgProps =
  new Array<ArgPropType>(
    "children",
    "allEducation",
    "allExperience",
    "allPublications",
    "allLicenses",
    "allCertifications",
    "allPatents",
    "allTrademarks",
    "onAllEducationChange",
    "onAllExperienceChange",
    "onAllPublicationsChange",
    "onAllLicensesChange",
    "onAllCertificationsChange",
    "onAllPatentsChange",
    "onAllTrademarksChange",
    "slot"
  );

export type PlasmicProfileOverviewOverviewContentBlock2__OverridesType = {
  overviewContentBlock?: Flex__<"div">;
  overviewHeading?: Flex__<typeof ProfileSectionsProfileSectionHeading>;
  text?: Flex__<"div">;
  featuredWorksArea?: Flex__<"div">;
  profileOverviewHighlightedWorksLayout?: Flex__<
    typeof ProfileOverviewHighlightedWorksLayout
  >;
  seeMoreButtonContainer2?: Flex__<"div">;
  summaryArea?: Flex__<"div">;
  personalSummaryHeading?: Flex__<typeof ProfileSectionsProfileSectionHeading>;
  overviewBlocks7?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  overviewBlocks2?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  overviewBlocks5?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  overviewBlocks4?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  overviewBlocks3?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  overviewBlocks6?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  seeMoreButtonContainer?: Flex__<"div">;
};

export interface DefaultProfileOverviewOverviewContentBlock2Props {
  children?: React.ReactNode;
  allEducation?: any;
  allExperience?: any;
  allPublications?: any;
  allLicenses?: any;
  allCertifications?: any;
  allPatents?: any;
  allTrademarks?: any;
  onAllEducationChange?: (val: string) => void;
  onAllExperienceChange?: (val: string) => void;
  onAllPublicationsChange?: (val: string) => void;
  onAllLicensesChange?: (val: string) => void;
  onAllCertificationsChange?: (val: string) => void;
  onAllPatentsChange?: (val: string) => void;
  onAllTrademarksChange?: (val: string) => void;
  slot?: React.ReactNode;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileOverviewOverviewContentBlock2__RenderFunc(props: {
  variants: PlasmicProfileOverviewOverviewContentBlock2__VariantsArgs;
  args: PlasmicProfileOverviewOverviewContentBlock2__ArgsType;
  overrides: PlasmicProfileOverviewOverviewContentBlock2__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "allEducation",
        type: "writable",
        variableType: "object",

        valueProp: "allEducation",
        onChangeProp: "onAllEducationChange"
      },
      {
        path: "overviewBlocks7.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks2.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allEducation;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks5.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks3.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allEducation;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks4.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allEducation;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks6.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks7.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks2.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allExperience;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks5.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks3.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allExperience;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks4.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allExperience;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks6.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "allExperience",
        type: "writable",
        variableType: "object",

        valueProp: "allExperience",
        onChangeProp: "onAllExperienceChange"
      },
      {
        path: "allPublications",
        type: "writable",
        variableType: "object",

        valueProp: "allPublications",
        onChangeProp: "onAllPublicationsChange"
      },
      {
        path: "allLicenses",
        type: "writable",
        variableType: "object",

        valueProp: "allLicenses",
        onChangeProp: "onAllLicensesChange"
      },
      {
        path: "allCertifications",
        type: "writable",
        variableType: "object",

        valueProp: "allCertifications",
        onChangeProp: "onAllCertificationsChange"
      },
      {
        path: "allPatents",
        type: "writable",
        variableType: "object",

        valueProp: "allPatents",
        onChangeProp: "onAllPatentsChange"
      },
      {
        path: "allTrademarks",
        type: "writable",
        variableType: "object",

        valueProp: "allTrademarks",
        onChangeProp: "onAllTrademarksChange"
      },
      {
        path: "overviewBlocks7.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks2.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPublications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks5.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks3.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPublications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks4.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPublications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks6.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks7.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks2.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPatents;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks5.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks3.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPatents;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks4.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPatents;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks6.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks7.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks2.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allTrademarks;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks5.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks3.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allTrademarks;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks4.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allTrademarks;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks6.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks7.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks2.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allCertifications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks5.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks3.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allCertifications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks4.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allCertifications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks6.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks7.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks2.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allLicenses;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks5.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "overviewBlocks3.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allLicenses;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks4.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allLicenses;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "overviewBlocks6.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"overviewContentBlock"}
      data-plasmic-override={overrides.overviewContentBlock}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.overviewContentBlock
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"overviewHeading"}
        data-plasmic-override={overrides.overviewHeading}
        className={classNames("__wab_instance", sty.overviewHeading)}
        noUnderline={true}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text
          )}
        >
          {"Overview"}
        </div>
      </ProfileSectionsProfileSectionHeading>
      <div
        data-plasmic-name={"featuredWorksArea"}
        data-plasmic-override={overrides.featuredWorksArea}
        className={classNames(projectcss.all, sty.featuredWorksArea)}
      >
        <ProfileOverviewHighlightedWorksLayout
          data-plasmic-name={"profileOverviewHighlightedWorksLayout"}
          data-plasmic-override={
            overrides.profileOverviewHighlightedWorksLayout
          }
          className={classNames(
            "__wab_instance",
            sty.profileOverviewHighlightedWorksLayout
          )}
        />

        <div
          data-plasmic-name={"seeMoreButtonContainer2"}
          data-plasmic-override={overrides.seeMoreButtonContainer2}
          className={classNames(projectcss.all, sty.seeMoreButtonContainer2)}
        >
          {renderPlasmicSlot({
            defaultContents: (
              <ProfileOverviewSeeMoreButton
                className={classNames(
                  "__wab_instance",
                  sty.profileOverviewSeeMoreButton___8Im90
                )}
              />
            ),

            value: args.children
          })}
        </div>
      </div>
      <div
        data-plasmic-name={"summaryArea"}
        data-plasmic-override={overrides.summaryArea}
        className={classNames(projectcss.all, sty.summaryArea)}
      >
        <ProfileSectionsProfileSectionHeading
          data-plasmic-name={"personalSummaryHeading"}
          data-plasmic-override={overrides.personalSummaryHeading}
          className={classNames("__wab_instance", sty.personalSummaryHeading)}
        >
          {"About Me"}
        </ProfileSectionsProfileSectionHeading>
        <div className={classNames(projectcss.all, sty.freeBox___1WYsA)}>
          <ProfileOverviewOverviewBlocks
            data-plasmic-name={"overviewBlocks7"}
            data-plasmic-override={overrides.overviewBlocks7}
            allCertifications={generateStateValueProp($state, [
              "overviewBlocks7",
              "allCertifications"
            ])}
            allEducation={generateStateValueProp($state, [
              "overviewBlocks7",
              "allEducation"
            ])}
            allExperience={generateStateValueProp($state, [
              "overviewBlocks7",
              "allExperience"
            ])}
            allLicenses={generateStateValueProp($state, [
              "overviewBlocks7",
              "allLicenses"
            ])}
            allPatents={generateStateValueProp($state, [
              "overviewBlocks7",
              "allPatents"
            ])}
            allPublications={generateStateValueProp($state, [
              "overviewBlocks7",
              "allPublications"
            ])}
            allTrademarks={generateStateValueProp($state, [
              "overviewBlocks7",
              "allTrademarks"
            ])}
            className={classNames("__wab_instance", sty.overviewBlocks7)}
            onAllCertificationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks7",
                "allCertifications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllEducationChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks7",
                "allEducation"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllExperienceChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks7",
                "allExperience"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllLicensesChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks7",
                "allLicenses"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPatentsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks7",
                "allPatents"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPublicationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks7",
                "allPublications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllTrademarksChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks7",
                "allTrademarks"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            sectionContent={"introduction"}
          />
        </div>
        <div className={classNames(projectcss.all, sty.freeBox__k92Qj)}>
          <ProfileOverviewOverviewBlocks
            data-plasmic-name={"overviewBlocks2"}
            data-plasmic-override={overrides.overviewBlocks2}
            allCertifications={generateStateValueProp($state, [
              "overviewBlocks2",
              "allCertifications"
            ])}
            allEducation={generateStateValueProp($state, [
              "overviewBlocks2",
              "allEducation"
            ])}
            allExperience={generateStateValueProp($state, [
              "overviewBlocks2",
              "allExperience"
            ])}
            allLicenses={generateStateValueProp($state, [
              "overviewBlocks2",
              "allLicenses"
            ])}
            allPatents={generateStateValueProp($state, [
              "overviewBlocks2",
              "allPatents"
            ])}
            allPublications={generateStateValueProp($state, [
              "overviewBlocks2",
              "allPublications"
            ])}
            allTrademarks={generateStateValueProp($state, [
              "overviewBlocks2",
              "allTrademarks"
            ])}
            className={classNames("__wab_instance", sty.overviewBlocks2)}
            onAllCertificationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks2",
                "allCertifications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllEducationChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks2",
                "allEducation"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllExperienceChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks2",
                "allExperience"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllLicensesChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks2",
                "allLicenses"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPatentsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks2",
                "allPatents"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPublicationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks2",
                "allPublications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllTrademarksChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks2",
                "allTrademarks"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            sectionContent={"experience"}
          />

          <ProfileOverviewOverviewBlocks
            data-plasmic-name={"overviewBlocks5"}
            data-plasmic-override={overrides.overviewBlocks5}
            allCertifications={generateStateValueProp($state, [
              "overviewBlocks5",
              "allCertifications"
            ])}
            allEducation={generateStateValueProp($state, [
              "overviewBlocks5",
              "allEducation"
            ])}
            allExperience={generateStateValueProp($state, [
              "overviewBlocks5",
              "allExperience"
            ])}
            allLicenses={generateStateValueProp($state, [
              "overviewBlocks5",
              "allLicenses"
            ])}
            allPatents={generateStateValueProp($state, [
              "overviewBlocks5",
              "allPatents"
            ])}
            allPublications={generateStateValueProp($state, [
              "overviewBlocks5",
              "allPublications"
            ])}
            allTrademarks={generateStateValueProp($state, [
              "overviewBlocks5",
              "allTrademarks"
            ])}
            className={classNames("__wab_instance", sty.overviewBlocks5)}
            comingSoon={true}
            onAllCertificationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks5",
                "allCertifications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllEducationChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks5",
                "allEducation"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllExperienceChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks5",
                "allExperience"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllLicensesChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks5",
                "allLicenses"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPatentsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks5",
                "allPatents"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPublicationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks5",
                "allPublications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllTrademarksChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks5",
                "allTrademarks"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            sectionContent={"skills"}
          />
        </div>
        <div className={classNames(projectcss.all, sty.freeBox___9AGm5)}>
          <ProfileOverviewOverviewBlocks
            data-plasmic-name={"overviewBlocks4"}
            data-plasmic-override={overrides.overviewBlocks4}
            allCertifications={generateStateValueProp($state, [
              "overviewBlocks4",
              "allCertifications"
            ])}
            allEducation={generateStateValueProp($state, [
              "overviewBlocks4",
              "allEducation"
            ])}
            allExperience={generateStateValueProp($state, [
              "overviewBlocks4",
              "allExperience"
            ])}
            allLicenses={generateStateValueProp($state, [
              "overviewBlocks4",
              "allLicenses"
            ])}
            allPatents={generateStateValueProp($state, [
              "overviewBlocks4",
              "allPatents"
            ])}
            allPublications={generateStateValueProp($state, [
              "overviewBlocks4",
              "allPublications"
            ])}
            allTrademarks={generateStateValueProp($state, [
              "overviewBlocks4",
              "allTrademarks"
            ])}
            className={classNames("__wab_instance", sty.overviewBlocks4)}
            onAllCertificationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks4",
                "allCertifications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllEducationChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks4",
                "allEducation"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllExperienceChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks4",
                "allExperience"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllLicensesChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks4",
                "allLicenses"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPatentsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks4",
                "allPatents"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPublicationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks4",
                "allPublications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllTrademarksChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks4",
                "allTrademarks"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            sectionContent={"licenses"}
          />

          <ProfileOverviewOverviewBlocks
            data-plasmic-name={"overviewBlocks3"}
            data-plasmic-override={overrides.overviewBlocks3}
            allCertifications={generateStateValueProp($state, [
              "overviewBlocks3",
              "allCertifications"
            ])}
            allEducation={generateStateValueProp($state, [
              "overviewBlocks3",
              "allEducation"
            ])}
            allExperience={generateStateValueProp($state, [
              "overviewBlocks3",
              "allExperience"
            ])}
            allLicenses={generateStateValueProp($state, [
              "overviewBlocks3",
              "allLicenses"
            ])}
            allPatents={generateStateValueProp($state, [
              "overviewBlocks3",
              "allPatents"
            ])}
            allPublications={generateStateValueProp($state, [
              "overviewBlocks3",
              "allPublications"
            ])}
            allTrademarks={generateStateValueProp($state, [
              "overviewBlocks3",
              "allTrademarks"
            ])}
            className={classNames("__wab_instance", sty.overviewBlocks3)}
            onAllCertificationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks3",
                "allCertifications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllEducationChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks3",
                "allEducation"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllExperienceChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks3",
                "allExperience"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllLicensesChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks3",
                "allLicenses"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPatentsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks3",
                "allPatents"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPublicationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks3",
                "allPublications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllTrademarksChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks3",
                "allTrademarks"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            sectionContent={"publications"}
          />

          <ProfileOverviewOverviewBlocks
            data-plasmic-name={"overviewBlocks6"}
            data-plasmic-override={overrides.overviewBlocks6}
            allCertifications={generateStateValueProp($state, [
              "overviewBlocks6",
              "allCertifications"
            ])}
            allEducation={generateStateValueProp($state, [
              "overviewBlocks6",
              "allEducation"
            ])}
            allExperience={generateStateValueProp($state, [
              "overviewBlocks6",
              "allExperience"
            ])}
            allLicenses={generateStateValueProp($state, [
              "overviewBlocks6",
              "allLicenses"
            ])}
            allPatents={generateStateValueProp($state, [
              "overviewBlocks6",
              "allPatents"
            ])}
            allPublications={generateStateValueProp($state, [
              "overviewBlocks6",
              "allPublications"
            ])}
            allTrademarks={generateStateValueProp($state, [
              "overviewBlocks6",
              "allTrademarks"
            ])}
            className={classNames("__wab_instance", sty.overviewBlocks6)}
            comingSoon={true}
            onAllCertificationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks6",
                "allCertifications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllEducationChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks6",
                "allEducation"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllExperienceChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks6",
                "allExperience"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllLicensesChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks6",
                "allLicenses"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPatentsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks6",
                "allPatents"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPublicationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks6",
                "allPublications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllTrademarksChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "overviewBlocks6",
                "allTrademarks"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            sectionContent={"tools"}
          />
        </div>
        <div
          data-plasmic-name={"seeMoreButtonContainer"}
          data-plasmic-override={overrides.seeMoreButtonContainer}
          className={classNames(projectcss.all, sty.seeMoreButtonContainer)}
        >
          {renderPlasmicSlot({
            defaultContents: (
              <ProfileOverviewSeeMoreButton
                className={classNames(
                  "__wab_instance",
                  sty.profileOverviewSeeMoreButton__v1DGe
                )}
              />
            ),

            value: args.slot
          })}
        </div>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  overviewContentBlock: [
    "overviewContentBlock",
    "overviewHeading",
    "text",
    "featuredWorksArea",
    "profileOverviewHighlightedWorksLayout",
    "seeMoreButtonContainer2",
    "summaryArea",
    "personalSummaryHeading",
    "overviewBlocks7",
    "overviewBlocks2",
    "overviewBlocks5",
    "overviewBlocks4",
    "overviewBlocks3",
    "overviewBlocks6",
    "seeMoreButtonContainer"
  ],
  overviewHeading: ["overviewHeading", "text"],
  text: ["text"],
  featuredWorksArea: [
    "featuredWorksArea",
    "profileOverviewHighlightedWorksLayout",
    "seeMoreButtonContainer2"
  ],
  profileOverviewHighlightedWorksLayout: [
    "profileOverviewHighlightedWorksLayout"
  ],
  seeMoreButtonContainer2: ["seeMoreButtonContainer2"],
  summaryArea: [
    "summaryArea",
    "personalSummaryHeading",
    "overviewBlocks7",
    "overviewBlocks2",
    "overviewBlocks5",
    "overviewBlocks4",
    "overviewBlocks3",
    "overviewBlocks6",
    "seeMoreButtonContainer"
  ],
  personalSummaryHeading: ["personalSummaryHeading"],
  overviewBlocks7: ["overviewBlocks7"],
  overviewBlocks2: ["overviewBlocks2"],
  overviewBlocks5: ["overviewBlocks5"],
  overviewBlocks4: ["overviewBlocks4"],
  overviewBlocks3: ["overviewBlocks3"],
  overviewBlocks6: ["overviewBlocks6"],
  seeMoreButtonContainer: ["seeMoreButtonContainer"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  overviewContentBlock: "div";
  overviewHeading: typeof ProfileSectionsProfileSectionHeading;
  text: "div";
  featuredWorksArea: "div";
  profileOverviewHighlightedWorksLayout: typeof ProfileOverviewHighlightedWorksLayout;
  seeMoreButtonContainer2: "div";
  summaryArea: "div";
  personalSummaryHeading: typeof ProfileSectionsProfileSectionHeading;
  overviewBlocks7: typeof ProfileOverviewOverviewBlocks;
  overviewBlocks2: typeof ProfileOverviewOverviewBlocks;
  overviewBlocks5: typeof ProfileOverviewOverviewBlocks;
  overviewBlocks4: typeof ProfileOverviewOverviewBlocks;
  overviewBlocks3: typeof ProfileOverviewOverviewBlocks;
  overviewBlocks6: typeof ProfileOverviewOverviewBlocks;
  seeMoreButtonContainer: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileOverviewOverviewContentBlock2__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileOverviewOverviewContentBlock2__VariantsArgs;
    args?: PlasmicProfileOverviewOverviewContentBlock2__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileOverviewOverviewContentBlock2__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileOverviewOverviewContentBlock2__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicProfileOverviewOverviewContentBlock2__ArgProps,
          internalVariantPropNames:
            PlasmicProfileOverviewOverviewContentBlock2__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileOverviewOverviewContentBlock2__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "overviewContentBlock") {
    func.displayName = "PlasmicProfileOverviewOverviewContentBlock2";
  } else {
    func.displayName = `PlasmicProfileOverviewOverviewContentBlock2.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileOverviewOverviewContentBlock2 = Object.assign(
  // Top-level PlasmicProfileOverviewOverviewContentBlock2 renders the root element
  makeNodeComponent("overviewContentBlock"),
  {
    // Helper components rendering sub-elements
    overviewHeading: makeNodeComponent("overviewHeading"),
    text: makeNodeComponent("text"),
    featuredWorksArea: makeNodeComponent("featuredWorksArea"),
    profileOverviewHighlightedWorksLayout: makeNodeComponent(
      "profileOverviewHighlightedWorksLayout"
    ),
    seeMoreButtonContainer2: makeNodeComponent("seeMoreButtonContainer2"),
    summaryArea: makeNodeComponent("summaryArea"),
    personalSummaryHeading: makeNodeComponent("personalSummaryHeading"),
    overviewBlocks7: makeNodeComponent("overviewBlocks7"),
    overviewBlocks2: makeNodeComponent("overviewBlocks2"),
    overviewBlocks5: makeNodeComponent("overviewBlocks5"),
    overviewBlocks4: makeNodeComponent("overviewBlocks4"),
    overviewBlocks3: makeNodeComponent("overviewBlocks3"),
    overviewBlocks6: makeNodeComponent("overviewBlocks6"),
    seeMoreButtonContainer: makeNodeComponent("seeMoreButtonContainer"),

    // Metadata about props expected for PlasmicProfileOverviewOverviewContentBlock2
    internalVariantProps:
      PlasmicProfileOverviewOverviewContentBlock2__VariantProps,
    internalArgProps: PlasmicProfileOverviewOverviewContentBlock2__ArgProps
  }
);

export default PlasmicProfileOverviewOverviewContentBlock2;
/* prettier-ignore-end */
