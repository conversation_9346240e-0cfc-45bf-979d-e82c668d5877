.langEditMod {
  box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  display: inline-flex;
  flex-direction: column;
  background: linear-gradient(0deg, #ededed8c 0%, #ededed0d 100%);
  width: auto;
  height: auto;
  backdrop-filter: blur(3px);
  column-gap: 0px;
  position: relative;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: nowrap;
  align-content: stretch;
  justify-self: flex-start;
  row-gap: var(--token-j0qnbpah5w9U);
  -webkit-backdrop-filter: blur(3px);
  padding: var(--token-j0qnbpah5w9U);
  border: 1px solid var(--token-1AMvw6c2eIK7);
}
.section__rgbpm {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  column-gap: var(--token-8HwyO4mnTWdf);
  padding: var(--token-sazGmnf7GWAk);
}
.svg__i7SFc {
  object-fit: cover;
  max-width: 100%;
  height: 1em;
}
.textInput:global(.__wab_instance) {
  width: 350px;
  flex-shrink: 0;
}
.svg__j6C3R {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg___4JfSj {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.subCheckbox:global(.__wab_instance) {
  max-width: 100%;
}
.section___0Sdm6 {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  column-gap: var(--token-8HwyO4mnTWdf);
  padding: var(--token-sazGmnf7GWAk);
}
.svg__vOhgp {
  object-fit: cover;
  max-width: 100%;
  height: 1em;
}
.textInput2:global(.__wab_instance) {
  width: 350px;
  flex-shrink: 0;
}
.svg___3RUa {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__jZu4M {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.section__ydPtq {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  column-gap: var(--token-8HwyO4mnTWdf);
  padding: var(--token-sazGmnf7GWAk);
}
.svg__dwf1S {
  object-fit: cover;
  max-width: 100%;
  height: 1em;
}
.textInput3:global(.__wab_instance) {
  width: 350px;
  flex-shrink: 0;
}
.svg__f2Agy {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__pXx6V {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.section__pIVrK {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  column-gap: var(--token-8HwyO4mnTWdf);
  padding: var(--token-sazGmnf7GWAk);
}
.svg___4YdcF {
  object-fit: cover;
  max-width: 100%;
  height: 1em;
}
.textInput4:global(.__wab_instance) {
  width: 350px;
  flex-shrink: 0;
}
.svg__jUqst {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__mtzjk {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
