.patentSpacingContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  flex-shrink: 0;
  height: auto;
  margin-bottom: 12px;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  margin-top: 4px;
  margin-left: 4px;
  row-gap: 0px;
  position: relative;
  min-width: 0;
  padding: 16px 16px 8px 0px;
}
.patentSpacingContaineroverview {
  width: auto;
  justify-self: flex-start;
  display: inline-flex;
}
.patentIcon {
  margin-top: 18px;
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: var(--token-ey2y7HI9U2zx);
  height: var(--token-ey2y7HI9U2zx);
  column-gap: 0px;
  position: relative;
  object-fit: cover;
  max-width: 100%;
  flex-shrink: 0;
}
.patentIconoverview {
  display: none;
}
.informationStack {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  margin-bottom: 2px;
  min-width: 0;
}
.titleInput:global(.__wab_instance) {
  max-width: 100%;
}
.infoBar {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: stretch;
  margin-bottom: 2px;
  column-gap: var(--token-rB3BKCgczoWa);
  row-gap: var(--token-ptnlAHOp9Vq0);
}
.infoBareditable {
  justify-content: flex-start;
  align-content: center;
  margin-top: 12px;
  margin-bottom: 8px;
}
.infoBaroverview {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: stretch;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  row-gap: var(--token-ptnlAHOp9Vq0);
}
.registrationNumberInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.registrationNumberInputeditable:global(.__wab_instance) {
  align-self: center;
  display: flex;
}
.registrationNumberInputoverview:global(.__wab_instance) {
  display: none;
}
.iconSpot3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.countryOfRegistration:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.countryOfRegistrationeditable:global(.__wab_instance) {
  align-self: center;
  display: flex;
}
.iconSpot {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.eventDateInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.eventDateInputeditable:global(.__wab_instance) {
  align-self: center;
  display: flex;
}
.eventDateInputoverview:global(.__wab_instance) {
  display: none;
}
.iconSpot2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.statusSelectorInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.statusSelectorInputeditable:global(.__wab_instance) {
  margin-bottom: 12px;
  display: flex;
}
.statusSelectorInputoverview:global(.__wab_instance) {
  display: flex;
}
.iconSpot4 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.patentTypeSelectorInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.patentTypeSelectorInputeditable:global(.__wab_instance) {
  margin-bottom: 12px;
  display: flex;
}
.patentTypeSelectorInputoverview:global(.__wab_instance) {
  display: flex;
}
.iconSpot5 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.description:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.descriptioneditable:global(.__wab_instance) {
  margin-top: 4px;
}
.subDeleteButton:global(.__wab_instance) {
  max-width: 100%;
  position: absolute;
  top: 16px;
  right: 16px;
  flex-shrink: 0;
  display: none;
}
.subDeleteButtoneditable:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
