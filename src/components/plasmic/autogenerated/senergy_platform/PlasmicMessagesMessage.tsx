/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: J-ppsQZTajkA

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import MessagesAttachments from "../../MessagesAttachments"; // plasmic-import: C1Ttec365eGb/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicMessagesMessage.module.css"; // plasmic-import: J-ppsQZTajkA/css

createPlasmicElementProxy;

export type PlasmicMessagesMessage__VariantMembers = {
  sentReceived: "sent" | "received";
  messageType: "text" | "attachment" | "textAndAttachment";
  chainMessages: "chainText" | "chainAttachment" | "chainTextAndAttachment";
};
export type PlasmicMessagesMessage__VariantsArgs = {
  sentReceived?: SingleChoiceArg<"sent" | "received">;
  messageType?: SingleChoiceArg<"text" | "attachment" | "textAndAttachment">;
  chainMessages?: SingleChoiceArg<
    "chainText" | "chainAttachment" | "chainTextAndAttachment"
  >;
};
type VariantPropType = keyof PlasmicMessagesMessage__VariantsArgs;
export const PlasmicMessagesMessage__VariantProps = new Array<VariantPropType>(
  "sentReceived",
  "messageType",
  "chainMessages"
);

export type PlasmicMessagesMessage__ArgsType = {
  unnamedProp?: React.ReactNode;
  slot2?: React.ReactNode;
};
type ArgPropType = keyof PlasmicMessagesMessage__ArgsType;
export const PlasmicMessagesMessage__ArgProps = new Array<ArgPropType>(
  "unnamedProp",
  "slot2"
);

export type PlasmicMessagesMessage__OverridesType = {
  messageContainer?: Flex__<"div">;
  img?: Flex__<typeof PlasmicImg__>;
  messageContent?: Flex__<"div">;
  formattingStack?: Flex__<"div">;
  timeAndName?: Flex__<"section">;
  messageBody?: Flex__<"div">;
  messageBody2?: Flex__<"div">;
  attachmentSlotFormatting?: Flex__<"section">;
};

export interface DefaultMessagesMessageProps {
  unnamedProp?: React.ReactNode;
  slot2?: React.ReactNode;
  sentReceived?: SingleChoiceArg<"sent" | "received">;
  messageType?: SingleChoiceArg<"text" | "attachment" | "textAndAttachment">;
  chainMessages?: SingleChoiceArg<
    "chainText" | "chainAttachment" | "chainTextAndAttachment"
  >;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicMessagesMessage__RenderFunc(props: {
  variants: PlasmicMessagesMessage__VariantsArgs;
  args: PlasmicMessagesMessage__ArgsType;
  overrides: PlasmicMessagesMessage__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "sentReceived",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.sentReceived
      },
      {
        path: "messageType",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.messageType
      },
      {
        path: "chainMessages",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.chainMessages
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"messageContainer"}
      data-plasmic-override={overrides.messageContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.messageContainer,
        {
          [sty.messageContainerchainMessages_chainAttachment]: hasVariant(
            $state,
            "chainMessages",
            "chainAttachment"
          ),
          [sty.messageContainerchainMessages_chainTextAndAttachment]:
            hasVariant($state, "chainMessages", "chainTextAndAttachment"),
          [sty.messageContainerchainMessages_chainText]: hasVariant(
            $state,
            "chainMessages",
            "chainText"
          ),
          [sty.messageContainermessageType_attachment]: hasVariant(
            $state,
            "messageType",
            "attachment"
          ),
          [sty.messageContainermessageType_textAndAttachment]: hasVariant(
            $state,
            "messageType",
            "textAndAttachment"
          ),
          [sty.messageContainersentReceived_received]: hasVariant(
            $state,
            "sentReceived",
            "received"
          ),
          [sty.messageContainersentReceived_received_chainMessages_chainText]:
            hasVariant($state, "sentReceived", "received") &&
            hasVariant($state, "chainMessages", "chainText"),
          [sty.messageContainersentReceived_sent]: hasVariant(
            $state,
            "sentReceived",
            "sent"
          ),
          [sty.messageContainersentReceived_sent_chainMessages_chainAttachment_messageType_attachment]:
            hasVariant($state, "sentReceived", "sent") &&
            hasVariant($state, "messageType", "attachment") &&
            hasVariant($state, "chainMessages", "chainAttachment"),
          [sty.messageContainersentReceived_sent_chainMessages_chainTextAndAttachment]:
            hasVariant($state, "sentReceived", "sent") &&
            hasVariant($state, "chainMessages", "chainTextAndAttachment")
        }
      )}
    >
      <PlasmicImg__
        data-plasmic-name={"img"}
        data-plasmic-override={overrides.img}
        alt={""}
        className={classNames(sty.img, {
          [sty.imgchainMessages_chainAttachment]: hasVariant(
            $state,
            "chainMessages",
            "chainAttachment"
          ),
          [sty.imgchainMessages_chainTextAndAttachment]: hasVariant(
            $state,
            "chainMessages",
            "chainTextAndAttachment"
          ),
          [sty.imgchainMessages_chainText]: hasVariant(
            $state,
            "chainMessages",
            "chainText"
          ),
          [sty.imgsentReceived_received]: hasVariant(
            $state,
            "sentReceived",
            "received"
          )
        })}
        displayHeight={"40px"}
        displayMaxHeight={"none"}
        displayMaxWidth={"100%"}
        displayMinHeight={"0"}
        displayMinWidth={"0"}
        displayWidth={"40px"}
        loading={"lazy"}
        src={{
          src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
          fullWidth: 800,
          fullHeight: 600,
          aspectRatio: undefined
        }}
      />

      <div
        data-plasmic-name={"messageContent"}
        data-plasmic-override={overrides.messageContent}
        className={classNames(projectcss.all, sty.messageContent, {
          [sty.messageContentsentReceived_received]: hasVariant(
            $state,
            "sentReceived",
            "received"
          ),
          [sty.messageContentsentReceived_received_chainMessages_chainText]:
            hasVariant($state, "sentReceived", "received") &&
            hasVariant($state, "chainMessages", "chainText"),
          [sty.messageContentsentReceived_sent]: hasVariant(
            $state,
            "sentReceived",
            "sent"
          ),
          [sty.messageContentsentReceived_sent_chainMessages_chainAttachment_messageType_attachment]:
            hasVariant($state, "sentReceived", "sent") &&
            hasVariant($state, "messageType", "attachment") &&
            hasVariant($state, "chainMessages", "chainAttachment"),
          [sty.messageContentsentReceived_sent_chainMessages_chainTextAndAttachment]:
            hasVariant($state, "sentReceived", "sent") &&
            hasVariant($state, "chainMessages", "chainTextAndAttachment")
        })}
      >
        <div
          data-plasmic-name={"formattingStack"}
          data-plasmic-override={overrides.formattingStack}
          className={classNames(projectcss.all, sty.formattingStack, {
            [sty.formattingStackchainMessages_chainAttachment]: hasVariant(
              $state,
              "chainMessages",
              "chainAttachment"
            ),
            [sty.formattingStackchainMessages_chainTextAndAttachment]:
              hasVariant($state, "chainMessages", "chainTextAndAttachment"),
            [sty.formattingStackchainMessages_chainText]: hasVariant(
              $state,
              "chainMessages",
              "chainText"
            ),
            [sty.formattingStackmessageType_attachment]: hasVariant(
              $state,
              "messageType",
              "attachment"
            ),
            [sty.formattingStackmessageType_textAndAttachment]: hasVariant(
              $state,
              "messageType",
              "textAndAttachment"
            ),
            [sty.formattingStackmessageType_text]: hasVariant(
              $state,
              "messageType",
              "text"
            ),
            [sty.formattingStacksentReceived_received]: hasVariant(
              $state,
              "sentReceived",
              "received"
            ),
            [sty.formattingStacksentReceived_received_chainMessages_chainText]:
              hasVariant($state, "sentReceived", "received") &&
              hasVariant($state, "chainMessages", "chainText"),
            [sty.formattingStacksentReceived_sent_chainMessages_chainAttachment_messageType_attachment]:
              hasVariant($state, "sentReceived", "sent") &&
              hasVariant($state, "messageType", "attachment") &&
              hasVariant($state, "chainMessages", "chainAttachment"),
            [sty.formattingStacksentReceived_sent_chainMessages_chainTextAndAttachment]:
              hasVariant($state, "sentReceived", "sent") &&
              hasVariant($state, "chainMessages", "chainTextAndAttachment")
          })}
        >
          <section
            data-plasmic-name={"timeAndName"}
            data-plasmic-override={overrides.timeAndName}
            className={classNames(projectcss.all, sty.timeAndName, {
              [sty.timeAndNamechainMessages_chainAttachment]: hasVariant(
                $state,
                "chainMessages",
                "chainAttachment"
              ),
              [sty.timeAndNamechainMessages_chainTextAndAttachment]: hasVariant(
                $state,
                "chainMessages",
                "chainTextAndAttachment"
              ),
              [sty.timeAndNamechainMessages_chainText]: hasVariant(
                $state,
                "chainMessages",
                "chainText"
              ),
              [sty.timeAndNamemessageType_attachment]: hasVariant(
                $state,
                "messageType",
                "attachment"
              ),
              [sty.timeAndNamesentReceived_sent_chainMessages_chainAttachment_messageType_attachment]:
                hasVariant($state, "sentReceived", "sent") &&
                hasVariant($state, "messageType", "attachment") &&
                hasVariant($state, "chainMessages", "chainAttachment")
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__fHFnz,
                {
                  [sty.textchainMessages_chainAttachment__fHFnziRcWb]:
                    hasVariant($state, "chainMessages", "chainAttachment"),
                  [sty.textchainMessages_chainTextAndAttachment__fHFnzszblr]:
                    hasVariant(
                      $state,
                      "chainMessages",
                      "chainTextAndAttachment"
                    ),
                  [sty.textchainMessages_chainText__fHFnzGeCe8]: hasVariant(
                    $state,
                    "chainMessages",
                    "chainText"
                  ),
                  [sty.textmessageType_attachment__fHFnzobfOf]: hasVariant(
                    $state,
                    "messageType",
                    "attachment"
                  ),
                  [sty.textsentReceived_received__fHFnzGnXii]: hasVariant(
                    $state,
                    "sentReceived",
                    "received"
                  )
                }
              )}
            >
              {"Firstname Lastname"}
            </div>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__mSb9J,
                {
                  [sty.textmessageType_attachment__mSb9JobfOf]: hasVariant(
                    $state,
                    "messageType",
                    "attachment"
                  ),
                  [sty.textsentReceived_sent__mSb9Jas4Ki]: hasVariant(
                    $state,
                    "sentReceived",
                    "sent"
                  )
                }
              )}
            >
              {"12:00 PM"}
            </div>
          </section>
          <div
            data-plasmic-name={"messageBody"}
            data-plasmic-override={overrides.messageBody}
            className={classNames(projectcss.all, sty.messageBody, {
              [sty.messageBodychainMessages_chainAttachment]: hasVariant(
                $state,
                "chainMessages",
                "chainAttachment"
              ),
              [sty.messageBodychainMessages_chainTextAndAttachment]: hasVariant(
                $state,
                "chainMessages",
                "chainTextAndAttachment"
              ),
              [sty.messageBodychainMessages_chainText]: hasVariant(
                $state,
                "chainMessages",
                "chainText"
              ),
              [sty.messageBodymessageType_attachment]: hasVariant(
                $state,
                "messageType",
                "attachment"
              ),
              [sty.messageBodymessageType_textAndAttachment]: hasVariant(
                $state,
                "messageType",
                "textAndAttachment"
              ),
              [sty.messageBodysentReceived_received_chainMessages_chainText]:
                hasVariant($state, "sentReceived", "received") &&
                hasVariant($state, "chainMessages", "chainText"),
              [sty.messageBodysentReceived_sent]: hasVariant(
                $state,
                "sentReceived",
                "sent"
              ),
              [sty.messageBodysentReceived_sent_chainMessages_chainTextAndAttachment]:
                hasVariant($state, "sentReceived", "sent") &&
                hasVariant($state, "chainMessages", "chainTextAndAttachment")
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__wmGFp,
                {
                  [sty.textmessageType_textAndAttachment__wmGFpvL2X]:
                    hasVariant($state, "messageType", "textAndAttachment"),
                  [sty.textmessageType_text__wmGFpBfjF]: hasVariant(
                    $state,
                    "messageType",
                    "text"
                  )
                }
              )}
            >
              {"This is the content of the message"}
            </div>
          </div>
          {(
            hasVariant($state, "chainMessages", "chainTextAndAttachment")
              ? true
              : hasVariant($state, "chainMessages", "chainAttachment")
              ? true
              : hasVariant($state, "chainMessages", "chainText")
              ? true
              : hasVariant($state, "messageType", "textAndAttachment")
              ? true
              : hasVariant($state, "messageType", "attachment")
              ? true
              : false
          ) ? (
            <div
              data-plasmic-name={"messageBody2"}
              data-plasmic-override={overrides.messageBody2}
              className={classNames(projectcss.all, sty.messageBody2, {
                [sty.messageBody2chainMessages_chainAttachment]: hasVariant(
                  $state,
                  "chainMessages",
                  "chainAttachment"
                ),
                [sty.messageBody2chainMessages_chainTextAndAttachment]:
                  hasVariant($state, "chainMessages", "chainTextAndAttachment"),
                [sty.messageBody2chainMessages_chainText]: hasVariant(
                  $state,
                  "chainMessages",
                  "chainText"
                ),
                [sty.messageBody2messageType_attachment]: hasVariant(
                  $state,
                  "messageType",
                  "attachment"
                ),
                [sty.messageBody2messageType_textAndAttachment]: hasVariant(
                  $state,
                  "messageType",
                  "textAndAttachment"
                ),
                [sty.messageBody2sentReceived_sent]: hasVariant(
                  $state,
                  "sentReceived",
                  "sent"
                ),
                [sty.messageBody2sentReceived_sent_chainMessages_chainAttachment_messageType_attachment]:
                  hasVariant($state, "sentReceived", "sent") &&
                  hasVariant($state, "messageType", "attachment") &&
                  hasVariant($state, "chainMessages", "chainAttachment"),
                [sty.messageBody2sentReceived_sent_chainMessages_chainTextAndAttachment]:
                  hasVariant($state, "sentReceived", "sent") &&
                  hasVariant($state, "chainMessages", "chainTextAndAttachment")
              })}
            >
              {renderPlasmicSlot({
                defaultContents: (
                  <MessagesAttachments
                    className={classNames(
                      "__wab_instance",
                      sty.messagesAttachments__ldNrC
                    )}
                  />
                ),

                value: args.slot2
              })}
            </div>
          ) : null}
        </div>
        <section
          data-plasmic-name={"attachmentSlotFormatting"}
          data-plasmic-override={overrides.attachmentSlotFormatting}
          className={classNames(projectcss.all, sty.attachmentSlotFormatting)}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__iMuIi
            )}
          >
            {"Filename Sent"}
          </div>
          {renderPlasmicSlot({
            defaultContents: (
              <MessagesAttachments
                className={classNames(
                  "__wab_instance",
                  sty.messagesAttachments__sEmIx
                )}
              />
            ),

            value: args.unnamedProp
          })}
        </section>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  messageContainer: [
    "messageContainer",
    "img",
    "messageContent",
    "formattingStack",
    "timeAndName",
    "messageBody",
    "messageBody2",
    "attachmentSlotFormatting"
  ],
  img: ["img"],
  messageContent: [
    "messageContent",
    "formattingStack",
    "timeAndName",
    "messageBody",
    "messageBody2",
    "attachmentSlotFormatting"
  ],
  formattingStack: [
    "formattingStack",
    "timeAndName",
    "messageBody",
    "messageBody2"
  ],
  timeAndName: ["timeAndName"],
  messageBody: ["messageBody"],
  messageBody2: ["messageBody2"],
  attachmentSlotFormatting: ["attachmentSlotFormatting"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  messageContainer: "div";
  img: typeof PlasmicImg__;
  messageContent: "div";
  formattingStack: "div";
  timeAndName: "section";
  messageBody: "div";
  messageBody2: "div";
  attachmentSlotFormatting: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicMessagesMessage__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicMessagesMessage__VariantsArgs;
    args?: PlasmicMessagesMessage__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicMessagesMessage__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicMessagesMessage__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicMessagesMessage__ArgProps,
          internalVariantPropNames: PlasmicMessagesMessage__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicMessagesMessage__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "messageContainer") {
    func.displayName = "PlasmicMessagesMessage";
  } else {
    func.displayName = `PlasmicMessagesMessage.${nodeName}`;
  }
  return func;
}

export const PlasmicMessagesMessage = Object.assign(
  // Top-level PlasmicMessagesMessage renders the root element
  makeNodeComponent("messageContainer"),
  {
    // Helper components rendering sub-elements
    img: makeNodeComponent("img"),
    messageContent: makeNodeComponent("messageContent"),
    formattingStack: makeNodeComponent("formattingStack"),
    timeAndName: makeNodeComponent("timeAndName"),
    messageBody: makeNodeComponent("messageBody"),
    messageBody2: makeNodeComponent("messageBody2"),
    attachmentSlotFormatting: makeNodeComponent("attachmentSlotFormatting"),

    // Metadata about props expected for PlasmicMessagesMessage
    internalVariantProps: PlasmicMessagesMessage__VariantProps,
    internalArgProps: PlasmicMessagesMessage__ArgProps
  }
);

export default PlasmicMessagesMessage;
/* prettier-ignore-end */
