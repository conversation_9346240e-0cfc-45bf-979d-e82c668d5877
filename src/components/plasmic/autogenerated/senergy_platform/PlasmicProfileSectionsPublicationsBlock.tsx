/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: zdegYaKxvR34

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTilePublicationTile from "../../ProfileTilePublicationTile"; // plasmic-import: kKAHWOIWX-5u/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsPublicationsBlock.module.css"; // plasmic-import: zdegYaKxvR34/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsPublicationsBlock__VariantMembers = {
  overviewGrid: "overviewGridBlock";
  editable: "editable";
};
export type PlasmicProfileSectionsPublicationsBlock__VariantsArgs = {
  overviewGrid?: SingleChoiceArg<"overviewGridBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType =
  keyof PlasmicProfileSectionsPublicationsBlock__VariantsArgs;
export const PlasmicProfileSectionsPublicationsBlock__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileSectionsPublicationsBlock__ArgsType = {
  allPublications?: any;
  onAllPublicationsChange?: (val: string) => void;
  addButtonOnClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicProfileSectionsPublicationsBlock__ArgsType;
export const PlasmicProfileSectionsPublicationsBlock__ArgProps =
  new Array<ArgPropType>(
    "allPublications",
    "onAllPublicationsChange",
    "addButtonOnClick"
  );

export type PlasmicProfileSectionsPublicationsBlock__OverridesType = {
  publicationsSection?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  pubDisplayContainer?: Flex__<"div">;
  publicationTile?: Flex__<typeof ProfileTilePublicationTile>;
};

export interface DefaultProfileSectionsPublicationsBlockProps {
  allPublications?: any;
  onAllPublicationsChange?: (val: string) => void;
  addButtonOnClick?: (event: any) => void;
  overviewGrid?: SingleChoiceArg<"overviewGridBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsPublicationsBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsPublicationsBlock__VariantsArgs;
  args: PlasmicProfileSectionsPublicationsBlock__ArgsType;
  overrides: PlasmicProfileSectionsPublicationsBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "publicationTile[].titleInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "publicationTile[].publicationDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "publicationTile[].publisherInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "publicationTile[].additionalAuthorsInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "publicationTile[].isbnIssnInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "publicationTile[].deleteButtonClickStage",
        type: "private",
        variableType: "number"
      },
      {
        path: "publicationTile[].deleteButtonDisabled",
        type: "private",
        variableType: "text"
      },
      {
        path: "allPublications",
        type: "writable",
        variableType: "object",

        valueProp: "allPublications",
        onChangeProp: "onAllPublicationsChange"
      },
      {
        path: "publicationTile[].descriptionInputValue",
        type: "private",
        variableType: "text"
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"publicationsSection"}
      data-plasmic-override={overrides.publicationsSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.publicationsSection
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        addButtonBaseOnClick={args.addButtonOnClick}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewGrid_overviewGridBlock]:
              hasVariant($state, "overviewGrid", "overviewGridBlock")
          }
        )}
        editable={
          hasVariant($state, "editable", "editable")
            ? true
            : (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })()
        }
        overviewGrid={
          hasVariant($state, "overviewGrid", "overviewGridBlock")
            ? true
            : undefined
        }
      >
        {"Publications"}
      </ProfileSectionsProfileSectionHeading>
      <div
        data-plasmic-name={"pubDisplayContainer"}
        data-plasmic-override={overrides.pubDisplayContainer}
        className={classNames(projectcss.all, sty.pubDisplayContainer, {
          [sty.pubDisplayContaineroverviewGrid_overviewGridBlock]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGridBlock"
          )
        })}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
          (() => {
            try {
              return $state.allPublications.data;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()
        ).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (() => {
            const child$Props = {
              additionalAuthorsInputValue: generateStateValueProp($state, [
                "publicationTile",
                __plasmic_idx_0,
                "additionalAuthorsInputValue"
              ]),
              className: classNames("__wab_instance", sty.publicationTile, {
                [sty.publicationTileoverviewGrid_overviewGridBlock]: hasVariant(
                  $state,
                  "overviewGrid",
                  "overviewGridBlock"
                )
              }),
              deleteButtonClickStage: generateStateValueProp($state, [
                "publicationTile",
                __plasmic_idx_0,
                "deleteButtonClickStage"
              ]),
              deleteButtonDisabled: generateStateValueProp($state, [
                "publicationTile",
                __plasmic_idx_0,
                "deleteButtonDisabled"
              ]),
              descriptionInputValue: generateStateValueProp($state, [
                "publicationTile",
                __plasmic_idx_0,
                "descriptionInputValue"
              ]),
              editable: (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })(),
              isbnIssnInputValue: generateStateValueProp($state, [
                "publicationTile",
                __plasmic_idx_0,
                "isbnIssnInputValue"
              ]),
              key: currentIndex,
              onAdditionalAuthorsInputValueChange: async (
                ...eventArgs: any
              ) => {
                generateStateOnChangeProp($state, [
                  "publicationTile",
                  __plasmic_idx_0,
                  "additionalAuthorsInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonClickStageChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "publicationTile",
                  __plasmic_idx_0,
                  "deleteButtonClickStage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonDisabledChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "publicationTile",
                  __plasmic_idx_0,
                  "deleteButtonDisabled"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDescriptionInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "publicationTile",
                  __plasmic_idx_0,
                  "descriptionInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onIsbnIssnInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "publicationTile",
                  __plasmic_idx_0,
                  "isbnIssnInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onPublicationDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "publicationTile",
                  __plasmic_idx_0,
                  "publicationDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onPublisherInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "publicationTile",
                  __plasmic_idx_0,
                  "publisherInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onTitleInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "publicationTile",
                  __plasmic_idx_0,
                  "titleInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              overview: hasVariant($state, "overviewGrid", "overviewGridBlock")
                ? true
                : undefined,
              publicationDateInputValue: generateStateValueProp($state, [
                "publicationTile",
                __plasmic_idx_0,
                "publicationDateInputValue"
              ]),
              publicationId: (() => {
                try {
                  return currentItem.id;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })(),
              publisherInputValue: generateStateValueProp($state, [
                "publicationTile",
                __plasmic_idx_0,
                "publisherInputValue"
              ]),
              titleInputValue: generateStateValueProp($state, [
                "publicationTile",
                __plasmic_idx_0,
                "titleInputValue"
              ])
            };

            initializePlasmicStates(
              $state,
              [
                {
                  name: "publicationTile[].titleInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.title;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "publicationTile[].publicationDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.publication_date
                          ? currentItem.publication_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "publicationTile[].publisherInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.publisher;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "publicationTile[].additionalAuthorsInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.additional_authors;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "publicationTile[].isbnIssnInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.reference_number;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "publicationTile[].deleteButtonClickStage",
                  initFunc: ({ $props, $state, $queries }) => 0
                },
                {
                  name: "publicationTile[].deleteButtonDisabled",
                  initFunc: ({ $props, $state, $queries }) => undefined
                },
                {
                  name: "publicationTile[].descriptionInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.description;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                }
              ],
              [__plasmic_idx_0]
            );
            return (
              <ProfileTilePublicationTile
                data-plasmic-name={"publicationTile"}
                data-plasmic-override={overrides.publicationTile}
                {...child$Props}
              />
            );
          })();
        })}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  publicationsSection: [
    "publicationsSection",
    "profileSectionsProfileSectionHeading",
    "pubDisplayContainer",
    "publicationTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading"
  ],
  pubDisplayContainer: ["pubDisplayContainer", "publicationTile"],
  publicationTile: ["publicationTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  publicationsSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  pubDisplayContainer: "div";
  publicationTile: typeof ProfileTilePublicationTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsPublicationsBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsPublicationsBlock__VariantsArgs;
    args?: PlasmicProfileSectionsPublicationsBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsPublicationsBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsPublicationsBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicProfileSectionsPublicationsBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsPublicationsBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsPublicationsBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "publicationsSection") {
    func.displayName = "PlasmicProfileSectionsPublicationsBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsPublicationsBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsPublicationsBlock = Object.assign(
  // Top-level PlasmicProfileSectionsPublicationsBlock renders the root element
  makeNodeComponent("publicationsSection"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    pubDisplayContainer: makeNodeComponent("pubDisplayContainer"),
    publicationTile: makeNodeComponent("publicationTile"),

    // Metadata about props expected for PlasmicProfileSectionsPublicationsBlock
    internalVariantProps: PlasmicProfileSectionsPublicationsBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsPublicationsBlock__ArgProps
  }
);

export default PlasmicProfileSectionsPublicationsBlock;
/* prettier-ignore-end */
