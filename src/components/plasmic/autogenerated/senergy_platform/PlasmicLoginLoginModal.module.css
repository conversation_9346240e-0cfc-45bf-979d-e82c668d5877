.root {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 600px;
  height: auto;
  max-width: 100%;
  background: var(--token-1AMvw6c2eIK7);
  row-gap: 32px;
  padding: 46px 84px;
  margin: var(--token-sazGmnf7GWAk);
}
.logoStack:global(.__wab_instance) {
  position: relative;
}
.form {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  row-gap: 8px;
  min-width: 0;
}
.emailInput:global(.__wab_instance) {
  position: relative;
}
.emailInputisSignUp:global(.__wab_instance) {
  margin-bottom: 8px;
}
.passwordInput:global(.__wab_instance) {
  position: relative;
}
.passwordInputisSignUp:global(.__wab_instance) {
  display: none;
}
.nameBoxes {
  position: relative;
  flex-direction: row;
  align-items: stretch;
  width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  display: none;
}
.nameBoxesisSignUp {
  margin-bottom: 8px;
  display: flex;
}
.firstNameInput:global(.__wab_instance) {
  display: none;
}
.firstNameInputisSignUp:global(.__wab_instance) {
  display: flex;
}
.lastNameInput:global(.__wab_instance) {
  position: relative;
}
.lastNameInputisSignUp:global(.__wab_instance) {
  display: flex;
}
.passwordCreationInput:global(.__wab_instance) {
  max-width: 100%;
  height: auto;
  position: relative;
  margin-bottom: 8px;
}
.passwordCreationInputisSignUp:global(.__wab_instance) {
  display: flex;
}
.passwordConfirmationInput:global(.__wab_instance) {
  max-width: 100%;
  height: auto;
  position: relative;
  margin-bottom: 8px;
}
.passwordConfirmationInputisSignUp:global(.__wab_instance) {
  display: flex;
}
.errorText {
  position: relative;
  width: 100%;
  max-width: 100%;
  font-family: "Arial";
  font-size: var(--token-2UEfYzPsoOY0);
  font-weight: 400;
  font-style: normal;
  color: var(--token-vmreU-dg02sh);
  text-align: center;
  text-transform: none;
  line-height: 1.5;
  letter-spacing: normal;
  white-space: pre-wrap;
  user-select: text;
  text-decoration-line: none;
  text-overflow: clip;
  min-width: 0;
}
.submitButton:global(.__wab_instance) {
  position: sticky;
  width: 180px;
  z-index: 1;
  height: 44px;
  flex-shrink: 0;
}
.svg__hsRrM {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.text {
  font-family: "Arial";
  font-size: var(--token-2UEfYzPsoOY0);
  font-weight: 400;
  font-style: normal;
  color: var(--token-xo_r2w5pebq-);
  text-align: center;
  text-transform: none;
  line-height: 1.5;
  letter-spacing: normal;
  white-space: pre;
  user-select: text;
  text-decoration-line: none !important;
  text-overflow: clip !important;
}
.textisLoading {
  display: none;
}
.svg__kaHgv {
  position: relative;
  object-fit: cover;
  max-width: 40%;
  height: 1em;
  display: none;
}
.svgisLoading__kaHgVphA88 {
  display: block;
}
.svg__oGgeK {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.swapButton {
  position: relative;
  text-decoration-line: none;
  color: var(--token-to1fT9g-t4Js);
  cursor: pointer;
  pointer-events: auto;
  border-style: none;
}
