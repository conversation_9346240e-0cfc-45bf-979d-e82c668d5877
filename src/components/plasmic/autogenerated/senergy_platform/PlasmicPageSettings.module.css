.root {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100vh;
  justify-content: flex-start;
  align-items: center;
  background: var(--token-1AMvw6c2eIK7);
  overflow: auto;
  min-width: 0;
}
.settingsHeader {
  display: grid;
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100%;
  margin-bottom: 64px;
  grid-column-start: 1 !important;
  grid-column-end: -1 !important;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.settingsHeader > * {
  grid-column: 4;
}
.h3__iOYzW {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  margin-bottom: 0px;
  min-width: 0;
}
.emails {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox__yidSg {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  background: var(--token-yOrnGWfG0DtN);
  min-width: 0;
  padding: var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
.svg___01Lvg {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 3px;
  width: var(--token-rB3BKCgczoWa);
  height: var(--token-rB3BKCgczoWa);
  flex-shrink: 0;
}
.text__zbyB {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.subButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.svg__sm0Zk {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__n7Y {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.settingsEmailEntry__l5Vq:global(.__wab_instance) {
  position: relative;
  margin: var(--token-sazGmnf7GWAk);
}
.activeSessions {
  display: grid;
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100%;
  margin-bottom: 64px;
  grid-column-start: 1 !important;
  grid-column-end: -1 !important;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.activeSessions > * {
  grid-column: 4;
}
.freeBox__uhGiI {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox__vDdvu {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  background: var(--token-yOrnGWfG0DtN);
  min-width: 0;
  padding: var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
.svg__vXkBy {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 3px;
  width: var(--token-rB3BKCgczoWa);
  height: var(--token-rB3BKCgczoWa);
  flex-shrink: 0;
}
.text___2MKkA {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.settingsEmailEntry__vrHOo:global(.__wab_instance) {
  position: relative;
  margin: var(--token-sazGmnf7GWAk);
}
.notificationSettings {
  display: grid;
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100%;
  grid-row-gap: 16px;
  margin-bottom: 64px;
  grid-column-start: 1 !important;
  grid-column-end: -1 !important;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.notificationSettings > * {
  grid-column: 4;
}
.h3__s6ApU {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  margin-top: 0px;
  margin-bottom: 0px;
  min-width: 0;
}
.freeBox__nlZw1 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox__yVmcq {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.h5__c1BUt {
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.text___9J8CK {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.subcomponentToggleSwitch___0AjZx:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  height: 30px;
}
.freeBox__ewPxl {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox__qphxa {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.h5__mgeT {
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.text__e4IDr {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.subcomponentToggleSwitch__urL3:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  height: 30px;
}
.freeBox__njBWu {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox__dkaqJ {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.h5__bM1Ht {
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.text__nRaZj {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.subcomponentToggleSwitch__zSscO:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  height: 30px;
}
