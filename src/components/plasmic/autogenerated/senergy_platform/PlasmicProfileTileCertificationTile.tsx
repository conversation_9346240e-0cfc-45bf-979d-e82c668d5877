/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: ld24XSt22oQ7

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentDeleteButton from "../../SubcomponentDeleteButton"; // plasmic-import: WMfLdq1qoZyo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileCertificationTile.module.css"; // plasmic-import: ld24XSt22oQ7/css

import CertificateIcon from "./icons/PlasmicIcon__Certificate"; // plasmic-import: oMeblj4iBOSs/icon
import HashtagIcon from "./icons/PlasmicIcon__Hashtag"; // plasmic-import: 7fTFQT6QY_o8/icon
import UserIcon from "./icons/PlasmicIcon__User"; // plasmic-import: hcR42vt5qkrz/icon
import MapPinIcon from "./icons/PlasmicIcon__MapPin"; // plasmic-import: Q_wDLzMm5l2Y/icon
import CalendarIcon from "./icons/PlasmicIcon__Calendar"; // plasmic-import: -xP51745fXLc/icon
import CalendarExpirationIcon from "./icons/PlasmicIcon__CalendarExpiration"; // plasmic-import: Ra-yugMEa7AY/icon

createPlasmicElementProxy;

export type PlasmicProfileTileCertificationTile__VariantMembers = {
  editable: "editable";
  overview: "overview";
};
export type PlasmicProfileTileCertificationTile__VariantsArgs = {
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
};
type VariantPropType = keyof PlasmicProfileTileCertificationTile__VariantsArgs;
export const PlasmicProfileTileCertificationTile__VariantProps =
  new Array<VariantPropType>("editable", "overview");

export type PlasmicProfileTileCertificationTile__ArgsType = {
  certificationId?: string;
  titleInputValue?: string;
  certifyingBodyInputValue?: string;
  completionDateInputValue?: string;
  registrationNumberInputValue?: string;
  instructorInputValue?: string;
  locationOfAwardInputValue?: string;
  expirationDateInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onCertifyingBodyInputValueChange?: (val: string) => void;
  onCompletionDateInputValueChange?: (val: string) => void;
  onRegistrationNumberInputValueChange?: (val: string) => void;
  onInstructorInputValueChange?: (val: string) => void;
  onLocationOfAwardInputValueChange?: (val: string) => void;
  onExpirationDateInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonOnClick?: (event: any) => void;
  deleteButtonDisabled?: any;
  deleteButtonClickStage?: number;
  onDeleteButtonDisabledChange?: (val: any) => void;
  onDeleteButtonClickStageChange?: (val: number) => void;
};
type ArgPropType = keyof PlasmicProfileTileCertificationTile__ArgsType;
export const PlasmicProfileTileCertificationTile__ArgProps =
  new Array<ArgPropType>(
    "certificationId",
    "titleInputValue",
    "certifyingBodyInputValue",
    "completionDateInputValue",
    "registrationNumberInputValue",
    "instructorInputValue",
    "locationOfAwardInputValue",
    "expirationDateInputValue",
    "descriptionInputValue",
    "onTitleInputValueChange",
    "onCertifyingBodyInputValueChange",
    "onCompletionDateInputValueChange",
    "onRegistrationNumberInputValueChange",
    "onInstructorInputValueChange",
    "onLocationOfAwardInputValueChange",
    "onExpirationDateInputValueChange",
    "onDescriptionInputValueChange",
    "deleteButtonOnClick",
    "deleteButtonDisabled",
    "deleteButtonClickStage",
    "onDeleteButtonDisabledChange",
    "onDeleteButtonClickStageChange"
  );

export type PlasmicProfileTileCertificationTile__OverridesType = {
  certificationSpacingContainer?: Flex__<"div">;
  certificationIcon?: Flex__<"svg">;
  informationStack?: Flex__<"div">;
  titleInput?: Flex__<typeof SubcomponentTextInput>;
  certifyingBodyInput?: Flex__<typeof SubcomponentTextInput>;
  infoBar?: Flex__<"section">;
  registrationNumberInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot3?: Flex__<"svg">;
  instructorInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot4?: Flex__<"svg">;
  locationOfAwardInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot5?: Flex__<"svg">;
  completionDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot2?: Flex__<"svg">;
  expirationDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot6?: Flex__<"svg">;
  description?: Flex__<typeof SubcomponentTextInput>;
  subDeleteButton?: Flex__<typeof SubcomponentDeleteButton>;
};

export interface DefaultProfileTileCertificationTileProps {
  certificationId?: string;
  titleInputValue?: string;
  certifyingBodyInputValue?: string;
  completionDateInputValue?: string;
  registrationNumberInputValue?: string;
  instructorInputValue?: string;
  locationOfAwardInputValue?: string;
  expirationDateInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onCertifyingBodyInputValueChange?: (val: string) => void;
  onCompletionDateInputValueChange?: (val: string) => void;
  onRegistrationNumberInputValueChange?: (val: string) => void;
  onInstructorInputValueChange?: (val: string) => void;
  onLocationOfAwardInputValueChange?: (val: string) => void;
  onExpirationDateInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonOnClick?: (event: any) => void;
  deleteButtonDisabled?: any;
  deleteButtonClickStage?: number;
  onDeleteButtonDisabledChange?: (val: any) => void;
  onDeleteButtonClickStageChange?: (val: number) => void;
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileCertificationTile__RenderFunc(props: {
  variants: PlasmicProfileTileCertificationTile__VariantsArgs;
  args: PlasmicProfileTileCertificationTile__ArgsType;
  overrides: PlasmicProfileTileCertificationTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "titleInput.value",
        type: "writable",
        variableType: "text",

        valueProp: "titleInputValue",
        onChangeProp: "onTitleInputValueChange"
      },
      {
        path: "completionDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "completionDateInputValue",
        onChangeProp: "onCompletionDateInputValueChange"
      },
      {
        path: "registrationNumberInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "registrationNumberInputValue",
        onChangeProp: "onRegistrationNumberInputValueChange"
      },
      {
        path: "instructorInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "instructorInputValue",
        onChangeProp: "onInstructorInputValueChange"
      },
      {
        path: "locationOfAwardInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "locationOfAwardInputValue",
        onChangeProp: "onLocationOfAwardInputValueChange"
      },
      {
        path: "expirationDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "expirationDateInputValue",
        onChangeProp: "onExpirationDateInputValueChange"
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "overview",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overview
      },
      {
        path: "certifyingBodyInput.value",
        type: "writable",
        variableType: "text",

        valueProp: "certifyingBodyInputValue",
        onChangeProp: "onCertifyingBodyInputValueChange"
      },
      {
        path: "subDeleteButton.clickStage",
        type: "writable",
        variableType: "number",

        valueProp: "deleteButtonClickStage",
        onChangeProp: "onDeleteButtonClickStageChange"
      },
      {
        path: "subDeleteButton.disabled",
        type: "writable",
        variableType: "text",

        valueProp: "deleteButtonDisabled",
        onChangeProp: "onDeleteButtonDisabledChange"
      },
      {
        path: "pendingUpdates",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => false
      },
      {
        path: "description.value",
        type: "writable",
        variableType: "text",

        valueProp: "descriptionInputValue",
        onChangeProp: "onDescriptionInputValueChange"
      },
      {
        path: "titleInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "certifyingBodyInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "description.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"certificationSpacingContainer"}
      data-plasmic-override={overrides.certificationSpacingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.certificationSpacingContainer,
        {
          [sty.certificationSpacingContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.certificationSpacingContaineroverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        }
      )}
    >
      <CertificateIcon
        data-plasmic-name={"certificationIcon"}
        data-plasmic-override={overrides.certificationIcon}
        className={classNames(projectcss.all, sty.certificationIcon, {
          [sty.certificationIconeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.certificationIconoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
        role={"img"}
      />

      <div
        data-plasmic-name={"informationStack"}
        data-plasmic-override={overrides.informationStack}
        className={classNames(projectcss.all, sty.informationStack, {
          [sty.informationStackeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.informationStackoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
      >
        <SubcomponentTextInput
          data-plasmic-name={"titleInput"}
          data-plasmic-override={overrides.titleInput}
          className={classNames("__wab_instance", sty.titleInput, {
            [sty.titleInputeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.titleInputoverview]: hasVariant($state, "overview", "overview")
          })}
          displayText={(() => {
            try {
              return $state.titleInput.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"heading3"}
          errorMessage={generateStateValueProp($state, [
            "titleInput",
            "errorMessage"
          ])}
          inputHoverText={"Certification Name"}
          inputName={"Certification Name"}
          inputPlaceholder={"Certification Name"}
          inputValue={generateStateValueProp($state, ["titleInput", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "titleInput",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["titleInput", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        <SubcomponentTextInput
          data-plasmic-name={"certifyingBodyInput"}
          data-plasmic-override={overrides.certifyingBodyInput}
          className={classNames("__wab_instance", sty.certifyingBodyInput, {
            [sty.certifyingBodyInputeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.certifyingBodyInputoverview]: hasVariant(
              $state,
              "overview",
              "overview"
            )
          })}
          displayText={(() => {
            try {
              return $state.certifyingBodyInput.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"subHeading"}
          errorMessage={generateStateValueProp($state, [
            "certifyingBodyInput",
            "errorMessage"
          ])}
          inputHoverText={"Certifying Body"}
          inputName={"Certifying Body"}
          inputPlaceholder={"Certifying Body"}
          inputValue={generateStateValueProp($state, [
            "certifyingBodyInput",
            "value"
          ])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "certifyingBodyInput",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "certifyingBodyInput",
              "value"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        <section
          data-plasmic-name={"infoBar"}
          data-plasmic-override={overrides.infoBar}
          className={classNames(projectcss.all, sty.infoBar, {
            [sty.infoBareditable]: hasVariant($state, "editable", "editable"),
            [sty.infoBaroverview]: hasVariant($state, "overview", "overview")
          })}
        >
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.registrationNumberInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"registrationNumberInput"}
              data-plasmic-override={overrides.registrationNumberInput}
              className={classNames(
                "__wab_instance",
                sty.registrationNumberInput,
                {
                  [sty.registrationNumberInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.registrationNumberInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.registrationNumberInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <HashtagIcon
                  data-plasmic-name={"iconSpot3"}
                  data-plasmic-override={overrides.iconSpot3}
                  className={classNames(projectcss.all, sty.iconSpot3)}
                  role={"img"}
                />
              }
              inputHoverText={"Registration Number"}
              inputName={"Registration Number"}
              inputPlaceholder={"Registration Number"}
              inputValue={generateStateValueProp($state, [
                "registrationNumberInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "registrationNumberInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.instructorInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"instructorInput"}
              data-plasmic-override={overrides.instructorInput}
              className={classNames("__wab_instance", sty.instructorInput, {
                [sty.instructorInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.instructorInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.instructorInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <UserIcon
                  data-plasmic-name={"iconSpot4"}
                  data-plasmic-override={overrides.iconSpot4}
                  className={classNames(projectcss.all, sty.iconSpot4)}
                  role={"img"}
                />
              }
              inputHoverText={"Instructor"}
              inputName={"Instructor"}
              inputPlaceholder={"Instructor"}
              inputValue={generateStateValueProp($state, [
                "instructorInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "instructorInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.locationOfAwardInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"locationOfAwardInput"}
              data-plasmic-override={overrides.locationOfAwardInput}
              className={classNames(
                "__wab_instance",
                sty.locationOfAwardInput,
                {
                  [sty.locationOfAwardInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.locationOfAwardInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.locationOfAwardInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <MapPinIcon
                  data-plasmic-name={"iconSpot5"}
                  data-plasmic-override={overrides.iconSpot5}
                  className={classNames(projectcss.all, sty.iconSpot5)}
                  role={"img"}
                />
              }
              inputHoverText={"Location of Award"}
              inputName={"Location of Award"}
              inputPlaceholder={"Location of Award"}
              inputValue={generateStateValueProp($state, [
                "locationOfAwardInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "locationOfAwardInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.completionDateInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"completionDateInput"}
              data-plasmic-override={overrides.completionDateInput}
              className={classNames("__wab_instance", sty.completionDateInput, {
                [sty.completionDateInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.completionDateInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.completionDateInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <CalendarIcon
                  data-plasmic-name={"iconSpot2"}
                  data-plasmic-override={overrides.iconSpot2}
                  className={classNames(projectcss.all, sty.iconSpot2, {
                    [sty.iconSpot2editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Date of Completion"}
              inputName={"Date of Completion"}
              inputPlaceholder={"Date of Completion"}
              inputType={"date"}
              inputValue={generateStateValueProp($state, [
                "completionDateInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "completionDateInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.expirationDateInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"expirationDateInput"}
              data-plasmic-override={overrides.expirationDateInput}
              className={classNames("__wab_instance", sty.expirationDateInput, {
                [sty.expirationDateInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.expirationDateInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.expirationDateInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <CalendarExpirationIcon
                  data-plasmic-name={"iconSpot6"}
                  data-plasmic-override={overrides.iconSpot6}
                  className={classNames(projectcss.all, sty.iconSpot6)}
                  role={"img"}
                />
              }
              inputHoverText={"Expiration Date"}
              inputName={"Expiration Date"}
              inputPlaceholder={"Expiration Date"}
              inputType={"date"}
              inputValue={generateStateValueProp($state, [
                "expirationDateInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "expirationDateInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
        </section>
        <SubcomponentTextInput
          data-plasmic-name={"description"}
          data-plasmic-override={overrides.description}
          className={classNames("__wab_instance", sty.description, {
            [sty.descriptioneditable]: hasVariant(
              $state,
              "editable",
              "editable"
            )
          })}
          displayText={(() => {
            try {
              return $state.description.value.length > 160
                ? $state.description.value.slice(0, 160) + "..."
                : $state.description.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          errorMessage={generateStateValueProp($state, [
            "description",
            "errorMessage"
          ])}
          inputHoverText={
            hasVariant($state, "editable", "editable")
              ? "Certification Summary"
              : undefined
          }
          inputName={
            hasVariant($state, "editable", "editable")
              ? "Certification Summary"
              : undefined
          }
          inputPlaceholder={
            hasVariant($state, "editable", "editable")
              ? "Certification Summary"
              : undefined
          }
          inputValue={generateStateValueProp($state, ["description", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "description",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["description", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />
      </div>
      <SubcomponentDeleteButton
        data-plasmic-name={"subDeleteButton"}
        data-plasmic-override={overrides.subDeleteButton}
        className={classNames("__wab_instance", sty.subDeleteButton, {
          [sty.subDeleteButtoneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
        onClick={args.deleteButtonOnClick}
        onClickStageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "clickStage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDisabledChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "disabled"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  certificationSpacingContainer: [
    "certificationSpacingContainer",
    "certificationIcon",
    "informationStack",
    "titleInput",
    "certifyingBodyInput",
    "infoBar",
    "registrationNumberInput",
    "iconSpot3",
    "instructorInput",
    "iconSpot4",
    "locationOfAwardInput",
    "iconSpot5",
    "completionDateInput",
    "iconSpot2",
    "expirationDateInput",
    "iconSpot6",
    "description",
    "subDeleteButton"
  ],
  certificationIcon: ["certificationIcon"],
  informationStack: [
    "informationStack",
    "titleInput",
    "certifyingBodyInput",
    "infoBar",
    "registrationNumberInput",
    "iconSpot3",
    "instructorInput",
    "iconSpot4",
    "locationOfAwardInput",
    "iconSpot5",
    "completionDateInput",
    "iconSpot2",
    "expirationDateInput",
    "iconSpot6",
    "description"
  ],
  titleInput: ["titleInput"],
  certifyingBodyInput: ["certifyingBodyInput"],
  infoBar: [
    "infoBar",
    "registrationNumberInput",
    "iconSpot3",
    "instructorInput",
    "iconSpot4",
    "locationOfAwardInput",
    "iconSpot5",
    "completionDateInput",
    "iconSpot2",
    "expirationDateInput",
    "iconSpot6"
  ],
  registrationNumberInput: ["registrationNumberInput", "iconSpot3"],
  iconSpot3: ["iconSpot3"],
  instructorInput: ["instructorInput", "iconSpot4"],
  iconSpot4: ["iconSpot4"],
  locationOfAwardInput: ["locationOfAwardInput", "iconSpot5"],
  iconSpot5: ["iconSpot5"],
  completionDateInput: ["completionDateInput", "iconSpot2"],
  iconSpot2: ["iconSpot2"],
  expirationDateInput: ["expirationDateInput", "iconSpot6"],
  iconSpot6: ["iconSpot6"],
  description: ["description"],
  subDeleteButton: ["subDeleteButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  certificationSpacingContainer: "div";
  certificationIcon: "svg";
  informationStack: "div";
  titleInput: typeof SubcomponentTextInput;
  certifyingBodyInput: typeof SubcomponentTextInput;
  infoBar: "section";
  registrationNumberInput: typeof SubcomponentIconWithText;
  iconSpot3: "svg";
  instructorInput: typeof SubcomponentIconWithText;
  iconSpot4: "svg";
  locationOfAwardInput: typeof SubcomponentIconWithText;
  iconSpot5: "svg";
  completionDateInput: typeof SubcomponentIconWithText;
  iconSpot2: "svg";
  expirationDateInput: typeof SubcomponentIconWithText;
  iconSpot6: "svg";
  description: typeof SubcomponentTextInput;
  subDeleteButton: typeof SubcomponentDeleteButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileCertificationTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileCertificationTile__VariantsArgs;
    args?: PlasmicProfileTileCertificationTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileTileCertificationTile__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileCertificationTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileCertificationTile__ArgProps,
          internalVariantPropNames:
            PlasmicProfileTileCertificationTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileCertificationTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "certificationSpacingContainer") {
    func.displayName = "PlasmicProfileTileCertificationTile";
  } else {
    func.displayName = `PlasmicProfileTileCertificationTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileCertificationTile = Object.assign(
  // Top-level PlasmicProfileTileCertificationTile renders the root element
  makeNodeComponent("certificationSpacingContainer"),
  {
    // Helper components rendering sub-elements
    certificationIcon: makeNodeComponent("certificationIcon"),
    informationStack: makeNodeComponent("informationStack"),
    titleInput: makeNodeComponent("titleInput"),
    certifyingBodyInput: makeNodeComponent("certifyingBodyInput"),
    infoBar: makeNodeComponent("infoBar"),
    registrationNumberInput: makeNodeComponent("registrationNumberInput"),
    iconSpot3: makeNodeComponent("iconSpot3"),
    instructorInput: makeNodeComponent("instructorInput"),
    iconSpot4: makeNodeComponent("iconSpot4"),
    locationOfAwardInput: makeNodeComponent("locationOfAwardInput"),
    iconSpot5: makeNodeComponent("iconSpot5"),
    completionDateInput: makeNodeComponent("completionDateInput"),
    iconSpot2: makeNodeComponent("iconSpot2"),
    expirationDateInput: makeNodeComponent("expirationDateInput"),
    iconSpot6: makeNodeComponent("iconSpot6"),
    description: makeNodeComponent("description"),
    subDeleteButton: makeNodeComponent("subDeleteButton"),

    // Metadata about props expected for PlasmicProfileTileCertificationTile
    internalVariantProps: PlasmicProfileTileCertificationTile__VariantProps,
    internalArgProps: PlasmicProfileTileCertificationTile__ArgProps
  }
);

export default PlasmicProfileTileCertificationTile;
/* prettier-ignore-end */
