/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: Wr1MGCSkm1uC

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import NavigationPopupModals from "../../NavigationPopupModals"; // plasmic-import: LXUgsJuF4wtn/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationButton.module.css"; // plasmic-import: Wr1MGCSkm1uC/css

createPlasmicElementProxy;

export type PlasmicNavigationButton__VariantMembers = {
  collapsed: "collapsed";
  hover: "hover";
  modals: "legal";
  closable: "closable";
  disabled: "disabled";
};
export type PlasmicNavigationButton__VariantsArgs = {
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  hover?: SingleBooleanChoiceArg<"hover">;
  modals?: SingleChoiceArg<"legal">;
  closable?: SingleBooleanChoiceArg<"closable">;
  disabled?: SingleBooleanChoiceArg<"disabled">;
};
type VariantPropType = keyof PlasmicNavigationButton__VariantsArgs;
export const PlasmicNavigationButton__VariantProps = new Array<VariantPropType>(
  "collapsed",
  "hover",
  "modals",
  "closable",
  "disabled"
);

export type PlasmicNavigationButton__ArgsType = {
  navIconSlot?: React.ReactNode;
  buttonText?: string;
  onClick?: (event: any) => void;
  onPointerEnter?: (event: any) => void;
  onPointerLeave?: (event: any) => void;
  onModalsChange?: (val: any) => void;
};
type ArgPropType = keyof PlasmicNavigationButton__ArgsType;
export const PlasmicNavigationButton__ArgProps = new Array<ArgPropType>(
  "navIconSlot",
  "buttonText",
  "onClick",
  "onPointerEnter",
  "onPointerLeave",
  "onModalsChange"
);

export type PlasmicNavigationButton__OverridesType = {
  navBarButtonContainer?: Flex__<"button">;
  freeBox?: Flex__<"div">;
  iconSectionContainer?: Flex__<"div">;
  pageTitle?: Flex__<"div">;
  text?: Flex__<"div">;
  navigationPopupModals?: Flex__<typeof NavigationPopupModals>;
};

export interface DefaultNavigationButtonProps {
  navIconSlot?: React.ReactNode;
  buttonText?: string;
  onClick?: (event: any) => void;
  onPointerEnter?: (event: any) => void;
  onPointerLeave?: (event: any) => void;
  onModalsChange?: (val: any) => void;
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  modals?: SingleChoiceArg<"legal">;
  closable?: SingleBooleanChoiceArg<"closable">;
  disabled?: SingleBooleanChoiceArg<"disabled">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationButton__RenderFunc(props: {
  variants: PlasmicNavigationButton__VariantsArgs;
  args: PlasmicNavigationButton__ArgsType;
  overrides: PlasmicNavigationButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "collapsed",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.collapsed
      },
      {
        path: "hover",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hover
      },
      {
        path: "modals",
        type: "writable",
        variableType: "variant",

        valueProp: "modals",
        onChangeProp: "onModalsChange"
      },
      {
        path: "closable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.closable
      },
      {
        path: "disabled",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.disabled
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <button
      data-plasmic-name={"navBarButtonContainer"}
      data-plasmic-override={overrides.navBarButtonContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.button,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.navBarButtonContainer,
        {
          [sty.navBarButtonContainerclosable]: hasVariant(
            $state,
            "closable",
            "closable"
          ),
          [sty.navBarButtonContainercollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.navBarButtonContainerdisabled]: hasVariant(
            $state,
            "disabled",
            "disabled"
          ),
          [sty.navBarButtonContainerhover]: hasVariant(
            $state,
            "hover",
            "hover"
          ),
          [sty.navBarButtonContainerhover_collapsed]:
            hasVariant($state, "collapsed", "collapsed") &&
            hasVariant($state, "hover", "hover"),
          [sty.navBarButtonContainermodals_legal]: hasVariant(
            $state,
            "modals",
            "legal"
          )
        }
      )}
      disabled={hasVariant($state, "disabled", "disabled") ? true : undefined}
      onClick={args.onClick}
      onPointerEnter={args.onPointerEnter}
      onPointerLeave={args.onPointerLeave}
      ref={ref => {
        $refs["navBarButtonContainer"] = ref;
      }}
    >
      <div
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        className={classNames(projectcss.all, sty.freeBox, {
          [sty.freeBoxclosable]: hasVariant($state, "closable", "closable"),
          [sty.freeBoxcollapsed]: hasVariant($state, "collapsed", "collapsed")
        })}
      >
        <div
          data-plasmic-name={"iconSectionContainer"}
          data-plasmic-override={overrides.iconSectionContainer}
          className={classNames(projectcss.all, sty.iconSectionContainer, {
            [sty.iconSectionContainerclosable]: hasVariant(
              $state,
              "closable",
              "closable"
            ),
            [sty.iconSectionContainercollapsed]: hasVariant(
              $state,
              "collapsed",
              "collapsed"
            )
          })}
        >
          {renderPlasmicSlot({
            defaultContents: (
              <svg
                className={classNames(projectcss.all, sty.svg__rVb6N)}
                role={"img"}
              />
            ),

            value: args.navIconSlot
          })}
        </div>
        <div
          data-plasmic-name={"pageTitle"}
          data-plasmic-override={overrides.pageTitle}
          className={classNames(projectcss.all, sty.pageTitle, {
            [sty.pageTitleclosable]: hasVariant($state, "closable", "closable"),
            [sty.pageTitlecollapsed]: hasVariant(
              $state,
              "collapsed",
              "collapsed"
            )
          })}
        >
          <div
            data-plasmic-name={"text"}
            data-plasmic-override={overrides.text}
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text,
              {
                [sty.textcollapsed]: hasVariant(
                  $state,
                  "collapsed",
                  "collapsed"
                ),
                [sty.textdisabled]: hasVariant($state, "disabled", "disabled"),
                [sty.textmodals_legal]: hasVariant($state, "modals", "legal")
              }
            )}
          >
            <React.Fragment>{$props.buttonText}</React.Fragment>
          </div>
        </div>
      </div>
      <NavigationPopupModals
        data-plasmic-name={"navigationPopupModals"}
        data-plasmic-override={overrides.navigationPopupModals}
        className={classNames("__wab_instance", sty.navigationPopupModals, {
          [sty.navigationPopupModalsmodals_legal]: hasVariant(
            $state,
            "modals",
            "legal"
          )
        })}
        navBarModals={"legal"}
      />
    </button>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  navBarButtonContainer: [
    "navBarButtonContainer",
    "freeBox",
    "iconSectionContainer",
    "pageTitle",
    "text",
    "navigationPopupModals"
  ],
  freeBox: ["freeBox", "iconSectionContainer", "pageTitle", "text"],
  iconSectionContainer: ["iconSectionContainer"],
  pageTitle: ["pageTitle", "text"],
  text: ["text"],
  navigationPopupModals: ["navigationPopupModals"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  navBarButtonContainer: "button";
  freeBox: "div";
  iconSectionContainer: "div";
  pageTitle: "div";
  text: "div";
  navigationPopupModals: typeof NavigationPopupModals;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationButton__VariantsArgs;
    args?: PlasmicNavigationButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicNavigationButton__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationButton__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationButton__ArgProps,
          internalVariantPropNames: PlasmicNavigationButton__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "navBarButtonContainer") {
    func.displayName = "PlasmicNavigationButton";
  } else {
    func.displayName = `PlasmicNavigationButton.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationButton = Object.assign(
  // Top-level PlasmicNavigationButton renders the root element
  makeNodeComponent("navBarButtonContainer"),
  {
    // Helper components rendering sub-elements
    freeBox: makeNodeComponent("freeBox"),
    iconSectionContainer: makeNodeComponent("iconSectionContainer"),
    pageTitle: makeNodeComponent("pageTitle"),
    text: makeNodeComponent("text"),
    navigationPopupModals: makeNodeComponent("navigationPopupModals"),

    // Metadata about props expected for PlasmicNavigationButton
    internalVariantProps: PlasmicNavigationButton__VariantProps,
    internalArgProps: PlasmicNavigationButton__ArgProps
  }
);

export default PlasmicNavigationButton;
/* prettier-ignore-end */
