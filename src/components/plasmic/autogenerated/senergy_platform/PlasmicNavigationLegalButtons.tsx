/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 4AlhyCYF3WHm

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationLegalButtons.module.css"; // plasmic-import: 4AlhyCYF3WHm/css

createPlasmicElementProxy;

export type PlasmicNavigationLegalButtons__VariantMembers = {};
export type PlasmicNavigationLegalButtons__VariantsArgs = {};
type VariantPropType = keyof PlasmicNavigationLegalButtons__VariantsArgs;
export const PlasmicNavigationLegalButtons__VariantProps =
  new Array<VariantPropType>();

export type PlasmicNavigationLegalButtons__ArgsType = {
  onClick?: (event: any) => void;
  children?: React.ReactNode;
};
type ArgPropType = keyof PlasmicNavigationLegalButtons__ArgsType;
export const PlasmicNavigationLegalButtons__ArgProps = new Array<ArgPropType>(
  "onClick",
  "children"
);

export type PlasmicNavigationLegalButtons__OverridesType = {
  root?: Flex__<"a"> & Partial<LinkProps>;
  formattingStack?: Flex__<"div">;
  textSlotContainer?: Flex__<"div">;
  underlineSection?: Flex__<"section">;
};

export interface DefaultNavigationLegalButtonsProps {
  onClick?: (event: any) => void;
  children?: React.ReactNode;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationLegalButtons__RenderFunc(props: {
  variants: PlasmicNavigationLegalButtons__VariantsArgs;
  args: PlasmicNavigationLegalButtons__ArgsType;
  overrides: PlasmicNavigationLegalButtons__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const [isRootHover, triggerRootHoverProps] = useTrigger("useHover", {});
  const triggers = {
    hover_root: isRootHover
  };

  return (
    <PlasmicLink__
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.a,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
      component={Link}
      onClick={args.onClick}
      platform={"nextjs"}
      data-plasmic-trigger-props={[triggerRootHoverProps]}
    >
      <div
        data-plasmic-name={"formattingStack"}
        data-plasmic-override={overrides.formattingStack}
        className={classNames(projectcss.all, sty.formattingStack)}
      >
        <div
          data-plasmic-name={"textSlotContainer"}
          data-plasmic-override={overrides.textSlotContainer}
          className={classNames(projectcss.all, sty.textSlotContainer)}
        >
          {renderPlasmicSlot({
            defaultContents: "Text Here",
            value: args.children,
            className: classNames(sty.slotTargetChildren)
          })}
        </div>
        <section
          data-plasmic-name={"underlineSection"}
          data-plasmic-override={overrides.underlineSection}
          className={classNames(projectcss.all, sty.underlineSection)}
        />
      </div>
    </PlasmicLink__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "formattingStack", "textSlotContainer", "underlineSection"],
  formattingStack: ["formattingStack", "textSlotContainer", "underlineSection"],
  textSlotContainer: ["textSlotContainer"],
  underlineSection: ["underlineSection"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "a";
  formattingStack: "div";
  textSlotContainer: "div";
  underlineSection: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationLegalButtons__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationLegalButtons__VariantsArgs;
    args?: PlasmicNavigationLegalButtons__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicNavigationLegalButtons__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationLegalButtons__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationLegalButtons__ArgProps,
          internalVariantPropNames: PlasmicNavigationLegalButtons__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationLegalButtons__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicNavigationLegalButtons";
  } else {
    func.displayName = `PlasmicNavigationLegalButtons.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationLegalButtons = Object.assign(
  // Top-level PlasmicNavigationLegalButtons renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    formattingStack: makeNodeComponent("formattingStack"),
    textSlotContainer: makeNodeComponent("textSlotContainer"),
    underlineSection: makeNodeComponent("underlineSection"),

    // Metadata about props expected for PlasmicNavigationLegalButtons
    internalVariantProps: PlasmicNavigationLegalButtons__VariantProps,
    internalArgProps: PlasmicNavigationLegalButtons__ArgProps
  }
);

export default PlasmicNavigationLegalButtons;
/* prettier-ignore-end */
