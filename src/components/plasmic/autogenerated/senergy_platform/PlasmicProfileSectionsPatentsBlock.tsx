/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: n9af0JVJyMFI

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTilePatentTile from "../../ProfileTilePatentTile"; // plasmic-import: R0Gfd7-8fcvf/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsPatentsBlock.module.css"; // plasmic-import: n9af0JVJyMFI/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsPatentsBlock__VariantMembers = {
  overviewGrid: "overviewGridBlock";
  editable: "editable";
};
export type PlasmicProfileSectionsPatentsBlock__VariantsArgs = {
  overviewGrid?: SingleChoiceArg<"overviewGridBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileSectionsPatentsBlock__VariantsArgs;
export const PlasmicProfileSectionsPatentsBlock__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileSectionsPatentsBlock__ArgsType = {
  allPatents?: any;
  onAllPatentsChange?: (val: string) => void;
  addButtonBaseOnClick?: (event: any) => void;
  addButtonPatentOnClick?: (event: any) => void;
  addButtonTrademarkOnClick?: (event: any) => void;
  addButtonLicenseOnClick?: (event: any) => void;
  addButtonCertificationOnClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicProfileSectionsPatentsBlock__ArgsType;
export const PlasmicProfileSectionsPatentsBlock__ArgProps =
  new Array<ArgPropType>(
    "allPatents",
    "onAllPatentsChange",
    "addButtonBaseOnClick",
    "addButtonPatentOnClick",
    "addButtonTrademarkOnClick",
    "addButtonLicenseOnClick",
    "addButtonCertificationOnClick"
  );

export type PlasmicProfileSectionsPatentsBlock__OverridesType = {
  patentsAndTrademarksSection?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  text?: Flex__<"div">;
  displayContainer?: Flex__<"section">;
  tPatentTile?: Flex__<typeof ProfileTilePatentTile>;
};

export interface DefaultProfileSectionsPatentsBlockProps {
  allPatents?: any;
  onAllPatentsChange?: (val: string) => void;
  addButtonBaseOnClick?: (event: any) => void;
  addButtonPatentOnClick?: (event: any) => void;
  addButtonTrademarkOnClick?: (event: any) => void;
  addButtonLicenseOnClick?: (event: any) => void;
  addButtonCertificationOnClick?: (event: any) => void;
  overviewGrid?: SingleChoiceArg<"overviewGridBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsPatentsBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsPatentsBlock__VariantsArgs;
  args: PlasmicProfileSectionsPatentsBlock__ArgsType;
  overrides: PlasmicProfileSectionsPatentsBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "tPatentTile[].titleInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tPatentTile[].countryOfRegistrationInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tPatentTile[].eventDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tPatentTile[].registrationNumberInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tPatentTile[].deleteButtonClickStage",
        type: "private",
        variableType: "number"
      },
      {
        path: "tPatentTile[].deleteButtonDisabled",
        type: "private",
        variableType: "text"
      },
      {
        path: "tPatentTile[].descriptionInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "allPatents",
        type: "writable",
        variableType: "object",

        valueProp: "allPatents",
        onChangeProp: "onAllPatentsChange"
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"patentsAndTrademarksSection"}
      data-plasmic-override={overrides.patentsAndTrademarksSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.patentsAndTrademarksSection,
        {
          [sty.patentsAndTrademarksSectionoverviewGrid_overviewGridBlock]:
            hasVariant($state, "overviewGrid", "overviewGridBlock")
        }
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        addButtonBaseOnClick={args.addButtonBaseOnClick}
        addButtonCertificationOnClick={args.addButtonCertificationOnClick}
        addButtonLicenseOnClick={args.addButtonLicenseOnClick}
        addButtonPatentOnClick={args.addButtonPatentOnClick}
        addButtonTrademarkOnClick={args.addButtonTrademarkOnClick}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewGrid_overviewGridBlock]:
              hasVariant($state, "overviewGrid", "overviewGridBlock")
          }
        )}
        editable={
          hasVariant($state, "editable", "editable")
            ? true
            : (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })()
        }
        overviewGrid={
          hasVariant($state, "overviewGrid", "overviewGridBlock")
            ? true
            : undefined
        }
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text
          )}
        >
          {"Patents"}
        </div>
      </ProfileSectionsProfileSectionHeading>
      <section
        data-plasmic-name={"displayContainer"}
        data-plasmic-override={overrides.displayContainer}
        className={classNames(projectcss.all, sty.displayContainer, {
          [sty.displayContaineroverviewGrid_overviewGridBlock]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGridBlock"
          )
        })}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
          (() => {
            try {
              return $state.allPatents.data;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()
        ).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (() => {
            const child$Props = {
              className: classNames("__wab_instance", sty.tPatentTile, {
                [sty.tPatentTileeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.tPatentTileoverviewGrid_overviewGridBlock]: hasVariant(
                  $state,
                  "overviewGrid",
                  "overviewGridBlock"
                )
              }),
              countryOfRegistrationInputValue: generateStateValueProp($state, [
                "tPatentTile",
                __plasmic_idx_0,
                "countryOfRegistrationInputValue"
              ]),
              deleteButtonClickStage: generateStateValueProp($state, [
                "tPatentTile",
                __plasmic_idx_0,
                "deleteButtonClickStage"
              ]),
              deleteButtonDisabled: generateStateValueProp($state, [
                "tPatentTile",
                __plasmic_idx_0,
                "deleteButtonDisabled"
              ]),
              descriptionInputValue: generateStateValueProp($state, [
                "tPatentTile",
                __plasmic_idx_0,
                "descriptionInputValue"
              ]),
              editable: hasVariant($state, "editable", "editable")
                ? true
                : (() => {
                    try {
                      return $state.editable;
                    } catch (e) {
                      if (
                        e instanceof TypeError ||
                        e?.plasmicType === "PlasmicUndefinedDataError"
                      ) {
                        return [];
                      }
                      throw e;
                    }
                  })(),
              eventDateInputValue: generateStateValueProp($state, [
                "tPatentTile",
                __plasmic_idx_0,
                "eventDateInputValue"
              ]),
              key: currentIndex,
              onCountryOfRegistrationInputValueChange: async (
                ...eventArgs: any
              ) => {
                generateStateOnChangeProp($state, [
                  "tPatentTile",
                  __plasmic_idx_0,
                  "countryOfRegistrationInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonClickStageChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tPatentTile",
                  __plasmic_idx_0,
                  "deleteButtonClickStage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonDisabledChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tPatentTile",
                  __plasmic_idx_0,
                  "deleteButtonDisabled"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDescriptionInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tPatentTile",
                  __plasmic_idx_0,
                  "descriptionInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onEventDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tPatentTile",
                  __plasmic_idx_0,
                  "eventDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onRegistrationNumberInputValueChange: async (
                ...eventArgs: any
              ) => {
                generateStateOnChangeProp($state, [
                  "tPatentTile",
                  __plasmic_idx_0,
                  "registrationNumberInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onTitleInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tPatentTile",
                  __plasmic_idx_0,
                  "titleInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              overview: hasVariant($state, "overviewGrid", "overviewGridBlock")
                ? true
                : undefined,
              patentId: (() => {
                try {
                  return currentItem.id;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })(),
              registrationNumberInputValue: generateStateValueProp($state, [
                "tPatentTile",
                __plasmic_idx_0,
                "registrationNumberInputValue"
              ]),
              titleInputValue: generateStateValueProp($state, [
                "tPatentTile",
                __plasmic_idx_0,
                "titleInputValue"
              ])
            };

            initializePlasmicStates(
              $state,
              [
                {
                  name: "tPatentTile[].titleInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.title;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tPatentTile[].countryOfRegistrationInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.registration_location;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tPatentTile[].eventDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.event_date
                          ? currentItem.event_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tPatentTile[].registrationNumberInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.registration_number;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tPatentTile[].deleteButtonClickStage",
                  initFunc: ({ $props, $state, $queries }) => 0
                },
                {
                  name: "tPatentTile[].deleteButtonDisabled",
                  initFunc: ({ $props, $state, $queries }) => undefined
                },
                {
                  name: "tPatentTile[].descriptionInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.summary;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                }
              ],
              [__plasmic_idx_0]
            );
            return (
              <ProfileTilePatentTile
                data-plasmic-name={"tPatentTile"}
                data-plasmic-override={overrides.tPatentTile}
                {...child$Props}
              />
            );
          })();
        })}
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  patentsAndTrademarksSection: [
    "patentsAndTrademarksSection",
    "profileSectionsProfileSectionHeading",
    "text",
    "displayContainer",
    "tPatentTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading",
    "text"
  ],
  text: ["text"],
  displayContainer: ["displayContainer", "tPatentTile"],
  tPatentTile: ["tPatentTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  patentsAndTrademarksSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  text: "div";
  displayContainer: "section";
  tPatentTile: typeof ProfileTilePatentTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsPatentsBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsPatentsBlock__VariantsArgs;
    args?: PlasmicProfileSectionsPatentsBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsPatentsBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsPatentsBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileSectionsPatentsBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsPatentsBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsPatentsBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "patentsAndTrademarksSection") {
    func.displayName = "PlasmicProfileSectionsPatentsBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsPatentsBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsPatentsBlock = Object.assign(
  // Top-level PlasmicProfileSectionsPatentsBlock renders the root element
  makeNodeComponent("patentsAndTrademarksSection"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    text: makeNodeComponent("text"),
    displayContainer: makeNodeComponent("displayContainer"),
    tPatentTile: makeNodeComponent("tPatentTile"),

    // Metadata about props expected for PlasmicProfileSectionsPatentsBlock
    internalVariantProps: PlasmicProfileSectionsPatentsBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsPatentsBlock__ArgProps
  }
);

export default PlasmicProfileSectionsPatentsBlock;
/* prettier-ignore-end */
