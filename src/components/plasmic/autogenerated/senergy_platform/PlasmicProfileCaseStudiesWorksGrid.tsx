/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: YSAU2xF9PL58

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileTileCaseStudyTile from "../../ProfileTileCaseStudyTile"; // plasmic-import: msZSdug4VrvS/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileCaseStudiesWorksGrid.module.css"; // plasmic-import: YSAU2xF9PL58/css

createPlasmicElementProxy;

export type PlasmicProfileCaseStudiesWorksGrid__VariantMembers = {};
export type PlasmicProfileCaseStudiesWorksGrid__VariantsArgs = {};
type VariantPropType = keyof PlasmicProfileCaseStudiesWorksGrid__VariantsArgs;
export const PlasmicProfileCaseStudiesWorksGrid__VariantProps =
  new Array<VariantPropType>();

export type PlasmicProfileCaseStudiesWorksGrid__ArgsType = {};
type ArgPropType = keyof PlasmicProfileCaseStudiesWorksGrid__ArgsType;
export const PlasmicProfileCaseStudiesWorksGrid__ArgProps =
  new Array<ArgPropType>();

export type PlasmicProfileCaseStudiesWorksGrid__OverridesType = {
  caseStudiesFormatting?: Flex__<"div">;
  profileTileCaseStudyTile?: Flex__<typeof ProfileTileCaseStudyTile>;
};

export interface DefaultProfileCaseStudiesWorksGridProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileCaseStudiesWorksGrid__RenderFunc(props: {
  variants: PlasmicProfileCaseStudiesWorksGrid__VariantsArgs;
  args: PlasmicProfileCaseStudiesWorksGrid__ArgsType;
  overrides: PlasmicProfileCaseStudiesWorksGrid__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"caseStudiesFormatting"}
      data-plasmic-override={overrides.caseStudiesFormatting}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.caseStudiesFormatting
      )}
    >
      {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
        (() => {
          try {
            return [2, 3, 4, 5, 6, 7, 8, 9, 10];
          } catch (e) {
            if (
              e instanceof TypeError ||
              e?.plasmicType === "PlasmicUndefinedDataError"
            ) {
              return [];
            }
            throw e;
          }
        })()
      ).map((__plasmic_item_0, __plasmic_idx_0) => {
        const currentItem = __plasmic_item_0;
        const currentIndex = __plasmic_idx_0;
        return (
          <ProfileTileCaseStudyTile
            data-plasmic-name={"profileTileCaseStudyTile"}
            data-plasmic-override={overrides.profileTileCaseStudyTile}
            className={classNames(
              "__wab_instance",
              sty.profileTileCaseStudyTile
            )}
            comingSoon={true}
            content={"photo"}
            key={currentIndex}
          />
        );
      })}
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  caseStudiesFormatting: ["caseStudiesFormatting", "profileTileCaseStudyTile"],
  profileTileCaseStudyTile: ["profileTileCaseStudyTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  caseStudiesFormatting: "div";
  profileTileCaseStudyTile: typeof ProfileTileCaseStudyTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileCaseStudiesWorksGrid__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileCaseStudiesWorksGrid__VariantsArgs;
    args?: PlasmicProfileCaseStudiesWorksGrid__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileCaseStudiesWorksGrid__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileCaseStudiesWorksGrid__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileCaseStudiesWorksGrid__ArgProps,
          internalVariantPropNames:
            PlasmicProfileCaseStudiesWorksGrid__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileCaseStudiesWorksGrid__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "caseStudiesFormatting") {
    func.displayName = "PlasmicProfileCaseStudiesWorksGrid";
  } else {
    func.displayName = `PlasmicProfileCaseStudiesWorksGrid.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileCaseStudiesWorksGrid = Object.assign(
  // Top-level PlasmicProfileCaseStudiesWorksGrid renders the root element
  makeNodeComponent("caseStudiesFormatting"),
  {
    // Helper components rendering sub-elements
    profileTileCaseStudyTile: makeNodeComponent("profileTileCaseStudyTile"),

    // Metadata about props expected for PlasmicProfileCaseStudiesWorksGrid
    internalVariantProps: PlasmicProfileCaseStudiesWorksGrid__VariantProps,
    internalArgProps: PlasmicProfileCaseStudiesWorksGrid__ArgProps
  }
);

export default PlasmicProfileCaseStudiesWorksGrid;
/* prettier-ignore-end */
