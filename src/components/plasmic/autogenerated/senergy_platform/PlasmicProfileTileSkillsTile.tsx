/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: V4zwOqV9lT52

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>iceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileSkillsTile.module.css"; // plasmic-import: V4zwOqV9lT52/css

createPlasmicElementProxy;

export type PlasmicProfileTileSkillsTile__VariantMembers = {
  overviewGrid: "overviewGrid";
  editable: "editable";
};
export type PlasmicProfileTileSkillsTile__VariantsArgs = {
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileTileSkillsTile__VariantsArgs;
export const PlasmicProfileTileSkillsTile__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileTileSkillsTile__ArgsType = {};
type ArgPropType = keyof PlasmicProfileTileSkillsTile__ArgsType;
export const PlasmicProfileTileSkillsTile__ArgProps = new Array<ArgPropType>();

export type PlasmicProfileTileSkillsTile__OverridesType = {
  skillsFormattingBox?: Flex__<"div">;
  skillsContent?: Flex__<"section">;
  text?: Flex__<"div">;
};

export interface DefaultProfileTileSkillsTileProps {
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileSkillsTile__RenderFunc(props: {
  variants: PlasmicProfileTileSkillsTile__VariantsArgs;
  args: PlasmicProfileTileSkillsTile__ArgsType;
  overrides: PlasmicProfileTileSkillsTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"skillsFormattingBox"}
      data-plasmic-override={overrides.skillsFormattingBox}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.skillsFormattingBox,
        {
          [sty.skillsFormattingBoxoverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        }
      )}
    >
      <section
        data-plasmic-name={"skillsContent"}
        data-plasmic-override={overrides.skillsContent}
        className={classNames(projectcss.all, sty.skillsContent, {
          [sty.skillsContentoverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        })}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text,
            {
              [sty.textoverviewGrid]: hasVariant(
                $state,
                "overviewGrid",
                "overviewGrid"
              )
            }
          )}
        >
          {
            "This is a placeholder text.  The skills component is a WIP and will be added when it is finished. "
          }
        </div>
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  skillsFormattingBox: ["skillsFormattingBox", "skillsContent", "text"],
  skillsContent: ["skillsContent", "text"],
  text: ["text"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  skillsFormattingBox: "div";
  skillsContent: "section";
  text: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileSkillsTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileSkillsTile__VariantsArgs;
    args?: PlasmicProfileTileSkillsTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTileSkillsTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileSkillsTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileSkillsTile__ArgProps,
          internalVariantPropNames: PlasmicProfileTileSkillsTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileSkillsTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "skillsFormattingBox") {
    func.displayName = "PlasmicProfileTileSkillsTile";
  } else {
    func.displayName = `PlasmicProfileTileSkillsTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileSkillsTile = Object.assign(
  // Top-level PlasmicProfileTileSkillsTile renders the root element
  makeNodeComponent("skillsFormattingBox"),
  {
    // Helper components rendering sub-elements
    skillsContent: makeNodeComponent("skillsContent"),
    text: makeNodeComponent("text"),

    // Metadata about props expected for PlasmicProfileTileSkillsTile
    internalVariantProps: PlasmicProfileTileSkillsTile__VariantProps,
    internalArgProps: PlasmicProfileTileSkillsTile__ArgProps
  }
);

export default PlasmicProfileTileSkillsTile;
/* prettier-ignore-end */
