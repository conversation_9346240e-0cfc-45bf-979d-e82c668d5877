/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: FSzKF-1XfOQy

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>ice<PERSON>rg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSearchCoreBookmarkButton.module.css"; // plasmic-import: FSzKF-1XfOQy/css

import BookmarkEmptyIcon from "./icons/PlasmicIcon__BookmarkEmpty"; // plasmic-import: CaAupFMSbhPP/icon
import BookmarkFilledIcon from "./icons/PlasmicIcon__BookmarkFilled"; // plasmic-import: mYm5L1LPvJxL/icon

createPlasmicElementProxy;

export type PlasmicSearchCoreBookmarkButton__VariantMembers = {
  selected: "selected";
};
export type PlasmicSearchCoreBookmarkButton__VariantsArgs = {
  selected?: SingleBooleanChoiceArg<"selected">;
};
type VariantPropType = keyof PlasmicSearchCoreBookmarkButton__VariantsArgs;
export const PlasmicSearchCoreBookmarkButton__VariantProps =
  new Array<VariantPropType>("selected");

export type PlasmicSearchCoreBookmarkButton__ArgsType = {};
type ArgPropType = keyof PlasmicSearchCoreBookmarkButton__ArgsType;
export const PlasmicSearchCoreBookmarkButton__ArgProps =
  new Array<ArgPropType>();

export type PlasmicSearchCoreBookmarkButton__OverridesType = {
  bookmarkIconContainer?: Flex__<"div">;
  bookmarkIcon?: Flex__<"svg">;
};

export interface DefaultSearchCoreBookmarkButtonProps {
  selected?: SingleBooleanChoiceArg<"selected">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSearchCoreBookmarkButton__RenderFunc(props: {
  variants: PlasmicSearchCoreBookmarkButton__VariantsArgs;
  args: PlasmicSearchCoreBookmarkButton__ArgsType;
  overrides: PlasmicSearchCoreBookmarkButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "selected",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.selected
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const [
    isBookmarkIconContainerActive,
    triggerBookmarkIconContainerActiveProps
  ] = useTrigger("usePressed", {});
  const triggers = {
    active_bookmarkIconContainer: isBookmarkIconContainerActive
  };

  return (
    <div
      data-plasmic-name={"bookmarkIconContainer"}
      data-plasmic-override={overrides.bookmarkIconContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.bookmarkIconContainer
      )}
      data-plasmic-trigger-props={[triggerBookmarkIconContainerActiveProps]}
    >
      <PlasmicIcon__
        data-plasmic-name={"bookmarkIcon"}
        data-plasmic-override={overrides.bookmarkIcon}
        PlasmicIconType={
          triggers.active_bookmarkIconContainer
            ? BookmarkFilledIcon
            : hasVariant($state, "selected", "selected")
            ? BookmarkFilledIcon
            : BookmarkEmptyIcon
        }
        className={classNames(projectcss.all, sty.bookmarkIcon, {
          [sty.bookmarkIconselected]: hasVariant($state, "selected", "selected")
        })}
        role={"img"}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  bookmarkIconContainer: ["bookmarkIconContainer", "bookmarkIcon"],
  bookmarkIcon: ["bookmarkIcon"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  bookmarkIconContainer: "div";
  bookmarkIcon: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSearchCoreBookmarkButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSearchCoreBookmarkButton__VariantsArgs;
    args?: PlasmicSearchCoreBookmarkButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSearchCoreBookmarkButton__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSearchCoreBookmarkButton__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSearchCoreBookmarkButton__ArgProps,
          internalVariantPropNames:
            PlasmicSearchCoreBookmarkButton__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSearchCoreBookmarkButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "bookmarkIconContainer") {
    func.displayName = "PlasmicSearchCoreBookmarkButton";
  } else {
    func.displayName = `PlasmicSearchCoreBookmarkButton.${nodeName}`;
  }
  return func;
}

export const PlasmicSearchCoreBookmarkButton = Object.assign(
  // Top-level PlasmicSearchCoreBookmarkButton renders the root element
  makeNodeComponent("bookmarkIconContainer"),
  {
    // Helper components rendering sub-elements
    bookmarkIcon: makeNodeComponent("bookmarkIcon"),

    // Metadata about props expected for PlasmicSearchCoreBookmarkButton
    internalVariantProps: PlasmicSearchCoreBookmarkButton__VariantProps,
    internalArgProps: PlasmicSearchCoreBookmarkButton__ArgProps
  }
);

export default PlasmicSearchCoreBookmarkButton;
/* prettier-ignore-end */
