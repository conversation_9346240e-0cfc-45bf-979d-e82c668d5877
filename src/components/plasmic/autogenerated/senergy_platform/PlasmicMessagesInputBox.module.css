.formattingContainer {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: flex-start;
  background: var(--token-xo_r2w5pebq-);
  min-width: 0;
  padding: 16px 20px var(--token-4Wrp9mDZCSCQ);
  border: 1px solid var(--token-p09LDPmbF81_);
}
.textEntryContainer {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  border: 1px solid var(--token-p09LDPmbF81_);
}
.messageInput {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  padding-left: 4px;
  padding-top: 4px;
  padding-right: 4px;
  cursor: text;
  min-width: 0;
  border-width: 1px;
  border-style: none;
}
.sendAndFileIcons {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: flex-end;
  justify-content: space-between;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: 0px var(--token-4Wrp9mDZCSCQ) var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
.svg__qYzAd {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  flex-shrink: 0;
}
.subButton:global(.__wab_instance) {
  max-width: 100%;
}
.svg__fL1G {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__eP5Ux {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.text {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-agfFfkxgxH6d);
  text-align: right;
  font-family: var(--token-z1yrQVi72Nj1);
  opacity: 0;
  padding-top: 4px;
  min-width: 0;
}
.textactive {
  opacity: 1;
}
