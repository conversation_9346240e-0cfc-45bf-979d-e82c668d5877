/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: LK4I3Kl7_-vt

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>iceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileTileCaseStudyTile from "../../ProfileTileCaseStudyTile"; // plasmic-import: msZSdug4VrvS/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileMyWorks.module.css"; // plasmic-import: LK4I3Kl7_-vt/css

createPlasmicElementProxy;

export type PlasmicProfileMyWorks__VariantMembers = {};
export type PlasmicProfileMyWorks__VariantsArgs = {};
type VariantPropType = keyof PlasmicProfileMyWorks__VariantsArgs;
export const PlasmicProfileMyWorks__VariantProps = new Array<VariantPropType>();

export type PlasmicProfileMyWorks__ArgsType = {};
type ArgPropType = keyof PlasmicProfileMyWorks__ArgsType;
export const PlasmicProfileMyWorks__ArgProps = new Array<ArgPropType>();

export type PlasmicProfileMyWorks__OverridesType = {
  root?: Flex__<"div">;
};

export interface DefaultProfileMyWorksProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileMyWorks__RenderFunc(props: {
  variants: PlasmicProfileMyWorks__VariantsArgs;
  args: PlasmicProfileMyWorks__ArgsType;
  overrides: PlasmicProfileMyWorks__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <ProfileTileCaseStudyTile
        className={classNames(
          "__wab_instance",
          sty.profileTileCaseStudyTile__wbr3T
        )}
        content={"photoWText"}
      />

      <ProfileTileCaseStudyTile
        className={classNames(
          "__wab_instance",
          sty.profileTileCaseStudyTile__tp4K
        )}
        content={"photoWText"}
      />

      <ProfileTileCaseStudyTile
        className={classNames(
          "__wab_instance",
          sty.profileTileCaseStudyTile___19H8C
        )}
        content={"photoWText"}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileMyWorks__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileMyWorks__VariantsArgs;
    args?: PlasmicProfileMyWorks__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileMyWorks__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileMyWorks__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileMyWorks__ArgProps,
          internalVariantPropNames: PlasmicProfileMyWorks__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileMyWorks__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicProfileMyWorks";
  } else {
    func.displayName = `PlasmicProfileMyWorks.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileMyWorks = Object.assign(
  // Top-level PlasmicProfileMyWorks renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicProfileMyWorks
    internalVariantProps: PlasmicProfileMyWorks__VariantProps,
    internalArgProps: PlasmicProfileMyWorks__ArgProps
  }
);

export default PlasmicProfileMyWorks;
/* prettier-ignore-end */
