/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: U2k0KPt8XoY6

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicMessagesDateDivider.module.css"; // plasmic-import: U2k0KPt8XoY6/css

createPlasmicElementProxy;

export type PlasmicMessagesDateDivider__VariantMembers = {};
export type PlasmicMessagesDateDivider__VariantsArgs = {};
type VariantPropType = keyof PlasmicMessagesDateDivider__VariantsArgs;
export const PlasmicMessagesDateDivider__VariantProps =
  new Array<VariantPropType>();

export type PlasmicMessagesDateDivider__ArgsType = {};
type ArgPropType = keyof PlasmicMessagesDateDivider__ArgsType;
export const PlasmicMessagesDateDivider__ArgProps = new Array<ArgPropType>();

export type PlasmicMessagesDateDivider__OverridesType = {
  formatingContainer?: Flex__<"div">;
  spacingLine?: Flex__<"section">;
  dateBox?: Flex__<"section">;
  text?: Flex__<"div">;
  accentLine?: Flex__<"section">;
};

export interface DefaultMessagesDateDividerProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicMessagesDateDivider__RenderFunc(props: {
  variants: PlasmicMessagesDateDivider__VariantsArgs;
  args: PlasmicMessagesDateDivider__ArgsType;
  overrides: PlasmicMessagesDateDivider__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"formatingContainer"}
      data-plasmic-override={overrides.formatingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formatingContainer
      )}
    >
      <section
        data-plasmic-name={"spacingLine"}
        data-plasmic-override={overrides.spacingLine}
        className={classNames(projectcss.all, sty.spacingLine)}
      />

      <section
        data-plasmic-name={"dateBox"}
        data-plasmic-override={overrides.dateBox}
        className={classNames(projectcss.all, sty.dateBox)}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text
          )}
        >
          {"Weekday, Month Day"}
        </div>
      </section>
      <section
        data-plasmic-name={"accentLine"}
        data-plasmic-override={overrides.accentLine}
        className={classNames(projectcss.all, sty.accentLine)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formatingContainer: [
    "formatingContainer",
    "spacingLine",
    "dateBox",
    "text",
    "accentLine"
  ],
  spacingLine: ["spacingLine"],
  dateBox: ["dateBox", "text"],
  text: ["text"],
  accentLine: ["accentLine"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formatingContainer: "div";
  spacingLine: "section";
  dateBox: "section";
  text: "div";
  accentLine: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicMessagesDateDivider__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicMessagesDateDivider__VariantsArgs;
    args?: PlasmicMessagesDateDivider__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicMessagesDateDivider__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicMessagesDateDivider__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicMessagesDateDivider__ArgProps,
          internalVariantPropNames: PlasmicMessagesDateDivider__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicMessagesDateDivider__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formatingContainer") {
    func.displayName = "PlasmicMessagesDateDivider";
  } else {
    func.displayName = `PlasmicMessagesDateDivider.${nodeName}`;
  }
  return func;
}

export const PlasmicMessagesDateDivider = Object.assign(
  // Top-level PlasmicMessagesDateDivider renders the root element
  makeNodeComponent("formatingContainer"),
  {
    // Helper components rendering sub-elements
    spacingLine: makeNodeComponent("spacingLine"),
    dateBox: makeNodeComponent("dateBox"),
    text: makeNodeComponent("text"),
    accentLine: makeNodeComponent("accentLine"),

    // Metadata about props expected for PlasmicMessagesDateDivider
    internalVariantProps: PlasmicMessagesDateDivider__VariantProps,
    internalArgProps: PlasmicMessagesDateDivider__ArgProps
  }
);

export default PlasmicMessagesDateDivider;
/* prettier-ignore-end */
