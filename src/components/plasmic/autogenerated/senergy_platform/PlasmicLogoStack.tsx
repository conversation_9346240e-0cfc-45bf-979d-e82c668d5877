/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: MY4cHaJaVAVx

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import { useScreenVariants as useScreenVariants_4Hrhi5G5ANwQ } from "./PlasmicGlobalVariant__FormattingBreakPoint"; // plasmic-import: 4Hrhi5G5aNwQ/globalVariant

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicLogoStack.module.css"; // plasmic-import: MY4cHaJaVAVx/css

createPlasmicElementProxy;

export type PlasmicLogoStack__VariantMembers = {};
export type PlasmicLogoStack__VariantsArgs = {};
type VariantPropType = keyof PlasmicLogoStack__VariantsArgs;
export const PlasmicLogoStack__VariantProps = new Array<VariantPropType>();

export type PlasmicLogoStack__ArgsType = {};
type ArgPropType = keyof PlasmicLogoStack__ArgsType;
export const PlasmicLogoStack__ArgProps = new Array<ArgPropType>();

export type PlasmicLogoStack__OverridesType = {
  logoStack?: Flex__<"div">;
  img?: Flex__<typeof PlasmicImg__>;
  text?: Flex__<"div">;
};

export interface DefaultLogoStackProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicLogoStack__RenderFunc(props: {
  variants: PlasmicLogoStack__VariantsArgs;
  args: PlasmicLogoStack__ArgsType;
  overrides: PlasmicLogoStack__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const globalVariants = ensureGlobalVariants({
    formattingBreakPoint: useScreenVariants_4Hrhi5G5ANwQ()
  });

  return (
    <div
      data-plasmic-name={"logoStack"}
      data-plasmic-override={overrides.logoStack}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.logoStack
      )}
    >
      <PlasmicImg__
        data-plasmic-name={"img"}
        data-plasmic-override={overrides.img}
        alt={""}
        className={classNames(sty.img)}
        displayHeight={
          hasVariant(globalVariants, "formattingBreakPoint", "mobile")
            ? "64px"
            : "77px"
        }
        displayMaxHeight={"none"}
        displayMaxWidth={"100%"}
        displayMinHeight={"0"}
        displayMinWidth={"0"}
        displayWidth={
          hasVariant(globalVariants, "formattingBreakPoint", "mobile")
            ? "64px"
            : "77px"
        }
        loading={"lazy"}
        src={{
          src: "/plasmic/senergy_platform/images/placeholderLogoSmSvg.svg",
          fullWidth: 149,
          fullHeight: 150,
          aspectRatio: 0.991071
        }}
      />

      <div
        data-plasmic-name={"text"}
        data-plasmic-override={overrides.text}
        className={classNames(projectcss.all, projectcss.__wab_text, sty.text)}
      >
        {"SENERGY\n.WORKS"}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  logoStack: ["logoStack", "img", "text"],
  img: ["img"],
  text: ["text"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  logoStack: "div";
  img: typeof PlasmicImg__;
  text: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLogoStack__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLogoStack__VariantsArgs;
    args?: PlasmicLogoStack__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLogoStack__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicLogoStack__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLogoStack__ArgProps,
          internalVariantPropNames: PlasmicLogoStack__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicLogoStack__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "logoStack") {
    func.displayName = "PlasmicLogoStack";
  } else {
    func.displayName = `PlasmicLogoStack.${nodeName}`;
  }
  return func;
}

export const PlasmicLogoStack = Object.assign(
  // Top-level PlasmicLogoStack renders the root element
  makeNodeComponent("logoStack"),
  {
    // Helper components rendering sub-elements
    img: makeNodeComponent("img"),
    text: makeNodeComponent("text"),

    // Metadata about props expected for PlasmicLogoStack
    internalVariantProps: PlasmicLogoStack__VariantProps,
    internalArgProps: PlasmicLogoStack__ArgProps
  }
);

export default PlasmicLogoStack;
/* prettier-ignore-end */
