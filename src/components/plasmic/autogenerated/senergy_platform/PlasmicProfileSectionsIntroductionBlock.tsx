/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: qfVGJMrc3gkW

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileIntroductionTile from "../../ProfileTileIntroductionTile"; // plasmic-import: ZpGWQx6DTU7R/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsIntroductionBlock.module.css"; // plasmic-import: qfVGJMrc3gkW/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsIntroductionBlock__VariantMembers = {
  overviewGridBlock: "overviewGridBlock";
  editable: "editable";
};
export type PlasmicProfileSectionsIntroductionBlock__VariantsArgs = {
  overviewGridBlock?: SingleBooleanChoiceArg<"overviewGridBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType =
  keyof PlasmicProfileSectionsIntroductionBlock__VariantsArgs;
export const PlasmicProfileSectionsIntroductionBlock__VariantProps =
  new Array<VariantPropType>("overviewGridBlock", "editable");

export type PlasmicProfileSectionsIntroductionBlock__ArgsType = {
  getProfile?: any;
};
type ArgPropType = keyof PlasmicProfileSectionsIntroductionBlock__ArgsType;
export const PlasmicProfileSectionsIntroductionBlock__ArgProps =
  new Array<ArgPropType>("getProfile");

export type PlasmicProfileSectionsIntroductionBlock__OverridesType = {
  aboutMeSection?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  text?: Flex__<"div">;
  displayContainer?: Flex__<"section">;
  profileTileIntroductionTile?: Flex__<typeof ProfileTileIntroductionTile>;
};

export interface DefaultProfileSectionsIntroductionBlockProps {
  getProfile?: any;
  overviewGridBlock?: SingleBooleanChoiceArg<"overviewGridBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsIntroductionBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsIntroductionBlock__VariantsArgs;
  args: PlasmicProfileSectionsIntroductionBlock__ArgsType;
  overrides: PlasmicProfileSectionsIntroductionBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGridBlock",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.overviewGridBlock
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"aboutMeSection"}
      data-plasmic-override={overrides.aboutMeSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.aboutMeSection,
        {
          [sty.aboutMeSectioneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.aboutMeSectionoverviewGridBlock]: hasVariant(
            $state,
            "overviewGridBlock",
            "overviewGridBlock"
          )
        }
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewGridBlock]:
              hasVariant($state, "overviewGridBlock", "overviewGridBlock")
          }
        )}
        overviewGrid={
          hasVariant($state, "overviewGridBlock", "overviewGridBlock")
            ? true
            : undefined
        }
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text,
            {
              [sty.textoverviewGridBlock]: hasVariant(
                $state,
                "overviewGridBlock",
                "overviewGridBlock"
              )
            }
          )}
        >
          {"Introduction"}
        </div>
      </ProfileSectionsProfileSectionHeading>
      <section
        data-plasmic-name={"displayContainer"}
        data-plasmic-override={overrides.displayContainer}
        className={classNames(projectcss.all, sty.displayContainer, {
          [sty.displayContaineroverviewGridBlock]: hasVariant(
            $state,
            "overviewGridBlock",
            "overviewGridBlock"
          )
        })}
      >
        <ProfileTileIntroductionTile
          data-plasmic-name={"profileTileIntroductionTile"}
          data-plasmic-override={overrides.profileTileIntroductionTile}
          className={classNames(
            "__wab_instance",
            sty.profileTileIntroductionTile,
            {
              [sty.profileTileIntroductionTileeditable]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.profileTileIntroductionTileoverviewGridBlock]: hasVariant(
                $state,
                "overviewGridBlock",
                "overviewGridBlock"
              ),
              [sty.profileTileIntroductionTileoverviewGridBlock_editable]:
                hasVariant($state, "editable", "editable") &&
                hasVariant($state, "overviewGridBlock", "overviewGridBlock")
            }
          )}
          overview={
            hasVariant($state, "overviewGridBlock", "overviewGridBlock")
              ? "introOverview"
              : undefined
          }
        />
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  aboutMeSection: [
    "aboutMeSection",
    "profileSectionsProfileSectionHeading",
    "text",
    "displayContainer",
    "profileTileIntroductionTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading",
    "text"
  ],
  text: ["text"],
  displayContainer: ["displayContainer", "profileTileIntroductionTile"],
  profileTileIntroductionTile: ["profileTileIntroductionTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  aboutMeSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  text: "div";
  displayContainer: "section";
  profileTileIntroductionTile: typeof ProfileTileIntroductionTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsIntroductionBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsIntroductionBlock__VariantsArgs;
    args?: PlasmicProfileSectionsIntroductionBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsIntroductionBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsIntroductionBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicProfileSectionsIntroductionBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsIntroductionBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsIntroductionBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "aboutMeSection") {
    func.displayName = "PlasmicProfileSectionsIntroductionBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsIntroductionBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsIntroductionBlock = Object.assign(
  // Top-level PlasmicProfileSectionsIntroductionBlock renders the root element
  makeNodeComponent("aboutMeSection"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    text: makeNodeComponent("text"),
    displayContainer: makeNodeComponent("displayContainer"),
    profileTileIntroductionTile: makeNodeComponent(
      "profileTileIntroductionTile"
    ),

    // Metadata about props expected for PlasmicProfileSectionsIntroductionBlock
    internalVariantProps: PlasmicProfileSectionsIntroductionBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsIntroductionBlock__ArgProps
  }
);

export default PlasmicProfileSectionsIntroductionBlock;
/* prettier-ignore-end */
