.root {
  display: inline-flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  justify-self: flex-start;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
  border-width: 1px;
  border-style: none;
}
.formattingContainer {
  display: flex;
  background: var(--token-K5FbAPSIIrXM);
  cursor: pointer;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  transition-property: all;
  transition-duration: 0.2s;
  row-gap: var(--token-sazGmnf7GWAk);
  min-width: 0;
  min-height: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.2s;
  border-radius: 1px;
  padding: 10px 16px;
}
.formattingContainershowStartIcon {
  padding-left: 16px;
}
.formattingContainershowEndIcon {
  padding-right: 16px;
}
.formattingContainerisDisabled {
  opacity: 0.6;
}
.formattingContainershape_rounded {
  padding-left: 20px;
  padding-right: 20px;
  min-width: 100px;
  border-radius: 999px;
}
.formattingContainershape_sharp {
  border-radius: 0px;
}
.formattingContainersize_compact {
  padding: 6px 16px;
}
.formattingContainersize_minimal {
  padding: 0px 4px;
}
.formattingContainerstyling_cameo {
  background: var(--token-MPvkQ1rq8Due);
}
.formattingContainerstyling_typewriter {
  background: none;
}
.formattingContainerjustification_left {
  justify-content: center;
  align-items: flex-start;
}
.formattingContainerjustification_right {
  align-items: flex-end;
  justify-content: center;
}
.formattingContainerselected {
  display: flex;
}
.formattingContainercolor_eggplant {
  background: var(--token-v4jufhOu3lt9);
}
.formattingContainercolor_forest {
  background: var(--token-XQQdLIBrONt7);
}
.formattingContainercolor_sage {
  background: var(--token-lUfRIntGAfk9);
}
.formattingContainercolor_sunflower {
  background: var(--token-cXZCUAmMzuNV);
}
.formattingContainerdisabledAnimation_disabled {
  opacity: 0.6;
}
.formattingContainerdisabledAnimation_shakeLeft {
  opacity: 0.6;
  transform: translateX(-5px) translateY(0px) translateZ(0px);
}
.formattingContainerdisabledAnimation_shakeRight {
  opacity: 0.6;
  transform: translateX(5px) translateY(0px) translateZ(0px);
}
.formattingContainerdisabledAnimation_disabledText {
  opacity: 0.6;
}
.formattingContainershape_rounded_showStartIcon {
  padding-left: 16px;
}
.formattingContainershowEndIcon_shape_rounded {
  padding-right: 16px;
}
.root:hover .formattingContainer {
  background: #3d3d3d;
}
.root:active .formattingContainer {
  background: #3d3d3d;
}
.contentContainer {
  display: flex;
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  left: auto;
  top: auto;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.contentContainerselected {
  justify-content: center;
  align-items: center;
  width: auto;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  row-gap: 0px;
}
.startIconContainer {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  position: relative;
  left: auto;
  top: auto;
  bottom: auto;
  right: auto;
}
.startIconContainershowEndIcon {
  display: none;
}
.slotTargetStartIcon {
  color: #ededec;
  user-select: none;
}
.svg___18VnU {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svgshowEndIcon___18VnU59Ind {
  display: none;
}
.text {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  left: auto;
  top: auto;
  width: auto;
}
.textselected {
  width: auto;
}
.slotTargetChildren {
  color: var(--token-xo_r2w5pebq-);
  font-weight: 400;
  white-space: pre;
  text-align: center;
  user-select: none;
}
.slotTargetChildrenstyling_cameo {
  color: var(--token-K5FbAPSIIrXM);
  font-family: var(--token-z1yrQVi72Nj1);
  font-weight: 700;
}
.slotTargetChildrenstyling_typewriter {
  font-family: var(--token-z1yrQVi72Nj1);
  color: var(--token-K5FbAPSIIrXM);
  font-weight: 400;
}
.slotTargetChildrenstyling_nittiWColor {
  font-family: var(--token-z1yrQVi72Nj1);
}
.slotTargetChildrenstyling_nittiWColor_selected {
  font-weight: 400;
}
.root:hover .slotTargetChildren {
  color: var(--token-xo_r2w5pebq-);
}
.root:hover .slotTargetChildren > :global(.__wab_text),
.root:hover .slotTargetChildren > :global(.__wab_expr_html_text),
.root:hover .slotTargetChildren > :global(.__wab_slot-string-wrapper),
.root:hover .slotTargetChildren > :global(.__wab_slot) > :global(.__wab_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_expr_html_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot-string-wrapper),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_expr_html_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot-string-wrapper),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_expr_html_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot-string-wrapper) {
  text-decoration-line: none;
}
.endIconContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  left: auto;
  top: auto;
  bottom: auto;
  right: auto;
}
.endIconContainershowEndIcon {
  display: flex;
}
.slotTargetEndIcon {
  color: #ededec;
  user-select: none;
}
.svg__jMo3E {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.underline {
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: auto;
  left: auto;
  bottom: auto;
  right: auto;
  top: auto;
  z-index: 1;
  height: 1px;
  background: var(--token-xo_r2w5pebq-);
  margin-bottom: -1px;
  flex-shrink: 0;
  display: none;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.underline > * {
  grid-column: 4;
}
.underlinesize_compact > * {
  grid-column: 4;
}
.underlineselected {
  left: auto;
  top: auto;
  bottom: auto;
  right: auto;
  width: 100%;
  display: block;
  min-width: 0;
}
.underlineselected > * {
  grid-column: 4;
}
.underlinedisabledAnimation_shakeLeft > * {
  grid-column: 4;
}
.underlinestyling_typewriter_selected {
  background: var(--token-K5FbAPSIIrXM);
}
.underlinestyling_typewriter_selected > * {
  grid-column: 4;
}
.disabledText {
  position: absolute;
  color: var(--token-3OLw18f8JRN3);
  text-align: center;
  top: 45px;
  z-index: 1;
  height: auto;
  transform: none;
  width: auto;
  display: none;
}
.disabledTextsize_compact {
  top: 36px;
  display: none;
}
.disabledTextsize_minimal {
  top: 25px;
  display: none;
}
.disabledTextdisabledAnimation_disabled {
  display: none;
}
.disabledTextdisabledAnimation_shakeLeft {
  display: block;
}
.disabledTextdisabledAnimation_shakeRight {
  display: block;
}
.disabledTextdisabledAnimation_disabledText {
  display: block;
}
