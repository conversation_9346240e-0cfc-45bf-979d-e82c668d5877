/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: kKAHWOIWX-5u

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentDeleteButton from "../../SubcomponentDeleteButton"; // plasmic-import: WMfLdq1qoZyo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTilePublicationTile.module.css"; // plasmic-import: kKAHWOIWX-5u/css

import PublicationBookIcon from "./icons/PlasmicIcon__PublicationBook"; // plasmic-import: 1vlHySSOKWkP/icon
import CalendarIcon from "./icons/PlasmicIcon__Calendar"; // plasmic-import: -xP51745fXLc/icon
import PublicationTypeIcon from "./icons/PlasmicIcon__PublicationType"; // plasmic-import: OR6AbF6nYdAn/icon
import PersonPlusAdditionalAuthorsIcon from "./icons/PlasmicIcon__PersonPlusAdditionalAuthors"; // plasmic-import: o8F8SDzVakKs/icon
import GenreIcon from "./icons/PlasmicIcon__Genre"; // plasmic-import: Y7CGwGvldN-s/icon
import IsbnIssnIcon from "./icons/PlasmicIcon__IsbnIssn"; // plasmic-import: P_52Tj-4F1hZ/icon

createPlasmicElementProxy;

export type PlasmicProfileTilePublicationTile__VariantMembers = {
  editable: "editable";
  overview: "overview";
  error: "error";
};
export type PlasmicProfileTilePublicationTile__VariantsArgs = {
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
  error?: SingleBooleanChoiceArg<"error">;
};
type VariantPropType = keyof PlasmicProfileTilePublicationTile__VariantsArgs;
export const PlasmicProfileTilePublicationTile__VariantProps =
  new Array<VariantPropType>("editable", "overview", "error");

export type PlasmicProfileTilePublicationTile__ArgsType = {
  publicationId?: string;
  titleInputValue?: string;
  publisherInputValue?: string;
  publicationDateInputValue?: string;
  isbnIssnInputValue?: string;
  additionalAuthorsInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onPublicationDateInputValueChange?: (val: string) => void;
  onPublisherInputValueChange?: (val: string) => void;
  onIsbnIssnInputValueChange?: (val: string) => void;
  onAdditionalAuthorsInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonClickStage?: number;
  onDeleteButtonClickStageChange?: (val: number) => void;
  deleteButtonDisabled?: any;
  onDeleteButtonDisabledChange?: (val: any) => void;
  deleteButtonOnClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicProfileTilePublicationTile__ArgsType;
export const PlasmicProfileTilePublicationTile__ArgProps =
  new Array<ArgPropType>(
    "publicationId",
    "titleInputValue",
    "publisherInputValue",
    "publicationDateInputValue",
    "isbnIssnInputValue",
    "additionalAuthorsInputValue",
    "descriptionInputValue",
    "onTitleInputValueChange",
    "onPublicationDateInputValueChange",
    "onPublisherInputValueChange",
    "onIsbnIssnInputValueChange",
    "onAdditionalAuthorsInputValueChange",
    "onDescriptionInputValueChange",
    "deleteButtonClickStage",
    "onDeleteButtonClickStageChange",
    "deleteButtonDisabled",
    "onDeleteButtonDisabledChange",
    "deleteButtonOnClick"
  );

export type PlasmicProfileTilePublicationTile__OverridesType = {
  publicationSpacingContainer?: Flex__<"div">;
  publicationIcon?: Flex__<"svg">;
  informationStack?: Flex__<"div">;
  titleInput?: Flex__<typeof SubcomponentTextInput>;
  publisher?: Flex__<typeof SubcomponentTextInput>;
  infoBar?: Flex__<"section">;
  publicationDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot?: Flex__<"svg">;
  typeOfPublicationSelector?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot2?: Flex__<"svg">;
  additionalAuthorsInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot3?: Flex__<"svg">;
  genreInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot4?: Flex__<"svg">;
  isbnIssnInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot6?: Flex__<"svg">;
  description?: Flex__<typeof SubcomponentTextInput>;
  subDeleteButton?: Flex__<typeof SubcomponentDeleteButton>;
};

export interface DefaultProfileTilePublicationTileProps {
  publicationId?: string;
  titleInputValue?: string;
  publisherInputValue?: string;
  publicationDateInputValue?: string;
  isbnIssnInputValue?: string;
  additionalAuthorsInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onPublicationDateInputValueChange?: (val: string) => void;
  onPublisherInputValueChange?: (val: string) => void;
  onIsbnIssnInputValueChange?: (val: string) => void;
  onAdditionalAuthorsInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonClickStage?: number;
  onDeleteButtonClickStageChange?: (val: number) => void;
  deleteButtonDisabled?: any;
  onDeleteButtonDisabledChange?: (val: any) => void;
  deleteButtonOnClick?: (event: any) => void;
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
  error?: SingleBooleanChoiceArg<"error">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTilePublicationTile__RenderFunc(props: {
  variants: PlasmicProfileTilePublicationTile__VariantsArgs;
  args: PlasmicProfileTilePublicationTile__ArgsType;
  overrides: PlasmicProfileTilePublicationTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "titleInput.value",
        type: "writable",
        variableType: "text",

        valueProp: "titleInputValue",
        onChangeProp: "onTitleInputValueChange"
      },
      {
        path: "publicationDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "publicationDateInputValue",
        onChangeProp: "onPublicationDateInputValueChange"
      },
      {
        path: "typeOfPublicationSelector.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "genreInput.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "overview",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overview
      },
      {
        path: "publisher.value",
        type: "writable",
        variableType: "text",

        valueProp: "publisherInputValue",
        onChangeProp: "onPublisherInputValueChange"
      },
      {
        path: "isbnIssnInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "isbnIssnInputValue",
        onChangeProp: "onIsbnIssnInputValueChange"
      },
      {
        path: "subDeleteButton.clickStage",
        type: "writable",
        variableType: "number",

        valueProp: "deleteButtonClickStage",
        onChangeProp: "onDeleteButtonClickStageChange"
      },
      {
        path: "subDeleteButton.disabled",
        type: "writable",
        variableType: "text",

        valueProp: "deleteButtonDisabled",
        onChangeProp: "onDeleteButtonDisabledChange"
      },
      {
        path: "additionalAuthorsInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "additionalAuthorsInputValue",
        onChangeProp: "onAdditionalAuthorsInputValueChange"
      },
      {
        path: "pendingUpdates",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => false
      },
      {
        path: "description.value",
        type: "writable",
        variableType: "text",

        valueProp: "descriptionInputValue",
        onChangeProp: "onDescriptionInputValueChange"
      },
      {
        path: "titleInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "publisher.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "description.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "error",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.error
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"publicationSpacingContainer"}
      data-plasmic-override={overrides.publicationSpacingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.publicationSpacingContainer,
        {
          [sty.publicationSpacingContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.publicationSpacingContainererror]: hasVariant(
            $state,
            "error",
            "error"
          ),
          [sty.publicationSpacingContaineroverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        }
      )}
    >
      <PublicationBookIcon
        data-plasmic-name={"publicationIcon"}
        data-plasmic-override={overrides.publicationIcon}
        className={classNames(projectcss.all, sty.publicationIcon, {
          [sty.publicationIconoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
        role={"img"}
      />

      <div
        data-plasmic-name={"informationStack"}
        data-plasmic-override={overrides.informationStack}
        className={classNames(projectcss.all, sty.informationStack, {
          [sty.informationStackeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.informationStackoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
      >
        <SubcomponentTextInput
          data-plasmic-name={"titleInput"}
          data-plasmic-override={overrides.titleInput}
          className={classNames("__wab_instance", sty.titleInput, {
            [sty.titleInputeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.titleInputoverview]: hasVariant($state, "overview", "overview")
          })}
          displayText={(() => {
            try {
              return $state.titleInput.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"heading3"}
          errorMessage={generateStateValueProp($state, [
            "titleInput",
            "errorMessage"
          ])}
          fieldNameRemainVisible={
            hasVariant($state, "editable", "editable") ? true : false
          }
          inputHoverText={"Publication Title"}
          inputName={"Publication Title"}
          inputNameAsPlaceholder={false}
          inputPlaceholder={"A comprehensive review"}
          inputValue={generateStateValueProp($state, ["titleInput", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "titleInput",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["titleInput", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        <SubcomponentTextInput
          data-plasmic-name={"publisher"}
          data-plasmic-override={overrides.publisher}
          className={classNames("__wab_instance", sty.publisher, {
            [sty.publishereditable]: hasVariant($state, "editable", "editable"),
            [sty.publisheroverview]: hasVariant($state, "overview", "overview")
          })}
          displayText={(() => {
            try {
              return $state.publisher.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"subHeading"}
          errorMessage={generateStateValueProp($state, [
            "publisher",
            "errorMessage"
          ])}
          fieldNameRemainVisible={
            hasVariant($state, "editable", "editable") ? true : false
          }
          inputHoverText={"Publisher"}
          inputName={"Publisher"}
          inputNameAsPlaceholder={false}
          inputPlaceholder={"American Medical Journal"}
          inputValue={generateStateValueProp($state, ["publisher", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "publisher",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["publisher", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        <section
          data-plasmic-name={"infoBar"}
          data-plasmic-override={overrides.infoBar}
          className={classNames(projectcss.all, sty.infoBar, {
            [sty.infoBareditable]: hasVariant($state, "editable", "editable"),
            [sty.infoBaroverview]: hasVariant($state, "overview", "overview")
          })}
        >
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.publicationDateInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"publicationDateInput"}
              data-plasmic-override={overrides.publicationDateInput}
              className={classNames(
                "__wab_instance",
                sty.publicationDateInput,
                {
                  [sty.publicationDateInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.publicationDateInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.publicationDateInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <CalendarIcon
                  data-plasmic-name={"iconSpot"}
                  data-plasmic-override={overrides.iconSpot}
                  className={classNames(projectcss.all, sty.iconSpot)}
                  role={"img"}
                />
              }
              inputHoverText={"Publication Date"}
              inputName={"Publication Date"}
              inputType={"date"}
              inputValue={generateStateValueProp($state, [
                "publicationDateInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "publicationDateInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return undefined;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"typeOfPublicationSelector"}
              data-plasmic-override={overrides.typeOfPublicationSelector}
              className={classNames(
                "__wab_instance",
                sty.typeOfPublicationSelector,
                {
                  [sty.typeOfPublicationSelectoreditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.typeOfPublicationSelectoroverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return undefined;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              dropdownName={"Publication Type"}
              dropdownOptions={(() => {
                const __composite = [
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null }
                ];
                __composite["0"]["value"] = "Book";
                __composite["1"]["value"] = "Magazine";
                __composite["2"]["value"] = "Blog";
                __composite["3"]["value"] = "Academic";
                __composite["4"]["value"] = "Reference";
                __composite["5"]["value"] = "Newspaper";
                __composite["6"]["value"] = "Online";
                __composite["7"]["value"] = "Newsletter";
                __composite["8"]["value"] = "Other";
                return __composite;
              })()}
              dropdownPlaceholderText={"Publication Type"}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableSelector"
                  : undefined
              }
              iconSpot2={
                <PublicationTypeIcon
                  data-plasmic-name={"iconSpot2"}
                  data-plasmic-override={overrides.iconSpot2}
                  className={classNames(projectcss.all, sty.iconSpot2)}
                  role={"img"}
                />
              }
              inputHoverText={"Publication Type"}
              inputName={"Publication Type"}
              inputValue={generateStateValueProp($state, [
                "typeOfPublicationSelector",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "typeOfPublicationSelector",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.additionalAuthorsInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"additionalAuthorsInput"}
              data-plasmic-override={overrides.additionalAuthorsInput}
              className={classNames(
                "__wab_instance",
                sty.additionalAuthorsInput,
                {
                  [sty.additionalAuthorsInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.additionalAuthorsInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.additionalAuthorsInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <PersonPlusAdditionalAuthorsIcon
                  data-plasmic-name={"iconSpot3"}
                  data-plasmic-override={overrides.iconSpot3}
                  className={classNames(projectcss.all, sty.iconSpot3)}
                  role={"img"}
                />
              }
              inputHoverText={"Additional Authors"}
              inputName={"Additional Authors"}
              inputPlaceholder={"Ex: Steve Cyboran, Wes Rogers"}
              inputValue={generateStateValueProp($state, [
                "additionalAuthorsInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "additionalAuthorsInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return undefined;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"genreInput"}
              data-plasmic-override={overrides.genreInput}
              className={classNames("__wab_instance", sty.genreInput, {
                [sty.genreInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.genreInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={
                hasVariant($state, "editable", "editable") ? `` : undefined
              }
              dropdownName={"Genre"}
              dropdownOptions={
                hasVariant($state, "editable", "editable")
                  ? (() => {
                      const __composite = [
                        { type: "option", value: null, label: null },
                        { type: "option", value: null, label: null }
                      ];
                      __composite["0"]["value"] = "Option 1";
                      __composite["0"]["label"] = "Option 1";
                      __composite["1"]["value"] = "Option 2";
                      __composite["1"]["label"] = "Option 2";
                      return __composite;
                    })()
                  : undefined
              }
              dropdownPlaceholderText={"Genre"}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableSelector"
                  : undefined
              }
              iconSpot2={
                <GenreIcon
                  data-plasmic-name={"iconSpot4"}
                  data-plasmic-override={overrides.iconSpot4}
                  className={classNames(projectcss.all, sty.iconSpot4)}
                  role={"img"}
                />
              }
              inputName={"Genre"}
              inputPlaceholder={"Genre"}
              inputValue={generateStateValueProp($state, [
                "genreInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "genreInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.isbnIssnInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"isbnIssnInput"}
              data-plasmic-override={overrides.isbnIssnInput}
              className={classNames("__wab_instance", sty.isbnIssnInput, {
                [sty.isbnIssnInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.isbnIssnInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.isbnIssnInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <IsbnIssnIcon
                  data-plasmic-name={"iconSpot6"}
                  data-plasmic-override={overrides.iconSpot6}
                  className={classNames(projectcss.all, sty.iconSpot6)}
                  role={"img"}
                />
              }
              inputHoverText={"ISBN \u2014 ISSN"}
              inputName={"ISBN \u2014 ISSN"}
              inputPlaceholder={"Ex: 0264-3596"}
              inputValue={generateStateValueProp($state, [
                "isbnIssnInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "isbnIssnInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
        </section>
        <SubcomponentTextInput
          data-plasmic-name={"description"}
          data-plasmic-override={overrides.description}
          className={classNames("__wab_instance", sty.description, {
            [sty.descriptioneditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.descriptionoverview]: hasVariant(
              $state,
              "overview",
              "overview"
            )
          })}
          displayText={
            hasVariant($state, "overview", "overview")
              ? (() => {
                  try {
                    return $state.description.value.length > 160
                      ? $state.description.value.substring(0, 160) + "..."
                      : $state.description.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
              : (() => {
                  try {
                    return $state.description.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
          }
          errorMessage={generateStateValueProp($state, [
            "description",
            "errorMessage"
          ])}
          inputHoverText={
            hasVariant($state, "editable", "editable")
              ? "Publication Summary"
              : undefined
          }
          inputName={
            hasVariant($state, "editable", "editable")
              ? "Publication Summary"
              : undefined
          }
          inputPlaceholder={
            hasVariant($state, "editable", "editable")
              ? "Publication Summary"
              : undefined
          }
          inputValue={generateStateValueProp($state, ["description", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "description",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["description", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />
      </div>
      <SubcomponentDeleteButton
        data-plasmic-name={"subDeleteButton"}
        data-plasmic-override={overrides.subDeleteButton}
        className={classNames("__wab_instance", sty.subDeleteButton, {
          [sty.subDeleteButtoneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
        onClick={args.deleteButtonOnClick}
        onClickStageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "clickStage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDisabledChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "disabled"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  publicationSpacingContainer: [
    "publicationSpacingContainer",
    "publicationIcon",
    "informationStack",
    "titleInput",
    "publisher",
    "infoBar",
    "publicationDateInput",
    "iconSpot",
    "typeOfPublicationSelector",
    "iconSpot2",
    "additionalAuthorsInput",
    "iconSpot3",
    "genreInput",
    "iconSpot4",
    "isbnIssnInput",
    "iconSpot6",
    "description",
    "subDeleteButton"
  ],
  publicationIcon: ["publicationIcon"],
  informationStack: [
    "informationStack",
    "titleInput",
    "publisher",
    "infoBar",
    "publicationDateInput",
    "iconSpot",
    "typeOfPublicationSelector",
    "iconSpot2",
    "additionalAuthorsInput",
    "iconSpot3",
    "genreInput",
    "iconSpot4",
    "isbnIssnInput",
    "iconSpot6",
    "description"
  ],
  titleInput: ["titleInput"],
  publisher: ["publisher"],
  infoBar: [
    "infoBar",
    "publicationDateInput",
    "iconSpot",
    "typeOfPublicationSelector",
    "iconSpot2",
    "additionalAuthorsInput",
    "iconSpot3",
    "genreInput",
    "iconSpot4",
    "isbnIssnInput",
    "iconSpot6"
  ],
  publicationDateInput: ["publicationDateInput", "iconSpot"],
  iconSpot: ["iconSpot"],
  typeOfPublicationSelector: ["typeOfPublicationSelector", "iconSpot2"],
  iconSpot2: ["iconSpot2"],
  additionalAuthorsInput: ["additionalAuthorsInput", "iconSpot3"],
  iconSpot3: ["iconSpot3"],
  genreInput: ["genreInput", "iconSpot4"],
  iconSpot4: ["iconSpot4"],
  isbnIssnInput: ["isbnIssnInput", "iconSpot6"],
  iconSpot6: ["iconSpot6"],
  description: ["description"],
  subDeleteButton: ["subDeleteButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  publicationSpacingContainer: "div";
  publicationIcon: "svg";
  informationStack: "div";
  titleInput: typeof SubcomponentTextInput;
  publisher: typeof SubcomponentTextInput;
  infoBar: "section";
  publicationDateInput: typeof SubcomponentIconWithText;
  iconSpot: "svg";
  typeOfPublicationSelector: typeof SubcomponentIconWithText;
  iconSpot2: "svg";
  additionalAuthorsInput: typeof SubcomponentIconWithText;
  iconSpot3: "svg";
  genreInput: typeof SubcomponentIconWithText;
  iconSpot4: "svg";
  isbnIssnInput: typeof SubcomponentIconWithText;
  iconSpot6: "svg";
  description: typeof SubcomponentTextInput;
  subDeleteButton: typeof SubcomponentDeleteButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTilePublicationTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTilePublicationTile__VariantsArgs;
    args?: PlasmicProfileTilePublicationTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTilePublicationTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTilePublicationTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTilePublicationTile__ArgProps,
          internalVariantPropNames:
            PlasmicProfileTilePublicationTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTilePublicationTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "publicationSpacingContainer") {
    func.displayName = "PlasmicProfileTilePublicationTile";
  } else {
    func.displayName = `PlasmicProfileTilePublicationTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTilePublicationTile = Object.assign(
  // Top-level PlasmicProfileTilePublicationTile renders the root element
  makeNodeComponent("publicationSpacingContainer"),
  {
    // Helper components rendering sub-elements
    publicationIcon: makeNodeComponent("publicationIcon"),
    informationStack: makeNodeComponent("informationStack"),
    titleInput: makeNodeComponent("titleInput"),
    publisher: makeNodeComponent("publisher"),
    infoBar: makeNodeComponent("infoBar"),
    publicationDateInput: makeNodeComponent("publicationDateInput"),
    iconSpot: makeNodeComponent("iconSpot"),
    typeOfPublicationSelector: makeNodeComponent("typeOfPublicationSelector"),
    iconSpot2: makeNodeComponent("iconSpot2"),
    additionalAuthorsInput: makeNodeComponent("additionalAuthorsInput"),
    iconSpot3: makeNodeComponent("iconSpot3"),
    genreInput: makeNodeComponent("genreInput"),
    iconSpot4: makeNodeComponent("iconSpot4"),
    isbnIssnInput: makeNodeComponent("isbnIssnInput"),
    iconSpot6: makeNodeComponent("iconSpot6"),
    description: makeNodeComponent("description"),
    subDeleteButton: makeNodeComponent("subDeleteButton"),

    // Metadata about props expected for PlasmicProfileTilePublicationTile
    internalVariantProps: PlasmicProfileTilePublicationTile__VariantProps,
    internalArgProps: PlasmicProfileTilePublicationTile__ArgProps
  }
);

export default PlasmicProfileTilePublicationTile;
/* prettier-ignore-end */
