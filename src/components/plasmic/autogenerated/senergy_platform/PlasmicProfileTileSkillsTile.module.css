.skillsFormattingBox {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  flex-shrink: 0;
  height: 100%;
  margin-bottom: 12px;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  margin-top: 4px;
  margin-left: 4px;
  row-gap: 0px;
  position: relative;
  min-width: 0;
  min-height: 0;
  padding: 16px 16px 8px 0px;
}
.skillsFormattingBoxoverviewGrid {
  height: auto;
  width: auto;
  justify-self: flex-start;
  display: inline-flex;
  padding: var(--token-sazGmnf7GWAk);
}
.skillsContent {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.text {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.textoverviewGrid {
  display: none;
}
