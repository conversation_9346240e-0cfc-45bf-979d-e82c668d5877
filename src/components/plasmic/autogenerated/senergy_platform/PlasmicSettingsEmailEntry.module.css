.root {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  background: var(--token-F0-tInDly4PE);
  position: relative;
  min-width: 0;
  padding: 0px var(--token-4Wrp9mDZCSCQ);
}
.freeBox___5BRu {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox__gqmMu {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  margin: 8px;
}
.text {
  width: auto;
  height: auto;
  max-width: 100%;
}
.primaryTag:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.primaryTagprimary:global(.__wab_instance) {
  display: flex;
}
.iconSpot {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.verifiedTag:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.verifiedTagverified:global(.__wab_instance) {
  display: flex;
}
.iconSpot2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
