/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: Jugl-x2pKrbY

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicWipStatusBar.module.css"; // plasmic-import: Jugl-x2pKrbY/css

import ToggleBoxIcon from "./icons/PlasmicIcon__ToggleBox"; // plasmic-import: VeGBlXIH6Sji/icon

createPlasmicElementProxy;

export type PlasmicWipStatusBar__VariantMembers = {
  progress:
    | "profileInfo"
    | "yourExperience"
    | "buildingProfile"
    | "exploration";
};
export type PlasmicWipStatusBar__VariantsArgs = {
  progress?: SingleChoiceArg<
    "profileInfo" | "yourExperience" | "buildingProfile" | "exploration"
  >;
};
type VariantPropType = keyof PlasmicWipStatusBar__VariantsArgs;
export const PlasmicWipStatusBar__VariantProps = new Array<VariantPropType>(
  "progress"
);

export type PlasmicWipStatusBar__ArgsType = {};
type ArgPropType = keyof PlasmicWipStatusBar__ArgsType;
export const PlasmicWipStatusBar__ArgProps = new Array<ArgPropType>();

export type PlasmicWipStatusBar__OverridesType = {
  statusFormattingContainer?: Flex__<"div">;
  personalInfoStack?: Flex__<"div">;
  experienceInfoStack?: Flex__<"div">;
  profileInfoStack?: Flex__<"div">;
  explorationInfoStack?: Flex__<"div">;
};

export interface DefaultWipStatusBarProps {
  progress?: SingleChoiceArg<
    "profileInfo" | "yourExperience" | "buildingProfile" | "exploration"
  >;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicWipStatusBar__RenderFunc(props: {
  variants: PlasmicWipStatusBar__VariantsArgs;
  args: PlasmicWipStatusBar__ArgsType;
  overrides: PlasmicWipStatusBar__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "progress",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.progress
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"statusFormattingContainer"}
      data-plasmic-override={overrides.statusFormattingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.statusFormattingContainer
      )}
    >
      <div
        data-plasmic-name={"personalInfoStack"}
        data-plasmic-override={overrides.personalInfoStack}
        className={classNames(projectcss.all, sty.personalInfoStack)}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__sddkz
          )}
        >
          {"Usage"}
        </div>
        <ToggleBoxIcon
          className={classNames(projectcss.all, sty.svg__dPnI, {
            [sty.svgprogress_profileInfo__dPnIyyhiu]: hasVariant(
              $state,
              "progress",
              "profileInfo"
            )
          })}
          role={"img"}
        />
      </div>
      <svg
        className={classNames(projectcss.all, sty.svg___5Ld6X)}
        role={"img"}
      />

      <div
        data-plasmic-name={"experienceInfoStack"}
        data-plasmic-override={overrides.experienceInfoStack}
        className={classNames(projectcss.all, sty.experienceInfoStack)}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text___7Yeja
          )}
        >
          {"Banner"}
        </div>
        <ToggleBoxIcon
          className={classNames(projectcss.all, sty.svg__zdWuz, {
            [sty.svgprogress_yourExperience__zdWuzPkLJ]: hasVariant(
              $state,
              "progress",
              "yourExperience"
            )
          })}
          role={"img"}
        />
      </div>
      <svg
        className={classNames(projectcss.all, sty.svg__hTmMr)}
        role={"img"}
      />

      <div
        data-plasmic-name={"profileInfoStack"}
        data-plasmic-override={overrides.profileInfoStack}
        className={classNames(projectcss.all, sty.profileInfoStack)}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__t24Vw
          )}
        >
          {"About"}
        </div>
        <ToggleBoxIcon
          className={classNames(projectcss.all, sty.svg__htraK, {
            [sty.svgprogress_buildingProfile__htraK3ITyF]: hasVariant(
              $state,
              "progress",
              "buildingProfile"
            )
          })}
          role={"img"}
        />
      </div>
      <svg
        className={classNames(projectcss.all, sty.svg__qsaRj)}
        role={"img"}
      />

      <div
        data-plasmic-name={"explorationInfoStack"}
        data-plasmic-override={overrides.explorationInfoStack}
        className={classNames(projectcss.all, sty.explorationInfoStack)}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__f1XqC
          )}
        >
          {"Profile"}
        </div>
        <ToggleBoxIcon
          className={classNames(projectcss.all, sty.svg__bJq5T, {
            [sty.svgprogress_exploration__bJq5TKp9Xk]: hasVariant(
              $state,
              "progress",
              "exploration"
            )
          })}
          role={"img"}
        />
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  statusFormattingContainer: [
    "statusFormattingContainer",
    "personalInfoStack",
    "experienceInfoStack",
    "profileInfoStack",
    "explorationInfoStack"
  ],
  personalInfoStack: ["personalInfoStack"],
  experienceInfoStack: ["experienceInfoStack"],
  profileInfoStack: ["profileInfoStack"],
  explorationInfoStack: ["explorationInfoStack"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  statusFormattingContainer: "div";
  personalInfoStack: "div";
  experienceInfoStack: "div";
  profileInfoStack: "div";
  explorationInfoStack: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicWipStatusBar__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicWipStatusBar__VariantsArgs;
    args?: PlasmicWipStatusBar__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicWipStatusBar__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicWipStatusBar__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicWipStatusBar__ArgProps,
          internalVariantPropNames: PlasmicWipStatusBar__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicWipStatusBar__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "statusFormattingContainer") {
    func.displayName = "PlasmicWipStatusBar";
  } else {
    func.displayName = `PlasmicWipStatusBar.${nodeName}`;
  }
  return func;
}

export const PlasmicWipStatusBar = Object.assign(
  // Top-level PlasmicWipStatusBar renders the root element
  makeNodeComponent("statusFormattingContainer"),
  {
    // Helper components rendering sub-elements
    personalInfoStack: makeNodeComponent("personalInfoStack"),
    experienceInfoStack: makeNodeComponent("experienceInfoStack"),
    profileInfoStack: makeNodeComponent("profileInfoStack"),
    explorationInfoStack: makeNodeComponent("explorationInfoStack"),

    // Metadata about props expected for PlasmicWipStatusBar
    internalVariantProps: PlasmicWipStatusBar__VariantProps,
    internalArgProps: PlasmicWipStatusBar__ArgProps
  }
);

export default PlasmicWipStatusBar;
/* prettier-ignore-end */
