.pageBase {
  display: flex;
  position: relative;
  height: 100vh;
  flex-direction: column;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: var(--token-F0-tInDly4PE);
  min-width: 0;
  padding: 0px;
}
.main {
  display: flex;
  position: relative;
  width: 100%;
  min-height: 200px;
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-left: 0;
  padding-right: 0;
  min-width: 0;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  top: auto;
  left: auto;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
@media (max-width: 480px) {
  .freeBox {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    width: auto;
    row-gap: var(--token-4Wrp9mDZCSCQ);
    column-gap: 0px;
  }
}
.h1__ozCot {
  width: auto;
  height: auto;
  max-width: 100%;
  text-align: center;
  letter-spacing: 5px;
  color: var(--token-K5FbAPSIIrXM);
  font-weight: 400;
  font-family: var(--token-z1yrQVi72Nj1);
  padding: var(--token-4Wrp9mDZCSCQ) 0px var(--token-4Wrp9mDZCSCQ)
    var(--token-4Wrp9mDZCSCQ);
}
@media (max-width: 480px) {
  .h1__ozCot {
    font-size: var(--token-hKlnSDJVAhUx);
  }
}
.h1__xGNvT {
  width: auto;
  height: auto;
  max-width: 100%;
  text-align: right;
  letter-spacing: 5px;
  color: var(--token-K5FbAPSIIrXM);
  font-weight: 400;
  font-family: var(--token-z1yrQVi72Nj1);
  left: auto;
  top: auto;
  position: relative;
  padding: var(--token-4Wrp9mDZCSCQ) var(--token-4Wrp9mDZCSCQ)
    var(--token-4Wrp9mDZCSCQ) 0px;
}
@media (max-width: 480px) {
  .h1__xGNvT {
    font-size: var(--token-hKlnSDJVAhUx);
    left: auto;
    top: auto;
  }
}
.h2 {
  position: relative;
  font-family: var(--token-z1yrQVi72Nj1);
  text-align: center;
  color: var(--token-v4jufhOu3lt9);
  font-weight: 700;
  transform: rotateX(0deg) rotateY(0deg) rotateZ(-2deg);
  left: auto;
  top: auto;
  padding: var(--token-4Wrp9mDZCSCQ);
}
@media (max-width: 480px) {
  .h2 {
    font-size: var(--token-hZBgG8kTaig3);
    left: auto;
    top: auto;
  }
}
.join:global(.__wab_instance) {
  max-width: 100%;
  position: fixed;
  left: auto;
  z-index: 1;
  transform: scaleX(1.4) scaleY(1.4) scaleZ(1);
  right: auto;
  bottom: 48px;
  flex-shrink: 0;
  margin: 24px;
}
@media (max-width: 1200px) {
  .join:global(.__wab_instance) {
    flex-shrink: 0;
  }
}
