/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: pRxYe0rDyQBY

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>ice<PERSON>rg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentSlotHeading.module.css"; // plasmic-import: pRxYe0rDyQBY/css

createPlasmicElementProxy;

export type PlasmicSubcomponentSlotHeading__VariantMembers = {};
export type PlasmicSubcomponentSlotHeading__VariantsArgs = {};
type VariantPropType = keyof PlasmicSubcomponentSlotHeading__VariantsArgs;
export const PlasmicSubcomponentSlotHeading__VariantProps =
  new Array<VariantPropType>();

export type PlasmicSubcomponentSlotHeading__ArgsType = {
  headingTitle?: React.ReactNode;
};
type ArgPropType = keyof PlasmicSubcomponentSlotHeading__ArgsType;
export const PlasmicSubcomponentSlotHeading__ArgProps = new Array<ArgPropType>(
  "headingTitle"
);

export type PlasmicSubcomponentSlotHeading__OverridesType = {
  headingContainer?: Flex__<"section">;
  dividerAndSpacer?: Flex__<"section">;
};

export interface DefaultSubcomponentSlotHeadingProps {
  headingTitle?: React.ReactNode;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentSlotHeading__RenderFunc(props: {
  variants: PlasmicSubcomponentSlotHeading__VariantsArgs;
  args: PlasmicSubcomponentSlotHeading__ArgsType;
  overrides: PlasmicSubcomponentSlotHeading__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <section
      data-plasmic-name={"headingContainer"}
      data-plasmic-override={overrides.headingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.headingContainer
      )}
    >
      {renderPlasmicSlot({
        defaultContents: "Overview Header",
        value: args.headingTitle,
        className: classNames(sty.slotTargetHeadingTitle)
      })}
      <section
        data-plasmic-name={"dividerAndSpacer"}
        data-plasmic-override={overrides.dividerAndSpacer}
        className={classNames(projectcss.all, sty.dividerAndSpacer)}
      />
    </section>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  headingContainer: ["headingContainer", "dividerAndSpacer"],
  dividerAndSpacer: ["dividerAndSpacer"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  headingContainer: "section";
  dividerAndSpacer: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentSlotHeading__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentSlotHeading__VariantsArgs;
    args?: PlasmicSubcomponentSlotHeading__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubcomponentSlotHeading__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentSlotHeading__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentSlotHeading__ArgProps,
          internalVariantPropNames: PlasmicSubcomponentSlotHeading__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentSlotHeading__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "headingContainer") {
    func.displayName = "PlasmicSubcomponentSlotHeading";
  } else {
    func.displayName = `PlasmicSubcomponentSlotHeading.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentSlotHeading = Object.assign(
  // Top-level PlasmicSubcomponentSlotHeading renders the root element
  makeNodeComponent("headingContainer"),
  {
    // Helper components rendering sub-elements
    dividerAndSpacer: makeNodeComponent("dividerAndSpacer"),

    // Metadata about props expected for PlasmicSubcomponentSlotHeading
    internalVariantProps: PlasmicSubcomponentSlotHeading__VariantProps,
    internalArgProps: PlasmicSubcomponentSlotHeading__ArgProps
  }
);

export default PlasmicSubcomponentSlotHeading;
/* prettier-ignore-end */
