.formattingAndShadow {
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: linear;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 298px;
  height: auto;
  justify-content: flex-start;
  align-items: flex-start;
  background: none;
  box-shadow: none;
  justify-self: flex-start;
  -webkit-transition-property: all;
  -webkit-transition-timing-function: linear;
  -webkit-transition-duration: 0.05s;
  margin: 0px;
  border-top: 0.01cm none #e4e4e7;
  border-right: 0.01cm none #e4e4e7;
  border-bottom: 0.5px solid var(--token-p09LDPmbF81_);
  border-left: 0.01cm none #e4e4e7;
}
.formattingAndShadow:hover {
  transform: none;
  background: var(--token-Ab1KZcxm-kp_);
}
.headingBar {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 300px;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  padding: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk) 4px;
  margin: var(--token-sazGmnf7GWAk);
}
.formattingAndShadow:hover .headingBar {
  width: 100%;
  min-width: 0;
}
.img {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-M1l4keX1sfKm);
  height: var(--token-M1l4keX1sfKm);
  margin-left: 8px;
  margin-top: 11px;
  margin-right: 4px;
  flex-shrink: 0;
  border-radius: 100%;
}
.img > picture > img {
  object-fit: cover;
}
.userInfo {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.text__cg2Wu {
  width: 208px;
  height: auto;
  max-width: 100%;
  font-size: var(--token-_-i82ElPHE7I);
  padding-top: 4px;
  padding-left: 0px;
  white-space: pre;
  text-overflow: ellipsis;
  overflow: hidden;
  transform: none;
  font-weight: 600;
}
.text__iow3U {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  padding-left: 0px;
  font-size: var(--token-agfFfkxgxH6d);
  min-width: 0;
}
.searchCoreBookmarkButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  height: 24px;
  width: 24px;
  flex-shrink: 0;
  margin: 10px 10px var(--token-j0qnbpah5w9U) 0px;
}
.formattingAndShadow:hover .searchCoreBookmarkButton:global(.__wab_instance) {
  flex-shrink: 0;
}
.skillsSection {
  display: flex;
  position: relative;
  width: 300px;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  column-gap: var(--token-j0qnbpah5w9U);
  padding: var(--token-4Wrp9mDZCSCQ) 16px 12px var(--token-4Wrp9mDZCSCQ);
}
.skillsRepeat {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  flex-shrink: 0;
}
