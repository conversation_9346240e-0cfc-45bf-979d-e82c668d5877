/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: G5tbmo_PlO9M

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentDeleteButton from "../../SubcomponentDeleteButton"; // plasmic-import: WMfLdq1qoZyo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileTrademarkTile.module.css"; // plasmic-import: G5tbmo_PlO9M/css

import TrademarkIcon from "./icons/PlasmicIcon__Trademark"; // plasmic-import: TJR3b86TU-Do/icon
import HashtagIcon from "./icons/PlasmicIcon__Hashtag"; // plasmic-import: 7fTFQT6QY_o8/icon
import GlobeIcon from "./icons/PlasmicIcon__Globe"; // plasmic-import: 4U-HfnBQfBTq/icon
import CalendarIcon from "./icons/PlasmicIcon__Calendar"; // plasmic-import: -xP51745fXLc/icon
import AwardIcon from "./icons/PlasmicIcon__Award"; // plasmic-import: BXxYsiTdXJr_/icon
import RegisteredIcon from "./icons/PlasmicIcon__Registered"; // plasmic-import: AA2-KUsid802/icon

createPlasmicElementProxy;

export type PlasmicProfileTileTrademarkTile__VariantMembers = {
  editable: "editable";
  overview: "overview";
};
export type PlasmicProfileTileTrademarkTile__VariantsArgs = {
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
};
type VariantPropType = keyof PlasmicProfileTileTrademarkTile__VariantsArgs;
export const PlasmicProfileTileTrademarkTile__VariantProps =
  new Array<VariantPropType>("editable", "overview");

export type PlasmicProfileTileTrademarkTile__ArgsType = {
  titleInputValue?: string;
  registrationNumberInputValue?: string;
  countryOfRegistrationInputValue?: string;
  registrationDateInputValue?: string;
  awardDateInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onRegistrationNumberInputValueChange?: (val: string) => void;
  onCountryOfRegistrationInputValueChange?: (val: string) => void;
  onRegistrationDateInputValueChange?: (val: string) => void;
  onAwardDateInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonClickStage?: number;
  onDeleteButtonClickStageChange?: (val: number) => void;
  deleteButtonDisabled?: any;
  onDeleteButtonDisabledChange?: (val: any) => void;
  trademarkId?: string;
  deleteButtonOnClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicProfileTileTrademarkTile__ArgsType;
export const PlasmicProfileTileTrademarkTile__ArgProps = new Array<ArgPropType>(
  "titleInputValue",
  "registrationNumberInputValue",
  "countryOfRegistrationInputValue",
  "registrationDateInputValue",
  "awardDateInputValue",
  "descriptionInputValue",
  "onTitleInputValueChange",
  "onRegistrationNumberInputValueChange",
  "onCountryOfRegistrationInputValueChange",
  "onRegistrationDateInputValueChange",
  "onAwardDateInputValueChange",
  "onDescriptionInputValueChange",
  "deleteButtonClickStage",
  "onDeleteButtonClickStageChange",
  "deleteButtonDisabled",
  "onDeleteButtonDisabledChange",
  "trademarkId",
  "deleteButtonOnClick"
);

export type PlasmicProfileTileTrademarkTile__OverridesType = {
  trademarkSpacingContainer?: Flex__<"div">;
  trademarkIcon?: Flex__<"svg">;
  informationStack?: Flex__<"div">;
  titleInput?: Flex__<typeof SubcomponentTextInput>;
  infoBar?: Flex__<"section">;
  registrationNumberInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot3?: Flex__<"svg">;
  countryOfRegistrationInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot?: Flex__<"svg">;
  registrationDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot2?: Flex__<"svg">;
  awardDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot6?: Flex__<"svg">;
  registrationStatusSelector?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot4?: Flex__<"svg">;
  description?: Flex__<typeof SubcomponentTextInput>;
  subDeleteButton?: Flex__<typeof SubcomponentDeleteButton>;
};

export interface DefaultProfileTileTrademarkTileProps {
  titleInputValue?: string;
  registrationNumberInputValue?: string;
  countryOfRegistrationInputValue?: string;
  registrationDateInputValue?: string;
  awardDateInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onRegistrationNumberInputValueChange?: (val: string) => void;
  onCountryOfRegistrationInputValueChange?: (val: string) => void;
  onRegistrationDateInputValueChange?: (val: string) => void;
  onAwardDateInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonClickStage?: number;
  onDeleteButtonClickStageChange?: (val: number) => void;
  deleteButtonDisabled?: any;
  onDeleteButtonDisabledChange?: (val: any) => void;
  trademarkId?: string;
  deleteButtonOnClick?: (event: any) => void;
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileTrademarkTile__RenderFunc(props: {
  variants: PlasmicProfileTileTrademarkTile__VariantsArgs;
  args: PlasmicProfileTileTrademarkTile__ArgsType;
  overrides: PlasmicProfileTileTrademarkTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "titleInput.value",
        type: "writable",
        variableType: "text",

        valueProp: "titleInputValue",
        onChangeProp: "onTitleInputValueChange"
      },
      {
        path: "countryOfRegistrationInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "countryOfRegistrationInputValue",
        onChangeProp: "onCountryOfRegistrationInputValueChange"
      },
      {
        path: "registrationDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "registrationDateInputValue",
        onChangeProp: "onRegistrationDateInputValueChange"
      },
      {
        path: "registrationNumberInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "registrationNumberInputValue",
        onChangeProp: "onRegistrationNumberInputValueChange"
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "overview",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overview
      },
      {
        path: "registrationStatusSelector.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "awardDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "awardDateInputValue",
        onChangeProp: "onAwardDateInputValueChange"
      },
      {
        path: "subDeleteButton.clickStage",
        type: "writable",
        variableType: "number",

        valueProp: "deleteButtonClickStage",
        onChangeProp: "onDeleteButtonClickStageChange"
      },
      {
        path: "subDeleteButton.disabled",
        type: "writable",
        variableType: "text",

        valueProp: "deleteButtonDisabled",
        onChangeProp: "onDeleteButtonDisabledChange"
      },
      {
        path: "pendingUpdate",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => false
      },
      {
        path: "description.value",
        type: "writable",
        variableType: "text",

        valueProp: "descriptionInputValue",
        onChangeProp: "onDescriptionInputValueChange"
      },
      {
        path: "titleInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "description.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"trademarkSpacingContainer"}
      data-plasmic-override={overrides.trademarkSpacingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.trademarkSpacingContainer,
        {
          [sty.trademarkSpacingContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.trademarkSpacingContaineroverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        }
      )}
    >
      <TrademarkIcon
        data-plasmic-name={"trademarkIcon"}
        data-plasmic-override={overrides.trademarkIcon}
        className={classNames(projectcss.all, sty.trademarkIcon, {
          [sty.trademarkIconeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.trademarkIconoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
        role={"img"}
      />

      <div
        data-plasmic-name={"informationStack"}
        data-plasmic-override={overrides.informationStack}
        className={classNames(projectcss.all, sty.informationStack, {
          [sty.informationStackoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
      >
        <SubcomponentTextInput
          data-plasmic-name={"titleInput"}
          data-plasmic-override={overrides.titleInput}
          className={classNames("__wab_instance", sty.titleInput, {
            [sty.titleInputeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.titleInputoverview]: hasVariant($state, "overview", "overview")
          })}
          displayText={(() => {
            try {
              return $state.titleInput.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"heading3"}
          errorMessage={generateStateValueProp($state, [
            "titleInput",
            "errorMessage"
          ])}
          inputHoverText={"Trademark Name"}
          inputName={"Trademark Name"}
          inputPlaceholder={"Trademark Name"}
          inputValue={generateStateValueProp($state, ["titleInput", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "titleInput",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["titleInput", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        <section
          data-plasmic-name={"infoBar"}
          data-plasmic-override={overrides.infoBar}
          className={classNames(projectcss.all, sty.infoBar, {
            [sty.infoBareditable]: hasVariant($state, "editable", "editable"),
            [sty.infoBaroverview]: hasVariant($state, "overview", "overview")
          })}
        >
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.registrationNumberInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"registrationNumberInput"}
              data-plasmic-override={overrides.registrationNumberInput}
              className={classNames(
                "__wab_instance",
                sty.registrationNumberInput,
                {
                  [sty.registrationNumberInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.registrationNumberInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.registrationNumberInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <HashtagIcon
                  data-plasmic-name={"iconSpot3"}
                  data-plasmic-override={overrides.iconSpot3}
                  className={classNames(projectcss.all, sty.iconSpot3, {
                    [sty.iconSpot3editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Registration Number"}
              inputName={"Registration Number"}
              inputPlaceholder={"Registration Number"}
              inputValue={generateStateValueProp($state, [
                "registrationNumberInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "registrationNumberInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.countryOfRegistrationInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"countryOfRegistrationInput"}
              data-plasmic-override={overrides.countryOfRegistrationInput}
              className={classNames(
                "__wab_instance",
                sty.countryOfRegistrationInput,
                {
                  [sty.countryOfRegistrationInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.countryOfRegistrationInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.countryOfRegistrationInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <GlobeIcon
                  data-plasmic-name={"iconSpot"}
                  data-plasmic-override={overrides.iconSpot}
                  className={classNames(projectcss.all, sty.iconSpot, {
                    [sty.iconSpoteditable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Country of Registration"}
              inputName={"Country of Registration"}
              inputPlaceholder={"Country of Registration"}
              inputValue={generateStateValueProp($state, [
                "countryOfRegistrationInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "countryOfRegistrationInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.registrationDateInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"registrationDateInput"}
              data-plasmic-override={overrides.registrationDateInput}
              className={classNames(
                "__wab_instance",
                sty.registrationDateInput,
                {
                  [sty.registrationDateInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.registrationDateInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.registrationDateInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <CalendarIcon
                  data-plasmic-name={"iconSpot2"}
                  data-plasmic-override={overrides.iconSpot2}
                  className={classNames(projectcss.all, sty.iconSpot2, {
                    [sty.iconSpot2editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Registration Date"}
              inputName={"Registration Date"}
              inputPlaceholder={"Registration Date"}
              inputType={"date"}
              inputValue={generateStateValueProp($state, [
                "registrationDateInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "registrationDateInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? (() => {
                  try {
                    return !!$state.awardDateInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.awardDateInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"awardDateInput"}
              data-plasmic-override={overrides.awardDateInput}
              className={classNames("__wab_instance", sty.awardDateInput, {
                [sty.awardDateInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.awardDateInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.awardDateInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <AwardIcon
                  data-plasmic-name={"iconSpot6"}
                  data-plasmic-override={overrides.iconSpot6}
                  className={classNames(projectcss.all, sty.iconSpot6, {
                    [sty.iconSpot6editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Award Date"}
              inputName={"Award Date"}
              inputPlaceholder={"Award Date"}
              inputType={
                hasVariant($state, "editable", "editable") ? "date" : undefined
              }
              inputValue={generateStateValueProp($state, [
                "awardDateInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "awardDateInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? (() => {
                  try {
                    return !!$state.registrationStatusSelector.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return false;
                    }
                    throw e;
                  }
                })()
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return undefined;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"registrationStatusSelector"}
              data-plasmic-override={overrides.registrationStatusSelector}
              className={classNames(
                "__wab_instance",
                sty.registrationStatusSelector,
                {
                  [sty.registrationStatusSelectoreditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.registrationStatusSelectoroverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={
                hasVariant($state, "editable", "editable")
                  ? (() => {
                      try {
                        return undefined;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                  : (() => {
                      try {
                        return undefined;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
              }
              dropdownName={"Registration Status"}
              dropdownOptions={(() => {
                const __composite = [
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null }
                ];
                __composite["0"]["value"] = "Registered";
                __composite["1"]["value"] = "Pending";
                __composite["2"]["value"] = "Cancelled";
                __composite["3"]["value"] = "Abandoned";
                return __composite;
              })()}
              dropdownPlaceholderText={"Registration Status"}
              editable={
                hasVariant($state, "overview", "overview")
                  ? undefined
                  : hasVariant($state, "editable", "editable")
                  ? "editableSelector"
                  : undefined
              }
              iconSpot2={
                <RegisteredIcon
                  data-plasmic-name={"iconSpot4"}
                  data-plasmic-override={overrides.iconSpot4}
                  className={classNames(projectcss.all, sty.iconSpot4, {
                    [sty.iconSpot4editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Registration Status"}
              inputName={"Registration Status"}
              inputPlaceholder={"Registration Status"}
              inputValue={generateStateValueProp($state, [
                "registrationStatusSelector",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "registrationStatusSelector",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "overview", "overview") ? true : undefined
              }
            />
          ) : null}
        </section>
        <SubcomponentTextInput
          data-plasmic-name={"description"}
          data-plasmic-override={overrides.description}
          className={classNames("__wab_instance", sty.description, {
            [sty.descriptioneditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.descriptionoverview]: hasVariant(
              $state,
              "overview",
              "overview"
            )
          })}
          displayText={
            hasVariant($state, "overview", "overview")
              ? (() => {
                  try {
                    return $state.description.value.length > 160
                      ? $state.description.value.substring(0, 160) + "..."
                      : $state.description.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
              : (() => {
                  try {
                    return $state.description.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
          }
          errorMessage={generateStateValueProp($state, [
            "description",
            "errorMessage"
          ])}
          inputHoverText={
            hasVariant($state, "editable", "editable")
              ? "Trademark Summary"
              : undefined
          }
          inputName={
            hasVariant($state, "editable", "editable")
              ? "Trademark Summary"
              : undefined
          }
          inputPlaceholder={
            hasVariant($state, "editable", "editable")
              ? "Trademark Summary"
              : undefined
          }
          inputValue={generateStateValueProp($state, ["description", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "description",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["description", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />
      </div>
      <SubcomponentDeleteButton
        data-plasmic-name={"subDeleteButton"}
        data-plasmic-override={overrides.subDeleteButton}
        className={classNames("__wab_instance", sty.subDeleteButton, {
          [sty.subDeleteButtoneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
        onClick={args.deleteButtonOnClick}
        onClickStageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "clickStage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDisabledChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "disabled"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  trademarkSpacingContainer: [
    "trademarkSpacingContainer",
    "trademarkIcon",
    "informationStack",
    "titleInput",
    "infoBar",
    "registrationNumberInput",
    "iconSpot3",
    "countryOfRegistrationInput",
    "iconSpot",
    "registrationDateInput",
    "iconSpot2",
    "awardDateInput",
    "iconSpot6",
    "registrationStatusSelector",
    "iconSpot4",
    "description",
    "subDeleteButton"
  ],
  trademarkIcon: ["trademarkIcon"],
  informationStack: [
    "informationStack",
    "titleInput",
    "infoBar",
    "registrationNumberInput",
    "iconSpot3",
    "countryOfRegistrationInput",
    "iconSpot",
    "registrationDateInput",
    "iconSpot2",
    "awardDateInput",
    "iconSpot6",
    "registrationStatusSelector",
    "iconSpot4",
    "description"
  ],
  titleInput: ["titleInput"],
  infoBar: [
    "infoBar",
    "registrationNumberInput",
    "iconSpot3",
    "countryOfRegistrationInput",
    "iconSpot",
    "registrationDateInput",
    "iconSpot2",
    "awardDateInput",
    "iconSpot6",
    "registrationStatusSelector",
    "iconSpot4"
  ],
  registrationNumberInput: ["registrationNumberInput", "iconSpot3"],
  iconSpot3: ["iconSpot3"],
  countryOfRegistrationInput: ["countryOfRegistrationInput", "iconSpot"],
  iconSpot: ["iconSpot"],
  registrationDateInput: ["registrationDateInput", "iconSpot2"],
  iconSpot2: ["iconSpot2"],
  awardDateInput: ["awardDateInput", "iconSpot6"],
  iconSpot6: ["iconSpot6"],
  registrationStatusSelector: ["registrationStatusSelector", "iconSpot4"],
  iconSpot4: ["iconSpot4"],
  description: ["description"],
  subDeleteButton: ["subDeleteButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  trademarkSpacingContainer: "div";
  trademarkIcon: "svg";
  informationStack: "div";
  titleInput: typeof SubcomponentTextInput;
  infoBar: "section";
  registrationNumberInput: typeof SubcomponentIconWithText;
  iconSpot3: "svg";
  countryOfRegistrationInput: typeof SubcomponentIconWithText;
  iconSpot: "svg";
  registrationDateInput: typeof SubcomponentIconWithText;
  iconSpot2: "svg";
  awardDateInput: typeof SubcomponentIconWithText;
  iconSpot6: "svg";
  registrationStatusSelector: typeof SubcomponentIconWithText;
  iconSpot4: "svg";
  description: typeof SubcomponentTextInput;
  subDeleteButton: typeof SubcomponentDeleteButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileTrademarkTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileTrademarkTile__VariantsArgs;
    args?: PlasmicProfileTileTrademarkTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTileTrademarkTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileTrademarkTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileTrademarkTile__ArgProps,
          internalVariantPropNames:
            PlasmicProfileTileTrademarkTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileTrademarkTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "trademarkSpacingContainer") {
    func.displayName = "PlasmicProfileTileTrademarkTile";
  } else {
    func.displayName = `PlasmicProfileTileTrademarkTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileTrademarkTile = Object.assign(
  // Top-level PlasmicProfileTileTrademarkTile renders the root element
  makeNodeComponent("trademarkSpacingContainer"),
  {
    // Helper components rendering sub-elements
    trademarkIcon: makeNodeComponent("trademarkIcon"),
    informationStack: makeNodeComponent("informationStack"),
    titleInput: makeNodeComponent("titleInput"),
    infoBar: makeNodeComponent("infoBar"),
    registrationNumberInput: makeNodeComponent("registrationNumberInput"),
    iconSpot3: makeNodeComponent("iconSpot3"),
    countryOfRegistrationInput: makeNodeComponent("countryOfRegistrationInput"),
    iconSpot: makeNodeComponent("iconSpot"),
    registrationDateInput: makeNodeComponent("registrationDateInput"),
    iconSpot2: makeNodeComponent("iconSpot2"),
    awardDateInput: makeNodeComponent("awardDateInput"),
    iconSpot6: makeNodeComponent("iconSpot6"),
    registrationStatusSelector: makeNodeComponent("registrationStatusSelector"),
    iconSpot4: makeNodeComponent("iconSpot4"),
    description: makeNodeComponent("description"),
    subDeleteButton: makeNodeComponent("subDeleteButton"),

    // Metadata about props expected for PlasmicProfileTileTrademarkTile
    internalVariantProps: PlasmicProfileTileTrademarkTile__VariantProps,
    internalArgProps: PlasmicProfileTileTrademarkTile__ArgProps
  }
);

export default PlasmicProfileTileTrademarkTile;
/* prettier-ignore-end */
