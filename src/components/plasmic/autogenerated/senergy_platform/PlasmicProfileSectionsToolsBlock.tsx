/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: lS3Tush4KDKz

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileToolsTile from "../../ProfileTileToolsTile"; // plasmic-import: 9IgGeZ9Jjxx9/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsToolsBlock.module.css"; // plasmic-import: lS3Tush4KDKz/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsToolsBlock__VariantMembers = {
  overviewBlock: "overviewBlock";
  editable: "editable";
};
export type PlasmicProfileSectionsToolsBlock__VariantsArgs = {
  overviewBlock?: SingleBooleanChoiceArg<"overviewBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileSectionsToolsBlock__VariantsArgs;
export const PlasmicProfileSectionsToolsBlock__VariantProps =
  new Array<VariantPropType>("overviewBlock", "editable");

export type PlasmicProfileSectionsToolsBlock__ArgsType = {};
type ArgPropType = keyof PlasmicProfileSectionsToolsBlock__ArgsType;
export const PlasmicProfileSectionsToolsBlock__ArgProps =
  new Array<ArgPropType>();

export type PlasmicProfileSectionsToolsBlock__OverridesType = {
  toolsSection?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  toolsContent?: Flex__<"div">;
  profileTileToolsTile?: Flex__<typeof ProfileTileToolsTile>;
};

export interface DefaultProfileSectionsToolsBlockProps {
  overviewBlock?: SingleBooleanChoiceArg<"overviewBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsToolsBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsToolsBlock__VariantsArgs;
  args: PlasmicProfileSectionsToolsBlock__ArgsType;
  overrides: PlasmicProfileSectionsToolsBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewBlock",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewBlock
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"toolsSection"}
      data-plasmic-override={overrides.toolsSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.toolsSection,
        {
          [sty.toolsSectioneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.toolsSectionoverviewBlock]: hasVariant(
            $state,
            "overviewBlock",
            "overviewBlock"
          )
        }
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewBlock]: hasVariant(
              $state,
              "overviewBlock",
              "overviewBlock"
            )
          }
        )}
        editable={hasVariant($state, "editable", "editable") ? true : undefined}
        overviewGrid={
          hasVariant($state, "overviewBlock", "overviewBlock")
            ? true
            : undefined
        }
      >
        {"Tools"}
      </ProfileSectionsProfileSectionHeading>
      <div
        data-plasmic-name={"toolsContent"}
        data-plasmic-override={overrides.toolsContent}
        className={classNames(projectcss.all, sty.toolsContent, {
          [sty.toolsContenteditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.toolsContentoverviewBlock]: hasVariant(
            $state,
            "overviewBlock",
            "overviewBlock"
          )
        })}
      >
        <ProfileTileToolsTile
          data-plasmic-name={"profileTileToolsTile"}
          data-plasmic-override={overrides.profileTileToolsTile}
          className={classNames("__wab_instance", sty.profileTileToolsTile, {
            [sty.profileTileToolsTileeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileTileToolsTileoverviewBlock]: hasVariant(
              $state,
              "overviewBlock",
              "overviewBlock"
            )
          })}
          editable={
            hasVariant($state, "editable", "editable") ? true : undefined
          }
        />
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  toolsSection: [
    "toolsSection",
    "profileSectionsProfileSectionHeading",
    "toolsContent",
    "profileTileToolsTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading"
  ],
  toolsContent: ["toolsContent", "profileTileToolsTile"],
  profileTileToolsTile: ["profileTileToolsTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  toolsSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  toolsContent: "div";
  profileTileToolsTile: typeof ProfileTileToolsTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsToolsBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsToolsBlock__VariantsArgs;
    args?: PlasmicProfileSectionsToolsBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileSectionsToolsBlock__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsToolsBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileSectionsToolsBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsToolsBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsToolsBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "toolsSection") {
    func.displayName = "PlasmicProfileSectionsToolsBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsToolsBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsToolsBlock = Object.assign(
  // Top-level PlasmicProfileSectionsToolsBlock renders the root element
  makeNodeComponent("toolsSection"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    toolsContent: makeNodeComponent("toolsContent"),
    profileTileToolsTile: makeNodeComponent("profileTileToolsTile"),

    // Metadata about props expected for PlasmicProfileSectionsToolsBlock
    internalVariantProps: PlasmicProfileSectionsToolsBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsToolsBlock__ArgProps
  }
);

export default PlasmicProfileSectionsToolsBlock;
/* prettier-ignore-end */
