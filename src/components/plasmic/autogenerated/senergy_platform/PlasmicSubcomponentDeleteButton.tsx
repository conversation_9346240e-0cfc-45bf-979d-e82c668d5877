/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: WMfLdq1qoZyo

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentDeleteButton.module.css"; // plasmic-import: WMfLdq1qoZyo/css

import XIcon from "./icons/PlasmicIcon__X"; // plasmic-import: deLi5PaFbuAg/icon

createPlasmicElementProxy;

export type PlasmicSubcomponentDeleteButton__VariantMembers = {
  interactionVariants: "hover" | "pendingConfirmation" | "confirmed";
  disabled: "disabled" | "disabledHover" | "shakeLeft" | "shakeRight";
};
export type PlasmicSubcomponentDeleteButton__VariantsArgs = {
  interactionVariants?: SingleChoiceArg<
    "hover" | "pendingConfirmation" | "confirmed"
  >;
  disabled?: SingleChoiceArg<
    "disabled" | "disabledHover" | "shakeLeft" | "shakeRight"
  >;
};
type VariantPropType = keyof PlasmicSubcomponentDeleteButton__VariantsArgs;
export const PlasmicSubcomponentDeleteButton__VariantProps =
  new Array<VariantPropType>("interactionVariants", "disabled");

export type PlasmicSubcomponentDeleteButton__ArgsType = {
  onClickStageChange?: (val: string) => void;
  onDisabledChange?: (val: any) => void;
  onClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicSubcomponentDeleteButton__ArgsType;
export const PlasmicSubcomponentDeleteButton__ArgProps = new Array<ArgPropType>(
  "onClickStageChange",
  "onDisabledChange",
  "onClick"
);

export type PlasmicSubcomponentDeleteButton__OverridesType = {
  deleteButtonContainer?: Flex__<"div">;
  text?: Flex__<"div">;
  xIcon?: Flex__<"svg">;
};

export interface DefaultSubcomponentDeleteButtonProps {
  onClickStageChange?: (val: string) => void;
  onDisabledChange?: (val: any) => void;
  onClick?: (event: any) => void;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentDeleteButton__RenderFunc(props: {
  variants: PlasmicSubcomponentDeleteButton__VariantsArgs;
  args: PlasmicSubcomponentDeleteButton__ArgsType;
  overrides: PlasmicSubcomponentDeleteButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "interactionVariants",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.interactionVariants
      },
      {
        path: "clickStage",
        type: "readonly",
        variableType: "number",
        initFunc: ({ $props, $state, $queries, $ctx }) => 0,

        onChangeProp: "onClickStageChange"
      },
      {
        path: "disabled",
        type: "readonly",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.disabled,

        onChangeProp: "onDisabledChange"
      },
      {
        path: "isDisabled",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return (
                $state.disabled == "disabled" ||
                $state.disabled == "disabledHover" ||
                $state.disabled == "shakeLeft" ||
                $state.disabled == "shakeRight"
              );
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return false;
              }
              throw e;
            }
          })()
      },
      {
        path: "hovered",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => false
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"deleteButtonContainer"}
      data-plasmic-override={overrides.deleteButtonContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.deleteButtonContainer,
        {
          [sty.deleteButtonContainerdisabled_disabledHover]: hasVariant(
            $state,
            "disabled",
            "disabledHover"
          ),
          [sty.deleteButtonContainerdisabled_disabled]: hasVariant(
            $state,
            "disabled",
            "disabled"
          ),
          [sty.deleteButtonContainerdisabled_shakeLeft]: hasVariant(
            $state,
            "disabled",
            "shakeLeft"
          ),
          [sty.deleteButtonContainerdisabled_shakeRight]: hasVariant(
            $state,
            "disabled",
            "shakeRight"
          ),
          [sty.deleteButtonContainerinteractionVariants_confirmed]: hasVariant(
            $state,
            "interactionVariants",
            "confirmed"
          ),
          [sty.deleteButtonContainerinteractionVariants_hover]: hasVariant(
            $state,
            "interactionVariants",
            "hover"
          ),
          [sty.deleteButtonContainerinteractionVariants_pendingConfirmation]:
            hasVariant($state, "interactionVariants", "pendingConfirmation")
        }
      )}
      onClick={args.onClick}
      onClickCapture={async event => {
        const $steps = {};

        $steps["askConfirm"] =
          $state.interactionVariants == "hover"
            ? (() => {
                const actionArgs = {
                  vgroup: "interactionVariants",
                  operation: 0,
                  value: "pendingConfirmation"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["askConfirm"] != null &&
          typeof $steps["askConfirm"] === "object" &&
          typeof $steps["askConfirm"].then === "function"
        ) {
          $steps["askConfirm"] = await $steps["askConfirm"];
        }

        $steps["updateClickStage"] =
          $state.interactionVariants == "pendingConfirmation"
            ? (() => {
                const actionArgs = {
                  variable: {
                    objRoot: $state,
                    variablePath: ["clickStage"]
                  },
                  operation: 2
                };
                return (({ variable, value, startIndex, deleteCount }) => {
                  if (!variable) {
                    return;
                  }
                  const { objRoot, variablePath } = variable;

                  const oldValue = $stateGet(objRoot, variablePath);
                  $stateSet(objRoot, variablePath, oldValue + 1);
                  return oldValue + 1;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["updateClickStage"] != null &&
          typeof $steps["updateClickStage"] === "object" &&
          typeof $steps["updateClickStage"].then === "function"
        ) {
          $steps["updateClickStage"] = await $steps["updateClickStage"];
        }

        $steps["confirmed"] =
          $state.clickStage == 2
            ? (() => {
                const actionArgs = {
                  vgroup: "interactionVariants",
                  operation: 0,
                  value: "confirmed"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["confirmed"] != null &&
          typeof $steps["confirmed"] === "object" &&
          typeof $steps["confirmed"].then === "function"
        ) {
          $steps["confirmed"] = await $steps["confirmed"];
        }

        $steps["shakeOnDisabled"] =
          $state.isDisabled == true
            ? (() => {
                const actionArgs = {
                  customFunction: async () => {
                    return setTimeout(() => {
                      $state.disabled = "shakeRight";
                      setTimeout(() => {
                        $state.disabled = "shakeLeft";
                        setTimeout(() => {
                          $state.disabled = "shakeRight";
                          setTimeout(() => {
                            $state.disabled = "shakeLeft";
                            setTimeout(() => {
                              $state.disabled = $state.hovered
                                ? "disabledHover"
                                : "disabled";
                            }, 50);
                          }, 50);
                        }, 50);
                      }, 50);
                    }, 50);
                  }
                };
                return (({ customFunction }) => {
                  return customFunction();
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["shakeOnDisabled"] != null &&
          typeof $steps["shakeOnDisabled"] === "object" &&
          typeof $steps["shakeOnDisabled"].then === "function"
        ) {
          $steps["shakeOnDisabled"] = await $steps["shakeOnDisabled"];
        }
      }}
      onMouseEnter={async event => {
        const $steps = {};

        $steps["enabledHover"] =
          $state.interactionVariants === undefined &&
          $state.disabled === undefined
            ? (() => {
                const actionArgs = {
                  vgroup: "interactionVariants",
                  operation: 0,
                  value: "hover"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["enabledHover"] != null &&
          typeof $steps["enabledHover"] === "object" &&
          typeof $steps["enabledHover"].then === "function"
        ) {
          $steps["enabledHover"] = await $steps["enabledHover"];
        }

        $steps["disabledHover"] =
          $state.disabled == "disabled"
            ? (() => {
                const actionArgs = {
                  vgroup: "disabled",
                  operation: 0,
                  value: "disabledHover"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["disabledHover"] != null &&
          typeof $steps["disabledHover"] === "object" &&
          typeof $steps["disabledHover"].then === "function"
        ) {
          $steps["disabledHover"] = await $steps["disabledHover"];
        }

        $steps["updateHoverStateVar"] = true
          ? (() => {
              const actionArgs = {
                variable: {
                  objRoot: $state,
                  variablePath: ["hovered"]
                },
                operation: 0,
                value: true
              };
              return (({ variable, value, startIndex, deleteCount }) => {
                if (!variable) {
                  return;
                }
                const { objRoot, variablePath } = variable;

                $stateSet(objRoot, variablePath, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverStateVar"] != null &&
          typeof $steps["updateHoverStateVar"] === "object" &&
          typeof $steps["updateHoverStateVar"].then === "function"
        ) {
          $steps["updateHoverStateVar"] = await $steps["updateHoverStateVar"];
        }
      }}
      onMouseLeave={async event => {
        const $steps = {};

        $steps["defaultEnabled"] =
          $state.disabled === undefined
            ? (() => {
                const actionArgs = {
                  vgroup: "interactionVariants",
                  operation: 1
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, undefined);
                  return undefined;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["defaultEnabled"] != null &&
          typeof $steps["defaultEnabled"] === "object" &&
          typeof $steps["defaultEnabled"].then === "function"
        ) {
          $steps["defaultEnabled"] = await $steps["defaultEnabled"];
        }

        $steps["resetClickStage"] = true
          ? (() => {
              const actionArgs = {
                variable: {
                  objRoot: $state,
                  variablePath: ["clickStage"]
                },
                operation: 0,
                value: 0
              };
              return (({ variable, value, startIndex, deleteCount }) => {
                if (!variable) {
                  return;
                }
                const { objRoot, variablePath } = variable;

                $stateSet(objRoot, variablePath, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["resetClickStage"] != null &&
          typeof $steps["resetClickStage"] === "object" &&
          typeof $steps["resetClickStage"].then === "function"
        ) {
          $steps["resetClickStage"] = await $steps["resetClickStage"];
        }

        $steps["defaultDisabled"] =
          $state.disabled == "disabledHover"
            ? (() => {
                const actionArgs = {
                  vgroup: "disabled",
                  operation: 0,
                  value: "disabled"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["defaultDisabled"] != null &&
          typeof $steps["defaultDisabled"] === "object" &&
          typeof $steps["defaultDisabled"].then === "function"
        ) {
          $steps["defaultDisabled"] = await $steps["defaultDisabled"];
        }

        $steps["updateHoverStateVar"] = true
          ? (() => {
              const actionArgs = {
                variable: {
                  objRoot: $state,
                  variablePath: ["hovered"]
                },
                operation: 0,
                value: false
              };
              return (({ variable, value, startIndex, deleteCount }) => {
                if (!variable) {
                  return;
                }
                const { objRoot, variablePath } = variable;

                $stateSet(objRoot, variablePath, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverStateVar"] != null &&
          typeof $steps["updateHoverStateVar"] === "object" &&
          typeof $steps["updateHoverStateVar"].then === "function"
        ) {
          $steps["updateHoverStateVar"] = await $steps["updateHoverStateVar"];
        }
      }}
    >
      <div
        data-plasmic-name={"text"}
        data-plasmic-override={overrides.text}
        className={classNames(projectcss.all, projectcss.__wab_text, sty.text, {
          [sty.textdisabled_disabledHover]: hasVariant(
            $state,
            "disabled",
            "disabledHover"
          ),
          [sty.textdisabled_disabled]: hasVariant(
            $state,
            "disabled",
            "disabled"
          ),
          [sty.textdisabled_shakeLeft]: hasVariant(
            $state,
            "disabled",
            "shakeLeft"
          ),
          [sty.textdisabled_shakeRight]: hasVariant(
            $state,
            "disabled",
            "shakeRight"
          ),
          [sty.textinteractionVariants_hover]: hasVariant(
            $state,
            "interactionVariants",
            "hover"
          ),
          [sty.textinteractionVariants_pendingConfirmation]: hasVariant(
            $state,
            "interactionVariants",
            "pendingConfirmation"
          )
        })}
      >
        {hasVariant($state, "disabled", "shakeRight")
          ? "Delete"
          : hasVariant($state, "disabled", "shakeLeft")
          ? "Delete"
          : hasVariant($state, "disabled", "disabledHover")
          ? "Delete"
          : hasVariant($state, "interactionVariants", "pendingConfirmation")
          ? "Confirm"
          : hasVariant($state, "interactionVariants", "hover")
          ? "Delete"
          : "Delete"}
      </div>
      <XIcon
        data-plasmic-name={"xIcon"}
        data-plasmic-override={overrides.xIcon}
        className={classNames(projectcss.all, sty.xIcon, {
          [sty.xIcondisabled_disabledHover]: hasVariant(
            $state,
            "disabled",
            "disabledHover"
          ),
          [sty.xIcondisabled_shakeLeft]: hasVariant(
            $state,
            "disabled",
            "shakeLeft"
          ),
          [sty.xIcondisabled_shakeRight]: hasVariant(
            $state,
            "disabled",
            "shakeRight"
          ),
          [sty.xIconinteractionVariants_confirmed]: hasVariant(
            $state,
            "interactionVariants",
            "confirmed"
          ),
          [sty.xIconinteractionVariants_hover]: hasVariant(
            $state,
            "interactionVariants",
            "hover"
          ),
          [sty.xIconinteractionVariants_pendingConfirmation]: hasVariant(
            $state,
            "interactionVariants",
            "pendingConfirmation"
          )
        })}
        role={"img"}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  deleteButtonContainer: ["deleteButtonContainer", "text", "xIcon"],
  text: ["text"],
  xIcon: ["xIcon"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  deleteButtonContainer: "div";
  text: "div";
  xIcon: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentDeleteButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentDeleteButton__VariantsArgs;
    args?: PlasmicSubcomponentDeleteButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubcomponentDeleteButton__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentDeleteButton__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentDeleteButton__ArgProps,
          internalVariantPropNames:
            PlasmicSubcomponentDeleteButton__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentDeleteButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "deleteButtonContainer") {
    func.displayName = "PlasmicSubcomponentDeleteButton";
  } else {
    func.displayName = `PlasmicSubcomponentDeleteButton.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentDeleteButton = Object.assign(
  // Top-level PlasmicSubcomponentDeleteButton renders the root element
  makeNodeComponent("deleteButtonContainer"),
  {
    // Helper components rendering sub-elements
    text: makeNodeComponent("text"),
    xIcon: makeNodeComponent("xIcon"),

    // Metadata about props expected for PlasmicSubcomponentDeleteButton
    internalVariantProps: PlasmicSubcomponentDeleteButton__VariantProps,
    internalArgProps: PlasmicSubcomponentDeleteButton__ArgProps
  }
);

export default PlasmicSubcomponentDeleteButton;
/* prettier-ignore-end */
