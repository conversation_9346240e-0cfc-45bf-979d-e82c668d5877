/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: UWTH8LMACb3s

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileOverviewSeeMoreButton.module.css"; // plasmic-import: UWTH8LMACb3s/css

import ArrowRightIcon from "./icons/PlasmicIcon__ArrowRight"; // plasmic-import: 0GbuOQuYcSoq/icon

createPlasmicElementProxy;

export type PlasmicProfileOverviewSeeMoreButton__VariantMembers = {};
export type PlasmicProfileOverviewSeeMoreButton__VariantsArgs = {};
type VariantPropType = keyof PlasmicProfileOverviewSeeMoreButton__VariantsArgs;
export const PlasmicProfileOverviewSeeMoreButton__VariantProps =
  new Array<VariantPropType>();

export type PlasmicProfileOverviewSeeMoreButton__ArgsType = {
  onClick?: (event: any) => void;
  text?: string;
};
type ArgPropType = keyof PlasmicProfileOverviewSeeMoreButton__ArgsType;
export const PlasmicProfileOverviewSeeMoreButton__ArgProps =
  new Array<ArgPropType>("onClick", "text");

export type PlasmicProfileOverviewSeeMoreButton__OverridesType = {
  root?: Flex__<"button">;
  formattingContainer?: Flex__<"div">;
  iconAndTextStack?: Flex__<"div">;
  displayText?: Flex__<"div">;
  svg?: Flex__<"svg">;
  underline?: Flex__<"section">;
};

export interface DefaultProfileOverviewSeeMoreButtonProps {
  onClick?: (event: any) => void;
  text?: string;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileOverviewSeeMoreButton__RenderFunc(props: {
  variants: PlasmicProfileOverviewSeeMoreButton__VariantsArgs;
  args: PlasmicProfileOverviewSeeMoreButton__ArgsType;
  overrides: PlasmicProfileOverviewSeeMoreButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          text: "See More"
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const [isRootHover, triggerRootHoverProps] = useTrigger("useHover", {});
  const [isRootActive, triggerRootActiveProps] = useTrigger("usePressed", {});
  const triggers = {
    hover_root: isRootHover,
    active_root: isRootActive
  };

  return (
    <button
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.button,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
      onClick={args.onClick}
      onMouseDown={async event => {
        const $steps = {};
      }}
      data-plasmic-trigger-props={[
        triggerRootHoverProps,
        triggerRootActiveProps
      ]}
    >
      <div
        data-plasmic-name={"formattingContainer"}
        data-plasmic-override={overrides.formattingContainer}
        className={classNames(projectcss.all, sty.formattingContainer)}
      >
        <div
          data-plasmic-name={"iconAndTextStack"}
          data-plasmic-override={overrides.iconAndTextStack}
          className={classNames(projectcss.all, sty.iconAndTextStack)}
          onClick={args.onClick}
        >
          <div
            data-plasmic-name={"displayText"}
            data-plasmic-override={overrides.displayText}
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.displayText
            )}
          >
            <React.Fragment>
              {(() => {
                try {
                  return $props.text;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return "See More";
                  }
                  throw e;
                }
              })()}
            </React.Fragment>
          </div>
          <ArrowRightIcon
            data-plasmic-name={"svg"}
            data-plasmic-override={overrides.svg}
            className={classNames(projectcss.all, sty.svg)}
            role={"img"}
          />
        </div>
        <section
          data-plasmic-name={"underline"}
          data-plasmic-override={overrides.underline}
          className={classNames(projectcss.all, sty.underline)}
        />
      </div>
    </button>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "formattingContainer",
    "iconAndTextStack",
    "displayText",
    "svg",
    "underline"
  ],
  formattingContainer: [
    "formattingContainer",
    "iconAndTextStack",
    "displayText",
    "svg",
    "underline"
  ],
  iconAndTextStack: ["iconAndTextStack", "displayText", "svg"],
  displayText: ["displayText"],
  svg: ["svg"],
  underline: ["underline"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "button";
  formattingContainer: "div";
  iconAndTextStack: "div";
  displayText: "div";
  svg: "svg";
  underline: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileOverviewSeeMoreButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileOverviewSeeMoreButton__VariantsArgs;
    args?: PlasmicProfileOverviewSeeMoreButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileOverviewSeeMoreButton__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileOverviewSeeMoreButton__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileOverviewSeeMoreButton__ArgProps,
          internalVariantPropNames:
            PlasmicProfileOverviewSeeMoreButton__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileOverviewSeeMoreButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicProfileOverviewSeeMoreButton";
  } else {
    func.displayName = `PlasmicProfileOverviewSeeMoreButton.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileOverviewSeeMoreButton = Object.assign(
  // Top-level PlasmicProfileOverviewSeeMoreButton renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    formattingContainer: makeNodeComponent("formattingContainer"),
    iconAndTextStack: makeNodeComponent("iconAndTextStack"),
    displayText: makeNodeComponent("displayText"),
    svg: makeNodeComponent("svg"),
    underline: makeNodeComponent("underline"),

    // Metadata about props expected for PlasmicProfileOverviewSeeMoreButton
    internalVariantProps: PlasmicProfileOverviewSeeMoreButton__VariantProps,
    internalArgProps: PlasmicProfileOverviewSeeMoreButton__ArgProps
  }
);

export default PlasmicProfileOverviewSeeMoreButton;
/* prettier-ignore-end */
