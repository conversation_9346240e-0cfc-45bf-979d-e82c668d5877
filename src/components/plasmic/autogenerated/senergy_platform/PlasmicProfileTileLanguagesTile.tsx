/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: tlsYnN8cn_ki

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentSlotHeading from "../../SubcomponentSlotHeading"; // plasmic-import: pRxYe0rDyQBY/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentDeleteButton from "../../SubcomponentDeleteButton"; // plasmic-import: WMfLdq1qoZyo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileLanguagesTile.module.css"; // plasmic-import: tlsYnN8cn_ki/css

import PlaceholderCircleIcon from "./icons/PlasmicIcon__PlaceholderCircle"; // plasmic-import: TfTuYHhdLsP9/icon

createPlasmicElementProxy;

export type PlasmicProfileTileLanguagesTile__VariantMembers = {
  overviewGrid: "overviewGrid";
  editable: "editable";
};
export type PlasmicProfileTileLanguagesTile__VariantsArgs = {
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileTileLanguagesTile__VariantsArgs;
export const PlasmicProfileTileLanguagesTile__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileTileLanguagesTile__ArgsType = {
  langProficiencyLevel?: string;
  languages?: any;
};
type ArgPropType = keyof PlasmicProfileTileLanguagesTile__ArgsType;
export const PlasmicProfileTileLanguagesTile__ArgProps = new Array<ArgPropType>(
  "langProficiencyLevel",
  "languages"
);

export type PlasmicProfileTileLanguagesTile__OverridesType = {
  langaugeFormatingStack?: Flex__<"div">;
  subcomponentSlotHeading?: Flex__<typeof SubcomponentSlotHeading>;
  subIconWithText?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot?: Flex__<"svg">;
  subDeleteButton?: Flex__<typeof SubcomponentDeleteButton>;
};

export interface DefaultProfileTileLanguagesTileProps {
  langProficiencyLevel?: string;
  languages?: any;
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileLanguagesTile__RenderFunc(props: {
  variants: PlasmicProfileTileLanguagesTile__VariantsArgs;
  args: PlasmicProfileTileLanguagesTile__ArgsType;
  overrides: PlasmicProfileTileLanguagesTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "subDeleteButton.disabled",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "subDeleteButton.clickStage",
        type: "private",
        variableType: "number",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "subIconWithText[].inputValue",
        type: "private",
        variableType: "text"
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"langaugeFormatingStack"}
      data-plasmic-override={overrides.langaugeFormatingStack}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.langaugeFormatingStack,
        {
          [sty.langaugeFormatingStackoverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        }
      )}
    >
      <SubcomponentSlotHeading
        data-plasmic-name={"subcomponentSlotHeading"}
        data-plasmic-override={overrides.subcomponentSlotHeading}
        className={classNames("__wab_instance", sty.subcomponentSlotHeading, {
          [sty.subcomponentSlotHeadingoverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        })}
        headingTitle={
          <React.Fragment>
            {(() => {
              try {
                return $props.langProficiencyLevel;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return "Proficiency Level SubHeading";
                }
                throw e;
              }
            })()}
          </React.Fragment>
        }
      />

      {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))([
        2, 3, 4
      ]).map((__plasmic_item_0, __plasmic_idx_0) => {
        const currentItem = __plasmic_item_0;
        const currentIndex = __plasmic_idx_0;
        return (() => {
          const child$Props = {
            className: classNames("__wab_instance", sty.subIconWithText, {
              [sty.subIconWithTexteditable]: hasVariant(
                $state,
                "editable",
                "editable"
              )
            }),
            iconSpot2: (
              <PlaceholderCircleIcon
                data-plasmic-name={"iconSpot"}
                data-plasmic-override={overrides.iconSpot}
                className={classNames(projectcss.all, sty.iconSpot)}
                role={"img"}
              />
            ),

            inputValue: generateStateValueProp($state, [
              "subIconWithText",
              __plasmic_idx_0,
              "inputValue"
            ]),
            key: currentIndex,
            onInputValueChange: async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "subIconWithText",
                __plasmic_idx_0,
                "inputValue"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }
          };

          initializePlasmicStates(
            $state,
            [
              {
                name: "subIconWithText[].inputValue",
                initFunc: ({ $props, $state, $queries }) => undefined
              }
            ],
            [__plasmic_idx_0]
          );
          return (
            <SubcomponentIconWithText
              data-plasmic-name={"subIconWithText"}
              data-plasmic-override={overrides.subIconWithText}
              {...child$Props}
            />
          );
        })();
      })}
      <SubcomponentDeleteButton
        data-plasmic-name={"subDeleteButton"}
        data-plasmic-override={overrides.subDeleteButton}
        className={classNames("__wab_instance", sty.subDeleteButton, {
          [sty.subDeleteButtoneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
        onClickStageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "clickStage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDisabledChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "disabled"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  langaugeFormatingStack: [
    "langaugeFormatingStack",
    "subcomponentSlotHeading",
    "subIconWithText",
    "iconSpot",
    "subDeleteButton"
  ],
  subcomponentSlotHeading: ["subcomponentSlotHeading"],
  subIconWithText: ["subIconWithText", "iconSpot"],
  iconSpot: ["iconSpot"],
  subDeleteButton: ["subDeleteButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  langaugeFormatingStack: "div";
  subcomponentSlotHeading: typeof SubcomponentSlotHeading;
  subIconWithText: typeof SubcomponentIconWithText;
  iconSpot: "svg";
  subDeleteButton: typeof SubcomponentDeleteButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileLanguagesTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileLanguagesTile__VariantsArgs;
    args?: PlasmicProfileTileLanguagesTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTileLanguagesTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileLanguagesTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileLanguagesTile__ArgProps,
          internalVariantPropNames:
            PlasmicProfileTileLanguagesTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileLanguagesTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "langaugeFormatingStack") {
    func.displayName = "PlasmicProfileTileLanguagesTile";
  } else {
    func.displayName = `PlasmicProfileTileLanguagesTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileLanguagesTile = Object.assign(
  // Top-level PlasmicProfileTileLanguagesTile renders the root element
  makeNodeComponent("langaugeFormatingStack"),
  {
    // Helper components rendering sub-elements
    subcomponentSlotHeading: makeNodeComponent("subcomponentSlotHeading"),
    subIconWithText: makeNodeComponent("subIconWithText"),
    iconSpot: makeNodeComponent("iconSpot"),
    subDeleteButton: makeNodeComponent("subDeleteButton"),

    // Metadata about props expected for PlasmicProfileTileLanguagesTile
    internalVariantProps: PlasmicProfileTileLanguagesTile__VariantProps,
    internalArgProps: PlasmicProfileTileLanguagesTile__ArgProps
  }
);

export default PlasmicProfileTileLanguagesTile;
/* prettier-ignore-end */
