/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: t4FaTc0WFZv3

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>iceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import NavigationLogoStack from "../../NavigationLogoStack"; // plasmic-import: 8ZiaPQ4apvBM/component
import NavigationTopButtonAssembly from "../../NavigationTopButtonAssembly"; // plasmic-import: EbN_LUazXitj/component
import NavigationBottomButtonAssembly from "../../NavigationBottomButtonAssembly"; // plasmic-import: BKP1w5f1sryI/component
import NavigationSubBarMenus from "../../NavigationSubBarMenus"; // plasmic-import: 19fXsiHSxAKf/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationSidebar.module.css"; // plasmic-import: t4FaTc0WFZv3/css

createPlasmicElementProxy;

export type PlasmicNavigationSidebar__VariantMembers = {
  activeSubBar: "search" | "bookmarks" | "messages";
  activePage: "profile" | "settings";
  unAuthed: "unAuthed";
  collapse: "fullCollapsed" | "hover";
};
export type PlasmicNavigationSidebar__VariantsArgs = {
  activeSubBar?: SingleChoiceArg<"search" | "bookmarks" | "messages">;
  activePage?: SingleChoiceArg<"profile" | "settings">;
  unAuthed?: SingleBooleanChoiceArg<"unAuthed">;
  collapse?: SingleChoiceArg<"fullCollapsed" | "hover">;
};
type VariantPropType = keyof PlasmicNavigationSidebar__VariantsArgs;
export const PlasmicNavigationSidebar__VariantProps =
  new Array<VariantPropType>(
    "activeSubBar",
    "activePage",
    "unAuthed",
    "collapse"
  );

export type PlasmicNavigationSidebar__ArgsType = {};
type ArgPropType = keyof PlasmicNavigationSidebar__ArgsType;
export const PlasmicNavigationSidebar__ArgProps = new Array<ArgPropType>();

export type PlasmicNavigationSidebar__OverridesType = {
  wrapper?: Flex__<"div">;
  container?: Flex__<"div">;
  navMainMenuContainer?: Flex__<"div">;
  navigationInteractions?: Flex__<"div">;
  navigationLogoStack?: Flex__<typeof NavigationLogoStack>;
  sideBarLinks?: Flex__<"div">;
  navigationTopButtonAssembly?: Flex__<typeof NavigationTopButtonAssembly>;
  workingInfo?: Flex__<"section">;
  navigationBottomButtonAssembly?: Flex__<
    typeof NavigationBottomButtonAssembly
  >;
  subMenuInformation?: Flex__<"section">;
  navigationSubBarMenus?: Flex__<typeof NavigationSubBarMenus>;
};

export interface DefaultNavigationSidebarProps {
  activeSubBar?: SingleChoiceArg<"search" | "bookmarks" | "messages">;
  activePage?: SingleChoiceArg<"profile" | "settings">;
  unAuthed?: SingleBooleanChoiceArg<"unAuthed">;
  collapse?: SingleChoiceArg<"fullCollapsed" | "hover">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationSidebar__RenderFunc(props: {
  variants: PlasmicNavigationSidebar__VariantsArgs;
  args: PlasmicNavigationSidebar__ArgsType;
  overrides: PlasmicNavigationSidebar__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "activePage",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.activePage
      },
      {
        path: "activeSubBar",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.activeSubBar
      },
      {
        path: "unAuthed",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.unAuthed
      },
      {
        path: "collapse",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.collapse
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"wrapper"}
      data-plasmic-override={overrides.wrapper}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.wrapper,
        {
          [sty.wrapperactivePage_profile]: hasVariant(
            $state,
            "activePage",
            "profile"
          ),
          [sty.wrapperactivePage_settings]: hasVariant(
            $state,
            "activePage",
            "settings"
          ),
          [sty.wrapperactiveSubBar_bookmarks]: hasVariant(
            $state,
            "activeSubBar",
            "bookmarks"
          ),
          [sty.wrapperactiveSubBar_bookmarks_collapse_hover]:
            hasVariant($state, "collapse", "hover") &&
            hasVariant($state, "activeSubBar", "bookmarks"),
          [sty.wrapperactiveSubBar_messages]: hasVariant(
            $state,
            "activeSubBar",
            "messages"
          ),
          [sty.wrapperactiveSubBar_messages_collapse_fullCollapsed]:
            hasVariant($state, "collapse", "fullCollapsed") &&
            hasVariant($state, "activeSubBar", "messages"),
          [sty.wrapperactiveSubBar_messages_collapse_hover]:
            hasVariant($state, "collapse", "hover") &&
            hasVariant($state, "activeSubBar", "messages"),
          [sty.wrapperactiveSubBar_search]: hasVariant(
            $state,
            "activeSubBar",
            "search"
          ),
          [sty.wrapperactiveSubBar_search_collapse_fullCollapsed]:
            hasVariant($state, "collapse", "fullCollapsed") &&
            hasVariant($state, "activeSubBar", "search"),
          [sty.wrapperactiveSubBar_search_collapse_hover]:
            hasVariant($state, "collapse", "hover") &&
            hasVariant($state, "activeSubBar", "search"),
          [sty.wrappercollapse_fullCollapsed]: hasVariant(
            $state,
            "collapse",
            "fullCollapsed"
          ),
          [sty.wrappercollapse_fullCollapsed_activeSubBar_bookmarks]:
            hasVariant($state, "collapse", "fullCollapsed") &&
            hasVariant($state, "activeSubBar", "bookmarks"),
          [sty.wrappercollapse_hover]: hasVariant($state, "collapse", "hover"),
          [sty.wrapperunAuthed]: hasVariant($state, "unAuthed", "unAuthed")
        }
      )}
    >
      <div
        data-plasmic-name={"container"}
        data-plasmic-override={overrides.container}
        className={classNames(projectcss.all, sty.container, {
          [sty.containeractivePage_profile]: hasVariant(
            $state,
            "activePage",
            "profile"
          ),
          [sty.containeractivePage_settings]: hasVariant(
            $state,
            "activePage",
            "settings"
          ),
          [sty.containeractiveSubBar_bookmarks]: hasVariant(
            $state,
            "activeSubBar",
            "bookmarks"
          ),
          [sty.containeractiveSubBar_bookmarks_collapse_hover]:
            hasVariant($state, "collapse", "hover") &&
            hasVariant($state, "activeSubBar", "bookmarks"),
          [sty.containeractiveSubBar_messages]: hasVariant(
            $state,
            "activeSubBar",
            "messages"
          ),
          [sty.containeractiveSubBar_messages_collapse_fullCollapsed]:
            hasVariant($state, "collapse", "fullCollapsed") &&
            hasVariant($state, "activeSubBar", "messages"),
          [sty.containeractiveSubBar_messages_collapse_hover]:
            hasVariant($state, "collapse", "hover") &&
            hasVariant($state, "activeSubBar", "messages"),
          [sty.containeractiveSubBar_search]: hasVariant(
            $state,
            "activeSubBar",
            "search"
          ),
          [sty.containeractiveSubBar_search_collapse_fullCollapsed]:
            hasVariant($state, "collapse", "fullCollapsed") &&
            hasVariant($state, "activeSubBar", "search"),
          [sty.containeractiveSubBar_search_collapse_hover]:
            hasVariant($state, "collapse", "hover") &&
            hasVariant($state, "activeSubBar", "search"),
          [sty.containercollapse_fullCollapsed]: hasVariant(
            $state,
            "collapse",
            "fullCollapsed"
          ),
          [sty.containercollapse_fullCollapsed_activeSubBar_bookmarks]:
            hasVariant($state, "collapse", "fullCollapsed") &&
            hasVariant($state, "activeSubBar", "bookmarks"),
          [sty.containercollapse_hover]: hasVariant(
            $state,
            "collapse",
            "hover"
          ),
          [sty.containerunAuthed]: hasVariant($state, "unAuthed", "unAuthed")
        })}
      >
        <div
          data-plasmic-name={"navMainMenuContainer"}
          data-plasmic-override={overrides.navMainMenuContainer}
          className={classNames(projectcss.all, sty.navMainMenuContainer, {
            [sty.navMainMenuContaineractivePage_profile]: hasVariant(
              $state,
              "activePage",
              "profile"
            ),
            [sty.navMainMenuContaineractivePage_settings]: hasVariant(
              $state,
              "activePage",
              "settings"
            ),
            [sty.navMainMenuContaineractiveSubBar_bookmarks]: hasVariant(
              $state,
              "activeSubBar",
              "bookmarks"
            ),
            [sty.navMainMenuContaineractiveSubBar_bookmarks_collapse_hover]:
              hasVariant($state, "collapse", "hover") &&
              hasVariant($state, "activeSubBar", "bookmarks"),
            [sty.navMainMenuContaineractiveSubBar_messages]: hasVariant(
              $state,
              "activeSubBar",
              "messages"
            ),
            [sty.navMainMenuContaineractiveSubBar_search]: hasVariant(
              $state,
              "activeSubBar",
              "search"
            ),
            [sty.navMainMenuContaineractiveSubBar_search_collapse_fullCollapsed]:
              hasVariant($state, "collapse", "fullCollapsed") &&
              hasVariant($state, "activeSubBar", "search"),
            [sty.navMainMenuContaineractiveSubBar_search_collapse_hover]:
              hasVariant($state, "collapse", "hover") &&
              hasVariant($state, "activeSubBar", "search"),
            [sty.navMainMenuContainercollapse_fullCollapsed]: hasVariant(
              $state,
              "collapse",
              "fullCollapsed"
            ),
            [sty.navMainMenuContainercollapse_fullCollapsed_activeSubBar_bookmarks]:
              hasVariant($state, "collapse", "fullCollapsed") &&
              hasVariant($state, "activeSubBar", "bookmarks"),
            [sty.navMainMenuContainercollapse_hover]: hasVariant(
              $state,
              "collapse",
              "hover"
            ),
            [sty.navMainMenuContainerunAuthed]: hasVariant(
              $state,
              "unAuthed",
              "unAuthed"
            )
          })}
          onMouseEnter={async event => {
            const $steps = {};

            $steps["updateCollapse"] =
              $state.collapse == "fullCollapsed"
                ? (() => {
                    const actionArgs = {
                      vgroup: "collapse",
                      operation: 0,
                      value: "hover"
                    };
                    return (({ vgroup, value }) => {
                      if (typeof value === "string") {
                        value = [value];
                      }

                      $stateSet($state, vgroup, value);
                      return value;
                    })?.apply(null, [actionArgs]);
                  })()
                : undefined;
            if (
              $steps["updateCollapse"] != null &&
              typeof $steps["updateCollapse"] === "object" &&
              typeof $steps["updateCollapse"].then === "function"
            ) {
              $steps["updateCollapse"] = await $steps["updateCollapse"];
            }
          }}
          onMouseLeave={async event => {
            const $steps = {};

            $steps["updateCollapse"] =
              $state.collapse == "hover"
                ? (() => {
                    const actionArgs = {
                      vgroup: "collapse",
                      operation: 0,
                      value: "fullCollapsed"
                    };
                    return (({ vgroup, value }) => {
                      if (typeof value === "string") {
                        value = [value];
                      }

                      $stateSet($state, vgroup, value);
                      return value;
                    })?.apply(null, [actionArgs]);
                  })()
                : undefined;
            if (
              $steps["updateCollapse"] != null &&
              typeof $steps["updateCollapse"] === "object" &&
              typeof $steps["updateCollapse"].then === "function"
            ) {
              $steps["updateCollapse"] = await $steps["updateCollapse"];
            }
          }}
        >
          <div
            data-plasmic-name={"navigationInteractions"}
            data-plasmic-override={overrides.navigationInteractions}
            className={classNames(projectcss.all, sty.navigationInteractions, {
              [sty.navigationInteractionsactiveSubBar_search]: hasVariant(
                $state,
                "activeSubBar",
                "search"
              ),
              [sty.navigationInteractionscollapse_fullCollapsed]: hasVariant(
                $state,
                "collapse",
                "fullCollapsed"
              ),
              [sty.navigationInteractionscollapse_hover]: hasVariant(
                $state,
                "collapse",
                "hover"
              )
            })}
          >
            <NavigationLogoStack
              data-plasmic-name={"navigationLogoStack"}
              data-plasmic-override={overrides.navigationLogoStack}
              className={classNames("__wab_instance", sty.navigationLogoStack, {
                [sty.navigationLogoStackactivePage_settings]: hasVariant(
                  $state,
                  "activePage",
                  "settings"
                ),
                [sty.navigationLogoStackactiveSubBar_search_collapse_fullCollapsed]:
                  hasVariant($state, "collapse", "fullCollapsed") &&
                  hasVariant($state, "activeSubBar", "search"),
                [sty.navigationLogoStackactiveSubBar_search_collapse_hover]:
                  hasVariant($state, "collapse", "hover") &&
                  hasVariant($state, "activeSubBar", "search"),
                [sty.navigationLogoStackcollapse_fullCollapsed]: hasVariant(
                  $state,
                  "collapse",
                  "fullCollapsed"
                ),
                [sty.navigationLogoStackcollapse_hover]: hasVariant(
                  $state,
                  "collapse",
                  "hover"
                )
              })}
              collapsed={
                hasVariant($state, "collapse", "fullCollapsed")
                  ? true
                  : undefined
              }
            />

            <div
              data-plasmic-name={"sideBarLinks"}
              data-plasmic-override={overrides.sideBarLinks}
              className={classNames(projectcss.all, sty.sideBarLinks, {
                [sty.sideBarLinksactivePage_profile]: hasVariant(
                  $state,
                  "activePage",
                  "profile"
                ),
                [sty.sideBarLinksactiveSubBar_bookmarks]: hasVariant(
                  $state,
                  "activeSubBar",
                  "bookmarks"
                ),
                [sty.sideBarLinkscollapse_fullCollapsed]: hasVariant(
                  $state,
                  "collapse",
                  "fullCollapsed"
                ),
                [sty.sideBarLinkscollapse_hover]: hasVariant(
                  $state,
                  "collapse",
                  "hover"
                )
              })}
            >
              <NavigationTopButtonAssembly
                data-plasmic-name={"navigationTopButtonAssembly"}
                data-plasmic-override={overrides.navigationTopButtonAssembly}
                activeTab={
                  hasVariant($state, "activeSubBar", "messages")
                    ? "slot3"
                    : hasVariant($state, "activeSubBar", "bookmarks")
                    ? "slot2"
                    : hasVariant($state, "activeSubBar", "search")
                    ? "slot1"
                    : undefined
                }
                className={classNames(
                  "__wab_instance",
                  sty.navigationTopButtonAssembly,
                  {
                    [sty.navigationTopButtonAssemblyactivePage_profile]:
                      hasVariant($state, "activePage", "profile"),
                    [sty.navigationTopButtonAssemblyactiveSubBar_bookmarks]:
                      hasVariant($state, "activeSubBar", "bookmarks"),
                    [sty.navigationTopButtonAssemblyactiveSubBar_messages]:
                      hasVariant($state, "activeSubBar", "messages"),
                    [sty.navigationTopButtonAssemblyactiveSubBar_search]:
                      hasVariant($state, "activeSubBar", "search"),
                    [sty.navigationTopButtonAssemblycollapse_fullCollapsed]:
                      hasVariant($state, "collapse", "fullCollapsed"),
                    [sty.navigationTopButtonAssemblycollapse_hover]: hasVariant(
                      $state,
                      "collapse",
                      "hover"
                    ),
                    [sty.navigationTopButtonAssemblyunAuthed]: hasVariant(
                      $state,
                      "unAuthed",
                      "unAuthed"
                    )
                  }
                )}
                collapsed={
                  hasVariant($state, "collapse", "fullCollapsed")
                    ? true
                    : undefined
                }
                onClickSlot1={async event => {
                  const $steps = {};

                  $steps["updateActiveSubBar"] = true
                    ? (() => {
                        const actionArgs = {
                          vgroup: "activeSubBar",
                          operation: 0,
                          value: "search"
                        };
                        return (({ vgroup, value }) => {
                          if (typeof value === "string") {
                            value = [value];
                          }

                          $stateSet($state, vgroup, value);
                          return value;
                        })?.apply(null, [actionArgs]);
                      })()
                    : undefined;
                  if (
                    $steps["updateActiveSubBar"] != null &&
                    typeof $steps["updateActiveSubBar"] === "object" &&
                    typeof $steps["updateActiveSubBar"].then === "function"
                  ) {
                    $steps["updateActiveSubBar"] = await $steps[
                      "updateActiveSubBar"
                    ];
                  }

                  $steps["updateCollapse"] = true
                    ? (() => {
                        const actionArgs = {
                          vgroup: "collapse",
                          operation: 0,
                          value: "fullCollapsed"
                        };
                        return (({ vgroup, value }) => {
                          if (typeof value === "string") {
                            value = [value];
                          }

                          $stateSet($state, vgroup, value);
                          return value;
                        })?.apply(null, [actionArgs]);
                      })()
                    : undefined;
                  if (
                    $steps["updateCollapse"] != null &&
                    typeof $steps["updateCollapse"] === "object" &&
                    typeof $steps["updateCollapse"].then === "function"
                  ) {
                    $steps["updateCollapse"] = await $steps["updateCollapse"];
                  }
                }}
                onClickSlot2={async event => {
                  const $steps = {};

                  $steps["updateActiveSubBar"] = true
                    ? (() => {
                        const actionArgs = {
                          vgroup: "activeSubBar",
                          operation: 0,
                          value: "bookmarks"
                        };
                        return (({ vgroup, value }) => {
                          if (typeof value === "string") {
                            value = [value];
                          }

                          $stateSet($state, vgroup, value);
                          return value;
                        })?.apply(null, [actionArgs]);
                      })()
                    : undefined;
                  if (
                    $steps["updateActiveSubBar"] != null &&
                    typeof $steps["updateActiveSubBar"] === "object" &&
                    typeof $steps["updateActiveSubBar"].then === "function"
                  ) {
                    $steps["updateActiveSubBar"] = await $steps[
                      "updateActiveSubBar"
                    ];
                  }

                  $steps["updateCollapse"] = true
                    ? (() => {
                        const actionArgs = {
                          vgroup: "collapse",
                          operation: 0,
                          value: "fullCollapsed"
                        };
                        return (({ vgroup, value }) => {
                          if (typeof value === "string") {
                            value = [value];
                          }

                          $stateSet($state, vgroup, value);
                          return value;
                        })?.apply(null, [actionArgs]);
                      })()
                    : undefined;
                  if (
                    $steps["updateCollapse"] != null &&
                    typeof $steps["updateCollapse"] === "object" &&
                    typeof $steps["updateCollapse"].then === "function"
                  ) {
                    $steps["updateCollapse"] = await $steps["updateCollapse"];
                  }
                }}
                onClickSlot3={async event => {
                  const $steps = {};

                  $steps["updateActiveSubBar"] = true
                    ? (() => {
                        const actionArgs = {
                          vgroup: "activeSubBar",
                          operation: 0,
                          value: "messages"
                        };
                        return (({ vgroup, value }) => {
                          if (typeof value === "string") {
                            value = [value];
                          }

                          $stateSet($state, vgroup, value);
                          return value;
                        })?.apply(null, [actionArgs]);
                      })()
                    : undefined;
                  if (
                    $steps["updateActiveSubBar"] != null &&
                    typeof $steps["updateActiveSubBar"] === "object" &&
                    typeof $steps["updateActiveSubBar"].then === "function"
                  ) {
                    $steps["updateActiveSubBar"] = await $steps[
                      "updateActiveSubBar"
                    ];
                  }

                  $steps["updateCollapse"] = true
                    ? (() => {
                        const actionArgs = {
                          vgroup: "collapse",
                          operation: 0,
                          value: "fullCollapsed"
                        };
                        return (({ vgroup, value }) => {
                          if (typeof value === "string") {
                            value = [value];
                          }

                          $stateSet($state, vgroup, value);
                          return value;
                        })?.apply(null, [actionArgs]);
                      })()
                    : undefined;
                  if (
                    $steps["updateCollapse"] != null &&
                    typeof $steps["updateCollapse"] === "object" &&
                    typeof $steps["updateCollapse"].then === "function"
                  ) {
                    $steps["updateCollapse"] = await $steps["updateCollapse"];
                  }
                }}
                unAuthed={
                  hasVariant($state, "unAuthed", "unAuthed") ? true : undefined
                }
              />
            </div>
          </div>
          <section
            data-plasmic-name={"workingInfo"}
            data-plasmic-override={overrides.workingInfo}
            className={classNames(projectcss.all, sty.workingInfo, {
              [sty.workingInfoactivePage_profile]: hasVariant(
                $state,
                "activePage",
                "profile"
              ),
              [sty.workingInfoactiveSubBar_search]: hasVariant(
                $state,
                "activeSubBar",
                "search"
              ),
              [sty.workingInfocollapse_fullCollapsed]: hasVariant(
                $state,
                "collapse",
                "fullCollapsed"
              ),
              [sty.workingInfocollapse_hover]: hasVariant(
                $state,
                "collapse",
                "hover"
              )
            })}
          >
            <NavigationBottomButtonAssembly
              data-plasmic-name={"navigationBottomButtonAssembly"}
              data-plasmic-override={overrides.navigationBottomButtonAssembly}
              activeTab={
                hasVariant($state, "activePage", "settings")
                  ? "slot2"
                  : hasVariant($state, "activePage", "profile")
                  ? "slot1"
                  : undefined
              }
              className={classNames(
                "__wab_instance",
                sty.navigationBottomButtonAssembly,
                {
                  [sty.navigationBottomButtonAssemblyactivePage_profile]:
                    hasVariant($state, "activePage", "profile"),
                  [sty.navigationBottomButtonAssemblyactivePage_settings]:
                    hasVariant($state, "activePage", "settings"),
                  [sty.navigationBottomButtonAssemblyactiveSubBar_search]:
                    hasVariant($state, "activeSubBar", "search"),
                  [sty.navigationBottomButtonAssemblycollapse_fullCollapsed]:
                    hasVariant($state, "collapse", "fullCollapsed"),
                  [sty.navigationBottomButtonAssemblycollapse_hover]:
                    hasVariant($state, "collapse", "hover"),
                  [sty.navigationBottomButtonAssemblyunAuthed]: hasVariant(
                    $state,
                    "unAuthed",
                    "unAuthed"
                  )
                }
              )}
              collapsed={
                hasVariant($state, "collapse", "fullCollapsed")
                  ? true
                  : undefined
              }
              onClickSlot1={async event => {
                const $steps = {};

                $steps["updateActivePage"] = true
                  ? (() => {
                      const actionArgs = {
                        vgroup: "activePage",
                        operation: 0,
                        value: "profile"
                      };
                      return (({ vgroup, value }) => {
                        if (typeof value === "string") {
                          value = [value];
                        }

                        $stateSet($state, vgroup, value);
                        return value;
                      })?.apply(null, [actionArgs]);
                    })()
                  : undefined;
                if (
                  $steps["updateActivePage"] != null &&
                  typeof $steps["updateActivePage"] === "object" &&
                  typeof $steps["updateActivePage"].then === "function"
                ) {
                  $steps["updateActivePage"] = await $steps["updateActivePage"];
                }

                $steps["goToPage"] = true
                  ? (() => {
                      const actionArgs = {
                        destination: (() => {
                          try {
                            return undefined;
                          } catch (e) {
                            if (
                              e instanceof TypeError ||
                              e?.plasmicType === "PlasmicUndefinedDataError"
                            ) {
                              return undefined;
                            }
                            throw e;
                          }
                        })()
                      };
                      return (({ destination }) => {
                        if (
                          typeof destination === "string" &&
                          destination.startsWith("#")
                        ) {
                          document
                            .getElementById(destination.substr(1))
                            .scrollIntoView({ behavior: "smooth" });
                        } else {
                          __nextRouter?.push(destination);
                        }
                      })?.apply(null, [actionArgs]);
                    })()
                  : undefined;
                if (
                  $steps["goToPage"] != null &&
                  typeof $steps["goToPage"] === "object" &&
                  typeof $steps["goToPage"].then === "function"
                ) {
                  $steps["goToPage"] = await $steps["goToPage"];
                }
              }}
              onClickSlot2={async event => {
                const $steps = {};

                $steps["updateActivePage"] = true
                  ? (() => {
                      const actionArgs = {
                        vgroup: "activePage",
                        operation: 0,
                        value: "settings"
                      };
                      return (({ vgroup, value }) => {
                        if (typeof value === "string") {
                          value = [value];
                        }

                        $stateSet($state, vgroup, value);
                        return value;
                      })?.apply(null, [actionArgs]);
                    })()
                  : undefined;
                if (
                  $steps["updateActivePage"] != null &&
                  typeof $steps["updateActivePage"] === "object" &&
                  typeof $steps["updateActivePage"].then === "function"
                ) {
                  $steps["updateActivePage"] = await $steps["updateActivePage"];
                }

                $steps["goToSettings"] = true
                  ? (() => {
                      const actionArgs = { destination: "/settings" };
                      return (({ destination }) => {
                        if (
                          typeof destination === "string" &&
                          destination.startsWith("#")
                        ) {
                          document
                            .getElementById(destination.substr(1))
                            .scrollIntoView({ behavior: "smooth" });
                        } else {
                          __nextRouter?.push(destination);
                        }
                      })?.apply(null, [actionArgs]);
                    })()
                  : undefined;
                if (
                  $steps["goToSettings"] != null &&
                  typeof $steps["goToSettings"] === "object" &&
                  typeof $steps["goToSettings"].then === "function"
                ) {
                  $steps["goToSettings"] = await $steps["goToSettings"];
                }
              }}
              unAuthed={
                hasVariant($state, "unAuthed", "unAuthed") ? true : undefined
              }
            />
          </section>
        </div>
        <section
          data-plasmic-name={"subMenuInformation"}
          data-plasmic-override={overrides.subMenuInformation}
          className={classNames(projectcss.all, sty.subMenuInformation, {
            [sty.subMenuInformationactiveSubBar_bookmarks]: hasVariant(
              $state,
              "activeSubBar",
              "bookmarks"
            ),
            [sty.subMenuInformationactiveSubBar_messages]: hasVariant(
              $state,
              "activeSubBar",
              "messages"
            ),
            [sty.subMenuInformationactiveSubBar_search]: hasVariant(
              $state,
              "activeSubBar",
              "search"
            ),
            [sty.subMenuInformationcollapse_fullCollapsed]: hasVariant(
              $state,
              "collapse",
              "fullCollapsed"
            ),
            [sty.subMenuInformationcollapse_hover]: hasVariant(
              $state,
              "collapse",
              "hover"
            )
          })}
        >
          <NavigationSubBarMenus
            data-plasmic-name={"navigationSubBarMenus"}
            data-plasmic-override={overrides.navigationSubBarMenus}
            className={classNames("__wab_instance", sty.navigationSubBarMenus, {
              [sty.navigationSubBarMenusactivePage_profile]: hasVariant(
                $state,
                "activePage",
                "profile"
              ),
              [sty.navigationSubBarMenusactivePage_settings]: hasVariant(
                $state,
                "activePage",
                "settings"
              ),
              [sty.navigationSubBarMenusactiveSubBar_bookmarks]: hasVariant(
                $state,
                "activeSubBar",
                "bookmarks"
              ),
              [sty.navigationSubBarMenusactiveSubBar_bookmarks_collapse_hover]:
                hasVariant($state, "collapse", "hover") &&
                hasVariant($state, "activeSubBar", "bookmarks"),
              [sty.navigationSubBarMenusactiveSubBar_messages]: hasVariant(
                $state,
                "activeSubBar",
                "messages"
              ),
              [sty.navigationSubBarMenusactiveSubBar_messages_collapse_fullCollapsed]:
                hasVariant($state, "collapse", "fullCollapsed") &&
                hasVariant($state, "activeSubBar", "messages"),
              [sty.navigationSubBarMenusactiveSubBar_messages_collapse_hover]:
                hasVariant($state, "collapse", "hover") &&
                hasVariant($state, "activeSubBar", "messages"),
              [sty.navigationSubBarMenusactiveSubBar_search]: hasVariant(
                $state,
                "activeSubBar",
                "search"
              ),
              [sty.navigationSubBarMenusactiveSubBar_search_collapse_fullCollapsed]:
                hasVariant($state, "collapse", "fullCollapsed") &&
                hasVariant($state, "activeSubBar", "search"),
              [sty.navigationSubBarMenusactiveSubBar_search_collapse_hover]:
                hasVariant($state, "collapse", "hover") &&
                hasVariant($state, "activeSubBar", "search"),
              [sty.navigationSubBarMenuscollapse_fullCollapsed]: hasVariant(
                $state,
                "collapse",
                "fullCollapsed"
              ),
              [sty.navigationSubBarMenuscollapse_fullCollapsed_activeSubBar_bookmarks]:
                hasVariant($state, "collapse", "fullCollapsed") &&
                hasVariant($state, "activeSubBar", "bookmarks"),
              [sty.navigationSubBarMenuscollapse_hover]: hasVariant(
                $state,
                "collapse",
                "hover"
              ),
              [sty.navigationSubBarMenusunAuthed]: hasVariant(
                $state,
                "unAuthed",
                "unAuthed"
              )
            })}
            onClickCloseButton={async event => {
              const $steps = {};

              $steps["updateActiveSubBar"] = true
                ? (() => {
                    const actionArgs = {
                      vgroup: "activeSubBar",
                      operation: 1,
                      value: "search"
                    };
                    return (({ vgroup, value }) => {
                      if (typeof value === "string") {
                        value = [value];
                      }

                      $stateSet($state, vgroup, undefined);
                      return undefined;
                    })?.apply(null, [actionArgs]);
                  })()
                : undefined;
              if (
                $steps["updateActiveSubBar"] != null &&
                typeof $steps["updateActiveSubBar"] === "object" &&
                typeof $steps["updateActiveSubBar"].then === "function"
              ) {
                $steps["updateActiveSubBar"] = await $steps[
                  "updateActiveSubBar"
                ];
              }

              $steps["updateCollapse"] =
                $state.activeSubBar == null
                  ? (() => {
                      const actionArgs = {
                        vgroup: "collapse",
                        operation: 0,
                        value: []
                      };
                      return (({ vgroup, value }) => {
                        if (typeof value === "string") {
                          value = [value];
                        }

                        $stateSet($state, vgroup, value);
                        return value;
                      })?.apply(null, [actionArgs]);
                    })()
                  : undefined;
              if (
                $steps["updateCollapse"] != null &&
                typeof $steps["updateCollapse"] === "object" &&
                typeof $steps["updateCollapse"].then === "function"
              ) {
                $steps["updateCollapse"] = await $steps["updateCollapse"];
              }
            }}
            subMenuContent={
              hasVariant($state, "activeSubBar", "messages")
                ? "messages"
                : hasVariant($state, "activeSubBar", "bookmarks")
                ? "bookmarks"
                : hasVariant($state, "activeSubBar", "search")
                ? "search"
                : undefined
            }
          />
        </section>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  wrapper: [
    "wrapper",
    "container",
    "navMainMenuContainer",
    "navigationInteractions",
    "navigationLogoStack",
    "sideBarLinks",
    "navigationTopButtonAssembly",
    "workingInfo",
    "navigationBottomButtonAssembly",
    "subMenuInformation",
    "navigationSubBarMenus"
  ],
  container: [
    "container",
    "navMainMenuContainer",
    "navigationInteractions",
    "navigationLogoStack",
    "sideBarLinks",
    "navigationTopButtonAssembly",
    "workingInfo",
    "navigationBottomButtonAssembly",
    "subMenuInformation",
    "navigationSubBarMenus"
  ],
  navMainMenuContainer: [
    "navMainMenuContainer",
    "navigationInteractions",
    "navigationLogoStack",
    "sideBarLinks",
    "navigationTopButtonAssembly",
    "workingInfo",
    "navigationBottomButtonAssembly"
  ],
  navigationInteractions: [
    "navigationInteractions",
    "navigationLogoStack",
    "sideBarLinks",
    "navigationTopButtonAssembly"
  ],
  navigationLogoStack: ["navigationLogoStack"],
  sideBarLinks: ["sideBarLinks", "navigationTopButtonAssembly"],
  navigationTopButtonAssembly: ["navigationTopButtonAssembly"],
  workingInfo: ["workingInfo", "navigationBottomButtonAssembly"],
  navigationBottomButtonAssembly: ["navigationBottomButtonAssembly"],
  subMenuInformation: ["subMenuInformation", "navigationSubBarMenus"],
  navigationSubBarMenus: ["navigationSubBarMenus"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  wrapper: "div";
  container: "div";
  navMainMenuContainer: "div";
  navigationInteractions: "div";
  navigationLogoStack: typeof NavigationLogoStack;
  sideBarLinks: "div";
  navigationTopButtonAssembly: typeof NavigationTopButtonAssembly;
  workingInfo: "section";
  navigationBottomButtonAssembly: typeof NavigationBottomButtonAssembly;
  subMenuInformation: "section";
  navigationSubBarMenus: typeof NavigationSubBarMenus;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationSidebar__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationSidebar__VariantsArgs;
    args?: PlasmicNavigationSidebar__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicNavigationSidebar__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationSidebar__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationSidebar__ArgProps,
          internalVariantPropNames: PlasmicNavigationSidebar__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationSidebar__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "wrapper") {
    func.displayName = "PlasmicNavigationSidebar";
  } else {
    func.displayName = `PlasmicNavigationSidebar.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationSidebar = Object.assign(
  // Top-level PlasmicNavigationSidebar renders the root element
  makeNodeComponent("wrapper"),
  {
    // Helper components rendering sub-elements
    container: makeNodeComponent("container"),
    navMainMenuContainer: makeNodeComponent("navMainMenuContainer"),
    navigationInteractions: makeNodeComponent("navigationInteractions"),
    navigationLogoStack: makeNodeComponent("navigationLogoStack"),
    sideBarLinks: makeNodeComponent("sideBarLinks"),
    navigationTopButtonAssembly: makeNodeComponent(
      "navigationTopButtonAssembly"
    ),
    workingInfo: makeNodeComponent("workingInfo"),
    navigationBottomButtonAssembly: makeNodeComponent(
      "navigationBottomButtonAssembly"
    ),
    subMenuInformation: makeNodeComponent("subMenuInformation"),
    navigationSubBarMenus: makeNodeComponent("navigationSubBarMenus"),

    // Metadata about props expected for PlasmicNavigationSidebar
    internalVariantProps: PlasmicNavigationSidebar__VariantProps,
    internalArgProps: PlasmicNavigationSidebar__ArgProps
  }
);

export default PlasmicNavigationSidebar;
/* prettier-ignore-end */
