/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: EbN_LUazXitj

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import NavigationActiveSlotSlider from "../../NavigationActiveSlotSlider"; // plasmic-import: Jnr7Ch5x507b/component
import NavigationHoverSlotSlider from "../../NavigationHoverSlotSlider"; // plasmic-import: 7r9yyvza21zd/component
import NavigationButton from "../../NavigationButton"; // plasmic-import: Wr1MGCSkm1uC/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationTopButtonAssembly.module.css"; // plasmic-import: EbN_LUazXitj/css

import SearchIcon from "./icons/PlasmicIcon__Search"; // plasmic-import: hosM0D-aZ2A1/icon
import BookmarkEmptyIcon from "./icons/PlasmicIcon__BookmarkEmpty"; // plasmic-import: CaAupFMSbhPP/icon
import MessagesIcon from "./icons/PlasmicIcon__Messages"; // plasmic-import: PjKgIKB6-HKl/icon

createPlasmicElementProxy;

export type PlasmicNavigationTopButtonAssembly__VariantMembers = {
  hoverTab: "slot1" | "slot2" | "slot3";
  activeTab: "slot1" | "slot2" | "slot3";
  collapsed: "collapsed";
  unAuthed: "unAuthed";
};
export type PlasmicNavigationTopButtonAssembly__VariantsArgs = {
  hoverTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  activeTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  unAuthed?: SingleBooleanChoiceArg<"unAuthed">;
};
type VariantPropType = keyof PlasmicNavigationTopButtonAssembly__VariantsArgs;
export const PlasmicNavigationTopButtonAssembly__VariantProps =
  new Array<VariantPropType>("hoverTab", "activeTab", "collapsed", "unAuthed");

export type PlasmicNavigationTopButtonAssembly__ArgsType = {
  onClickSlot1?: (event: any) => void;
  onClickSlot2?: (event: any) => void;
  onClickSlot3?: (event: any) => void;
};
type ArgPropType = keyof PlasmicNavigationTopButtonAssembly__ArgsType;
export const PlasmicNavigationTopButtonAssembly__ArgProps =
  new Array<ArgPropType>("onClickSlot1", "onClickSlot2", "onClickSlot3");

export type PlasmicNavigationTopButtonAssembly__OverridesType = {
  navTopButtonAssembly?: Flex__<"nav">;
  navigationActiveSlotSlider?: Flex__<typeof NavigationActiveSlotSlider>;
  navigationHoverSlotSlider?: Flex__<typeof NavigationHoverSlotSlider>;
  searchButton?: Flex__<typeof NavigationButton>;
  icon?: Flex__<"svg">;
  bookmarkButton?: Flex__<typeof NavigationButton>;
  icon2?: Flex__<"svg">;
  messagesButton?: Flex__<typeof NavigationButton>;
  icon3?: Flex__<"svg">;
};

export interface DefaultNavigationTopButtonAssemblyProps {
  onClickSlot1?: (event: any) => void;
  onClickSlot2?: (event: any) => void;
  onClickSlot3?: (event: any) => void;
  hoverTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  activeTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  unAuthed?: SingleBooleanChoiceArg<"unAuthed">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationTopButtonAssembly__RenderFunc(props: {
  variants: PlasmicNavigationTopButtonAssembly__VariantsArgs;
  args: PlasmicNavigationTopButtonAssembly__ArgsType;
  overrides: PlasmicNavigationTopButtonAssembly__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "activeTab",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.activeTab
      },
      {
        path: "collapsed",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.collapsed
      },
      {
        path: "hoverTab",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hoverTab
      },
      {
        path: "searchButton.modals",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "bookmarkButton.modals",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "messagesButton.modals",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "unAuthed",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.unAuthed
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <nav
      data-plasmic-name={"navTopButtonAssembly"}
      data-plasmic-override={overrides.navTopButtonAssembly}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.navTopButtonAssembly,
        {
          [sty.navTopButtonAssemblyactiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.navTopButtonAssemblyactiveTab_slot1_hoverTab_slot1]:
            hasVariant($state, "hoverTab", "slot1") &&
            hasVariant($state, "activeTab", "slot1"),
          [sty.navTopButtonAssemblyactiveTab_slot3]: hasVariant(
            $state,
            "activeTab",
            "slot3"
          ),
          [sty.navTopButtonAssemblyactiveTab_slot3_hoverTab_slot3]:
            hasVariant($state, "hoverTab", "slot3") &&
            hasVariant($state, "activeTab", "slot3"),
          [sty.navTopButtonAssemblycollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.navTopButtonAssemblyhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.navTopButtonAssemblyhoverTab_slot3]: hasVariant(
            $state,
            "hoverTab",
            "slot3"
          )
        }
      )}
    >
      <NavigationActiveSlotSlider
        data-plasmic-name={"navigationActiveSlotSlider"}
        data-plasmic-override={overrides.navigationActiveSlotSlider}
        activeTab={
          hasVariant($state, "hoverTab", "slot3") &&
          hasVariant($state, "activeTab", "slot3")
            ? "slot3"
            : hasVariant($state, "hoverTab", "slot2") &&
              hasVariant($state, "activeTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : hasVariant($state, "activeTab", "slot3")
            ? "slot3"
            : hasVariant($state, "activeTab", "slot2")
            ? "slot2"
            : hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : undefined
        }
        className={classNames(
          "__wab_instance",
          sty.navigationActiveSlotSlider,
          {
            [sty.navigationActiveSlotSlideractiveTab_slot1]: hasVariant(
              $state,
              "activeTab",
              "slot1"
            ),
            [sty.navigationActiveSlotSlideractiveTab_slot1_hoverTab_slot1]:
              hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1"),
            [sty.navigationActiveSlotSlideractiveTab_slot2]: hasVariant(
              $state,
              "activeTab",
              "slot2"
            ),
            [sty.navigationActiveSlotSlideractiveTab_slot2_hoverTab_slot2]:
              hasVariant($state, "hoverTab", "slot2") &&
              hasVariant($state, "activeTab", "slot2"),
            [sty.navigationActiveSlotSlideractiveTab_slot3]: hasVariant(
              $state,
              "activeTab",
              "slot3"
            ),
            [sty.navigationActiveSlotSlideractiveTab_slot3_hoverTab_slot3]:
              hasVariant($state, "hoverTab", "slot3") &&
              hasVariant($state, "activeTab", "slot3"),
            [sty.navigationActiveSlotSlidercollapsed]: hasVariant(
              $state,
              "collapsed",
              "collapsed"
            ),
            [sty.navigationActiveSlotSliderhoverTab_slot1]: hasVariant(
              $state,
              "hoverTab",
              "slot1"
            ),
            [sty.navigationActiveSlotSliderhoverTab_slot2]: hasVariant(
              $state,
              "hoverTab",
              "slot2"
            ),
            [sty.navigationActiveSlotSliderhoverTab_slot3]: hasVariant(
              $state,
              "hoverTab",
              "slot3"
            )
          }
        )}
        hoverTab={
          hasVariant($state, "hoverTab", "slot3") &&
          hasVariant($state, "activeTab", "slot3")
            ? "slot3"
            : hasVariant($state, "hoverTab", "slot2") &&
              hasVariant($state, "activeTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : hasVariant($state, "hoverTab", "slot3")
            ? "slot3"
            : hasVariant($state, "hoverTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1")
            ? "slot1"
            : undefined
        }
      />

      <NavigationHoverSlotSlider
        data-plasmic-name={"navigationHoverSlotSlider"}
        data-plasmic-override={overrides.navigationHoverSlotSlider}
        activeTab={
          hasVariant($state, "activeTab", "slot3") &&
          hasVariant($state, "hoverTab", "slot2")
            ? "slot3"
            : hasVariant($state, "activeTab", "slot2") &&
              hasVariant($state, "hoverTab", "slot3")
            ? "slot2"
            : hasVariant($state, "activeTab", "slot3") &&
              hasVariant($state, "hoverTab", "slot1")
            ? "slot3"
            : hasVariant($state, "activeTab", "slot1") &&
              hasVariant($state, "hoverTab", "slot3")
            ? "slot1"
            : hasVariant($state, "activeTab", "slot2") &&
              hasVariant($state, "hoverTab", "slot1")
            ? "slot2"
            : hasVariant($state, "activeTab", "slot1") &&
              hasVariant($state, "hoverTab", "slot2")
            ? "slot1"
            : hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : hasVariant($state, "activeTab", "slot3")
            ? "slot3"
            : hasVariant($state, "activeTab", "slot2")
            ? "slot2"
            : hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : undefined
        }
        className={classNames("__wab_instance", sty.navigationHoverSlotSlider, {
          [sty.navigationHoverSlotSlideractiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.navigationHoverSlotSlideractiveTab_slot1_hoverTab_slot1]:
            hasVariant($state, "hoverTab", "slot1") &&
            hasVariant($state, "activeTab", "slot1"),
          [sty.navigationHoverSlotSlideractiveTab_slot1_hoverTab_slot2]:
            hasVariant($state, "activeTab", "slot1") &&
            hasVariant($state, "hoverTab", "slot2"),
          [sty.navigationHoverSlotSlideractiveTab_slot1_hoverTab_slot3]:
            hasVariant($state, "activeTab", "slot1") &&
            hasVariant($state, "hoverTab", "slot3"),
          [sty.navigationHoverSlotSlideractiveTab_slot2]: hasVariant(
            $state,
            "activeTab",
            "slot2"
          ),
          [sty.navigationHoverSlotSlideractiveTab_slot2_hoverTab_slot1]:
            hasVariant($state, "activeTab", "slot2") &&
            hasVariant($state, "hoverTab", "slot1"),
          [sty.navigationHoverSlotSlideractiveTab_slot2_hoverTab_slot3]:
            hasVariant($state, "activeTab", "slot2") &&
            hasVariant($state, "hoverTab", "slot3"),
          [sty.navigationHoverSlotSlideractiveTab_slot3]: hasVariant(
            $state,
            "activeTab",
            "slot3"
          ),
          [sty.navigationHoverSlotSlideractiveTab_slot3_hoverTab_slot2]:
            hasVariant($state, "activeTab", "slot3") &&
            hasVariant($state, "hoverTab", "slot2"),
          [sty.navigationHoverSlotSliderhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.navigationHoverSlotSliderhoverTab_slot1_activeTab_slot3]:
            hasVariant($state, "activeTab", "slot3") &&
            hasVariant($state, "hoverTab", "slot1"),
          [sty.navigationHoverSlotSliderhoverTab_slot2]: hasVariant(
            $state,
            "hoverTab",
            "slot2"
          ),
          [sty.navigationHoverSlotSliderhoverTab_slot3]: hasVariant(
            $state,
            "hoverTab",
            "slot3"
          )
        })}
        hoverTab={
          hasVariant($state, "activeTab", "slot3") &&
          hasVariant($state, "hoverTab", "slot2")
            ? "slot2"
            : hasVariant($state, "activeTab", "slot2") &&
              hasVariant($state, "hoverTab", "slot3")
            ? "slot3"
            : hasVariant($state, "activeTab", "slot3") &&
              hasVariant($state, "hoverTab", "slot1")
            ? "slot1"
            : hasVariant($state, "activeTab", "slot1") &&
              hasVariant($state, "hoverTab", "slot3")
            ? "slot3"
            : hasVariant($state, "activeTab", "slot2") &&
              hasVariant($state, "hoverTab", "slot1")
            ? "slot1"
            : hasVariant($state, "activeTab", "slot1") &&
              hasVariant($state, "hoverTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : hasVariant($state, "hoverTab", "slot3")
            ? "slot3"
            : hasVariant($state, "hoverTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1")
            ? "slot1"
            : undefined
        }
      />

      <NavigationButton
        data-plasmic-name={"searchButton"}
        data-plasmic-override={overrides.searchButton}
        buttonText={"Search"}
        className={classNames("__wab_instance", sty.searchButton, {
          [sty.searchButtonactiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.searchButtonactiveTab_slot1_hoverTab_slot1]:
            hasVariant($state, "hoverTab", "slot1") &&
            hasVariant($state, "activeTab", "slot1"),
          [sty.searchButtonactiveTab_slot3]: hasVariant(
            $state,
            "activeTab",
            "slot3"
          ),
          [sty.searchButtoncollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.searchButtonhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.searchButtonhoverTab_slot3]: hasVariant(
            $state,
            "hoverTab",
            "slot3"
          )
        })}
        collapsed={
          hasVariant($state, "collapsed", "collapsed") ? true : undefined
        }
        modals={generateStateValueProp($state, ["searchButton", "modals"])}
        navIconSlot={
          <SearchIcon
            data-plasmic-name={"icon"}
            data-plasmic-override={overrides.icon}
            className={classNames(projectcss.all, sty.icon, {
              [sty.iconcollapsed]: hasVariant($state, "collapsed", "collapsed")
            })}
            role={"img"}
          />
        }
        onClick={args.onClickSlot1}
        onModalsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["searchButton", "modals"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPointerEnter={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 0,
                  value: "slot1"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
        onPointerLeave={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 1,
                  value: "slot1"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, undefined);
                  return undefined;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
      />

      <NavigationButton
        data-plasmic-name={"bookmarkButton"}
        data-plasmic-override={overrides.bookmarkButton}
        buttonText={"Bookmarks"}
        className={classNames("__wab_instance", sty.bookmarkButton, {
          [sty.bookmarkButtonactiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.bookmarkButtonactiveTab_slot2]: hasVariant(
            $state,
            "activeTab",
            "slot2"
          ),
          [sty.bookmarkButtonactiveTab_slot2_hoverTab_slot1]:
            hasVariant($state, "activeTab", "slot2") &&
            hasVariant($state, "hoverTab", "slot1"),
          [sty.bookmarkButtonactiveTab_slot2_hoverTab_slot2]:
            hasVariant($state, "hoverTab", "slot2") &&
            hasVariant($state, "activeTab", "slot2"),
          [sty.bookmarkButtonactiveTab_slot3]: hasVariant(
            $state,
            "activeTab",
            "slot3"
          ),
          [sty.bookmarkButtoncollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.bookmarkButtonhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.bookmarkButtonhoverTab_slot2]: hasVariant(
            $state,
            "hoverTab",
            "slot2"
          ),
          [sty.bookmarkButtonhoverTab_slot3]: hasVariant(
            $state,
            "hoverTab",
            "slot3"
          ),
          [sty.bookmarkButtonunAuthed]: hasVariant(
            $state,
            "unAuthed",
            "unAuthed"
          )
        })}
        collapsed={
          hasVariant($state, "collapsed", "collapsed") ? true : undefined
        }
        modals={generateStateValueProp($state, ["bookmarkButton", "modals"])}
        navIconSlot={
          <BookmarkEmptyIcon
            data-plasmic-name={"icon2"}
            data-plasmic-override={overrides.icon2}
            className={classNames(projectcss.all, sty.icon2)}
            role={"img"}
          />
        }
        onClick={args.onClickSlot2}
        onModalsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["bookmarkButton", "modals"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPointerEnter={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 0,
                  value: "slot2"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
        onPointerLeave={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 1,
                  value: []
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, undefined);
                  return undefined;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
      />

      <NavigationButton
        data-plasmic-name={"messagesButton"}
        data-plasmic-override={overrides.messagesButton}
        buttonText={"Messages"}
        className={classNames("__wab_instance", sty.messagesButton, {
          [sty.messagesButtonactiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.messagesButtonactiveTab_slot3]: hasVariant(
            $state,
            "activeTab",
            "slot3"
          ),
          [sty.messagesButtonactiveTab_slot3_hoverTab_slot3]:
            hasVariant($state, "hoverTab", "slot3") &&
            hasVariant($state, "activeTab", "slot3"),
          [sty.messagesButtoncollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.messagesButtonhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.messagesButtonhoverTab_slot3]: hasVariant(
            $state,
            "hoverTab",
            "slot3"
          ),
          [sty.messagesButtonunAuthed]: hasVariant(
            $state,
            "unAuthed",
            "unAuthed"
          )
        })}
        collapsed={
          hasVariant($state, "collapsed", "collapsed") ? true : undefined
        }
        modals={generateStateValueProp($state, ["messagesButton", "modals"])}
        navIconSlot={
          <MessagesIcon
            data-plasmic-name={"icon3"}
            data-plasmic-override={overrides.icon3}
            className={classNames(projectcss.all, sty.icon3)}
            role={"img"}
          />
        }
        onClick={args.onClickSlot3}
        onModalsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["messagesButton", "modals"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPointerEnter={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 0,
                  value: "slot3"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
        onPointerLeave={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 1,
                  value: "slot3"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, undefined);
                  return undefined;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
      />
    </nav>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  navTopButtonAssembly: [
    "navTopButtonAssembly",
    "navigationActiveSlotSlider",
    "navigationHoverSlotSlider",
    "searchButton",
    "icon",
    "bookmarkButton",
    "icon2",
    "messagesButton",
    "icon3"
  ],
  navigationActiveSlotSlider: ["navigationActiveSlotSlider"],
  navigationHoverSlotSlider: ["navigationHoverSlotSlider"],
  searchButton: ["searchButton", "icon"],
  icon: ["icon"],
  bookmarkButton: ["bookmarkButton", "icon2"],
  icon2: ["icon2"],
  messagesButton: ["messagesButton", "icon3"],
  icon3: ["icon3"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  navTopButtonAssembly: "nav";
  navigationActiveSlotSlider: typeof NavigationActiveSlotSlider;
  navigationHoverSlotSlider: typeof NavigationHoverSlotSlider;
  searchButton: typeof NavigationButton;
  icon: "svg";
  bookmarkButton: typeof NavigationButton;
  icon2: "svg";
  messagesButton: typeof NavigationButton;
  icon3: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationTopButtonAssembly__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationTopButtonAssembly__VariantsArgs;
    args?: PlasmicNavigationTopButtonAssembly__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicNavigationTopButtonAssembly__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationTopButtonAssembly__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationTopButtonAssembly__ArgProps,
          internalVariantPropNames:
            PlasmicNavigationTopButtonAssembly__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationTopButtonAssembly__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "navTopButtonAssembly") {
    func.displayName = "PlasmicNavigationTopButtonAssembly";
  } else {
    func.displayName = `PlasmicNavigationTopButtonAssembly.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationTopButtonAssembly = Object.assign(
  // Top-level PlasmicNavigationTopButtonAssembly renders the root element
  makeNodeComponent("navTopButtonAssembly"),
  {
    // Helper components rendering sub-elements
    navigationActiveSlotSlider: makeNodeComponent("navigationActiveSlotSlider"),
    navigationHoverSlotSlider: makeNodeComponent("navigationHoverSlotSlider"),
    searchButton: makeNodeComponent("searchButton"),
    icon: makeNodeComponent("icon"),
    bookmarkButton: makeNodeComponent("bookmarkButton"),
    icon2: makeNodeComponent("icon2"),
    messagesButton: makeNodeComponent("messagesButton"),
    icon3: makeNodeComponent("icon3"),

    // Metadata about props expected for PlasmicNavigationTopButtonAssembly
    internalVariantProps: PlasmicNavigationTopButtonAssembly__VariantProps,
    internalArgProps: PlasmicNavigationTopButtonAssembly__ArgProps
  }
);

export default PlasmicNavigationTopButtonAssembly;
/* prettier-ignore-end */
