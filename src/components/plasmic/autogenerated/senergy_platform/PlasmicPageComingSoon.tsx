/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 50ENdwdc_-7i

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileOverviewSeeMoreButton from "../../ProfileOverviewSeeMoreButton"; // plasmic-import: UWTH8LMACb3s/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicPageComingSoon.module.css"; // plasmic-import: 50ENdwdc_-7i/css

createPlasmicElementProxy;

export type PlasmicPageComingSoon__VariantMembers = {};
export type PlasmicPageComingSoon__VariantsArgs = {};
type VariantPropType = keyof PlasmicPageComingSoon__VariantsArgs;
export const PlasmicPageComingSoon__VariantProps = new Array<VariantPropType>();

export type PlasmicPageComingSoon__ArgsType = {
  onClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicPageComingSoon__ArgsType;
export const PlasmicPageComingSoon__ArgProps = new Array<ArgPropType>(
  "onClick"
);

export type PlasmicPageComingSoon__OverridesType = {
  pageBase?: Flex__<"div">;
  main?: Flex__<"section">;
  freeBox?: Flex__<"div">;
  h2?: Flex__<"h2">;
  join?: Flex__<typeof ProfileOverviewSeeMoreButton>;
};

export interface DefaultPageComingSoonProps {
  onClick?: (event: any) => void;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicPageComingSoon__RenderFunc(props: {
  variants: PlasmicPageComingSoon__VariantsArgs;
  args: PlasmicPageComingSoon__ArgsType;
  overrides: PlasmicPageComingSoon__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"pageBase"}
      data-plasmic-override={overrides.pageBase}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.pageBase
      )}
    >
      <section
        data-plasmic-name={"main"}
        data-plasmic-override={overrides.main}
        className={classNames(projectcss.all, sty.main)}
      >
        <div
          data-plasmic-name={"freeBox"}
          data-plasmic-override={overrides.freeBox}
          className={classNames(projectcss.all, sty.freeBox)}
        >
          <h1
            className={classNames(
              projectcss.all,
              projectcss.h1,
              projectcss.__wab_text,
              sty.h1__ozCot
            )}
          >
            {"SENERGY"}
          </h1>
          <h1
            className={classNames(
              projectcss.all,
              projectcss.h1,
              projectcss.__wab_text,
              sty.h1__xGNvT
            )}
          >
            {".WORKS"}
          </h1>
        </div>
        <h2
          data-plasmic-name={"h2"}
          data-plasmic-override={overrides.h2}
          className={classNames(
            projectcss.all,
            projectcss.h2,
            projectcss.__wab_text,
            sty.h2
          )}
        >
          {" Coming Soon "}
        </h2>
        <ProfileOverviewSeeMoreButton
          data-plasmic-name={"join"}
          data-plasmic-override={overrides.join}
          className={classNames("__wab_instance", sty.join)}
          onClick={async event => {
            const $steps = {};

            $steps["goToLogin"] = true
              ? (() => {
                  const actionArgs = { destination: "/login" };
                  return (({ destination }) => {
                    if (
                      typeof destination === "string" &&
                      destination.startsWith("#")
                    ) {
                      document
                        .getElementById(destination.substr(1))
                        .scrollIntoView({ behavior: "smooth" });
                    } else {
                      __nextRouter?.push(destination);
                    }
                  })?.apply(null, [actionArgs]);
                })()
              : undefined;
            if (
              $steps["goToLogin"] != null &&
              typeof $steps["goToLogin"] === "object" &&
              typeof $steps["goToLogin"].then === "function"
            ) {
              $steps["goToLogin"] = await $steps["goToLogin"];
            }
          }}
          text={"Access Alpha"}
        />
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  pageBase: ["pageBase", "main", "freeBox", "h2", "join"],
  main: ["main", "freeBox", "h2", "join"],
  freeBox: ["freeBox"],
  h2: ["h2"],
  join: ["join"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  pageBase: "div";
  main: "section";
  freeBox: "div";
  h2: "h2";
  join: typeof ProfileOverviewSeeMoreButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicPageComingSoon__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicPageComingSoon__VariantsArgs;
    args?: PlasmicPageComingSoon__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicPageComingSoon__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicPageComingSoon__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPageComingSoon__ArgProps,
          internalVariantPropNames: PlasmicPageComingSoon__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicPageComingSoon__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "pageBase") {
    func.displayName = "PlasmicPageComingSoon";
  } else {
    func.displayName = `PlasmicPageComingSoon.${nodeName}`;
  }
  return func;
}

export const PlasmicPageComingSoon = Object.assign(
  // Top-level PlasmicPageComingSoon renders the root element
  makeNodeComponent("pageBase"),
  {
    // Helper components rendering sub-elements
    main: makeNodeComponent("main"),
    freeBox: makeNodeComponent("freeBox"),
    h2: makeNodeComponent("h2"),
    join: makeNodeComponent("join"),

    // Metadata about props expected for PlasmicPageComingSoon
    internalVariantProps: PlasmicPageComingSoon__VariantProps,
    internalArgProps: PlasmicPageComingSoon__ArgProps
  }
);

export default PlasmicPageComingSoon;
/* prettier-ignore-end */
