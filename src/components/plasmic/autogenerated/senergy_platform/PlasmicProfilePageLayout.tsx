/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: oCnNzqTJcCfl

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON><PERSON><PERSON>rg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileBanner from "../../ProfileBanner"; // plasmic-import: 5U1t8gggh1Oz/component
import ProfileCoreContentWrapper from "../../ProfileCoreContentWrapper"; // plasmic-import: 6qIgV9tA-TG7/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfilePageLayout.module.css"; // plasmic-import: oCnNzqTJcCfl/css

createPlasmicElementProxy;

export type PlasmicProfilePageLayout__VariantMembers = {};
export type PlasmicProfilePageLayout__VariantsArgs = {};
type VariantPropType = keyof PlasmicProfilePageLayout__VariantsArgs;
export const PlasmicProfilePageLayout__VariantProps =
  new Array<VariantPropType>();

export type PlasmicProfilePageLayout__ArgsType = {
  preferredNameInputValue?: string;
  pitchIntroInputValue?: string;
  pitchRoleInputValue?: string;
  pitchDescriptorInputValue?: string;
  langaugeInputValue?: string;
  startTimeInputValue?: string;
  locationInputValue?: string;
  endTimeInputValue?: string;
  emailAddress?: string;
};
type ArgPropType = keyof PlasmicProfilePageLayout__ArgsType;
export const PlasmicProfilePageLayout__ArgProps = new Array<ArgPropType>(
  "preferredNameInputValue",
  "pitchIntroInputValue",
  "pitchRoleInputValue",
  "pitchDescriptorInputValue",
  "langaugeInputValue",
  "startTimeInputValue",
  "locationInputValue",
  "endTimeInputValue",
  "emailAddress"
);

export type PlasmicProfilePageLayout__OverridesType = {
  root?: Flex__<"div">;
  profileBanner?: Flex__<typeof ProfileBanner>;
  content?: Flex__<typeof ProfileCoreContentWrapper>;
};

export interface DefaultProfilePageLayoutProps {
  preferredNameInputValue?: string;
  pitchIntroInputValue?: string;
  pitchRoleInputValue?: string;
  pitchDescriptorInputValue?: string;
  langaugeInputValue?: string;
  startTimeInputValue?: string;
  locationInputValue?: string;
  endTimeInputValue?: string;
  emailAddress?: string;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfilePageLayout__RenderFunc(props: {
  variants: PlasmicProfilePageLayout__VariantsArgs;
  args: PlasmicProfilePageLayout__ArgsType;
  overrides: PlasmicProfilePageLayout__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "profileBanner.preferredNameInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props["preferredNameInputValue"]
      },
      {
        path: "profileBanner.pitchIntroInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props["pitchIntroInputValue"]
      },
      {
        path: "profileBanner.pitchRoleInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props["pitchRoleInputValue"]
      },
      {
        path: "profileBanner.pitchDescriptorInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props["pitchDescriptorInputValue"]
      },
      {
        path: "profileBanner.langaugeInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props["langaugeInputValue"]
      },
      {
        path: "profileBanner.locationInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props["locationInputValue"]
      },
      {
        path: "profileBanner.startTimeInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props["startTimeInputValue"]
      },
      {
        path: "profileBanner.endTimeInputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props["endTimeInputValue"]
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <ProfileBanner
        data-plasmic-name={"profileBanner"}
        data-plasmic-override={overrides.profileBanner}
        className={classNames("__wab_instance", sty.profileBanner)}
        emailAddress={args.emailAddress}
        endTimeInputValue={generateStateValueProp($state, [
          "profileBanner",
          "endTimeInputValue"
        ])}
        langaugeInputValue={generateStateValueProp($state, [
          "profileBanner",
          "langaugeInputValue"
        ])}
        locationInputValue={generateStateValueProp($state, [
          "profileBanner",
          "locationInputValue"
        ])}
        onEndTimeInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "profileBanner",
            "endTimeInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onLangaugeInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "profileBanner",
            "langaugeInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onLocationInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "profileBanner",
            "locationInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPitchDescriptorInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "profileBanner",
            "pitchDescriptorInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPitchIntroInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "profileBanner",
            "pitchIntroInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPitchRoleInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "profileBanner",
            "pitchRoleInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPreferredFirstNameInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "profileBanner",
            "preferredNameInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onStartTimeInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "profileBanner",
            "startTimeInputValue"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        pitchDescriptorInputValue={generateStateValueProp($state, [
          "profileBanner",
          "pitchDescriptorInputValue"
        ])}
        pitchIntroInputValue={generateStateValueProp($state, [
          "profileBanner",
          "pitchIntroInputValue"
        ])}
        pitchRoleInputValue={generateStateValueProp($state, [
          "profileBanner",
          "pitchRoleInputValue"
        ])}
        preferredNameInputValue={generateStateValueProp($state, [
          "profileBanner",
          "preferredNameInputValue"
        ])}
        startTimeInputValue={generateStateValueProp($state, [
          "profileBanner",
          "startTimeInputValue"
        ])}
      />

      <ProfileCoreContentWrapper
        data-plasmic-name={"content"}
        data-plasmic-override={overrides.content}
        className={classNames("__wab_instance", sty.content)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "profileBanner", "content"],
  profileBanner: ["profileBanner"],
  content: ["content"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  profileBanner: typeof ProfileBanner;
  content: typeof ProfileCoreContentWrapper;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfilePageLayout__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfilePageLayout__VariantsArgs;
    args?: PlasmicProfilePageLayout__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfilePageLayout__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfilePageLayout__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfilePageLayout__ArgProps,
          internalVariantPropNames: PlasmicProfilePageLayout__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfilePageLayout__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicProfilePageLayout";
  } else {
    func.displayName = `PlasmicProfilePageLayout.${nodeName}`;
  }
  return func;
}

export const PlasmicProfilePageLayout = Object.assign(
  // Top-level PlasmicProfilePageLayout renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    profileBanner: makeNodeComponent("profileBanner"),
    content: makeNodeComponent("content"),

    // Metadata about props expected for PlasmicProfilePageLayout
    internalVariantProps: PlasmicProfilePageLayout__VariantProps,
    internalArgProps: PlasmicProfilePageLayout__ArgProps
  }
);

export default PlasmicProfilePageLayout;
/* prettier-ignore-end */
