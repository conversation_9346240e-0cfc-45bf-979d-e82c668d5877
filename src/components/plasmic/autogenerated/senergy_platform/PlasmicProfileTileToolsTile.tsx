/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 9IgGeZ9Jjxx9

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON><PERSON><PERSON>rg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileToolsTile.module.css"; // plasmic-import: 9IgGeZ9Jjxx9/css

createPlasmicElementProxy;

export type PlasmicProfileTileToolsTile__VariantMembers = {
  overviewGrid: "overviewGrid";
  editable: "editable";
};
export type PlasmicProfileTileToolsTile__VariantsArgs = {
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileTileToolsTile__VariantsArgs;
export const PlasmicProfileTileToolsTile__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileTileToolsTile__ArgsType = {};
type ArgPropType = keyof PlasmicProfileTileToolsTile__ArgsType;
export const PlasmicProfileTileToolsTile__ArgProps = new Array<ArgPropType>();

export type PlasmicProfileTileToolsTile__OverridesType = {
  toolsFormattingBox?: Flex__<"div">;
  toolsContent?: Flex__<"section">;
  text?: Flex__<"div">;
};

export interface DefaultProfileTileToolsTileProps {
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileToolsTile__RenderFunc(props: {
  variants: PlasmicProfileTileToolsTile__VariantsArgs;
  args: PlasmicProfileTileToolsTile__ArgsType;
  overrides: PlasmicProfileTileToolsTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"toolsFormattingBox"}
      data-plasmic-override={overrides.toolsFormattingBox}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.toolsFormattingBox,
        {
          [sty.toolsFormattingBoxoverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        }
      )}
    >
      <section
        data-plasmic-name={"toolsContent"}
        data-plasmic-override={overrides.toolsContent}
        className={classNames(projectcss.all, sty.toolsContent, {
          [sty.toolsContentoverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        })}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text,
            {
              [sty.textoverviewGrid]: hasVariant(
                $state,
                "overviewGrid",
                "overviewGrid"
              )
            }
          )}
        >
          {
            "This is a placeholder text.  The tools component is a WIP and will be added when it is finished. "
          }
        </div>
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  toolsFormattingBox: ["toolsFormattingBox", "toolsContent", "text"],
  toolsContent: ["toolsContent", "text"],
  text: ["text"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  toolsFormattingBox: "div";
  toolsContent: "section";
  text: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileToolsTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileToolsTile__VariantsArgs;
    args?: PlasmicProfileTileToolsTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTileToolsTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileToolsTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileToolsTile__ArgProps,
          internalVariantPropNames: PlasmicProfileTileToolsTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileToolsTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "toolsFormattingBox") {
    func.displayName = "PlasmicProfileTileToolsTile";
  } else {
    func.displayName = `PlasmicProfileTileToolsTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileToolsTile = Object.assign(
  // Top-level PlasmicProfileTileToolsTile renders the root element
  makeNodeComponent("toolsFormattingBox"),
  {
    // Helper components rendering sub-elements
    toolsContent: makeNodeComponent("toolsContent"),
    text: makeNodeComponent("text"),

    // Metadata about props expected for PlasmicProfileTileToolsTile
    internalVariantProps: PlasmicProfileTileToolsTile__VariantProps,
    internalArgProps: PlasmicProfileTileToolsTile__ArgProps
  }
);

export default PlasmicProfileTileToolsTile;
/* prettier-ignore-end */
