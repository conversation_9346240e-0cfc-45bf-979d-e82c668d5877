.licenseSpacingContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  flex-shrink: 0;
  height: auto;
  margin-bottom: 12px;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  margin-top: 4px;
  margin-left: 4px;
  row-gap: 0px;
  position: relative;
  min-width: 0;
  padding: 16px 16px 8px 0px;
}
.licenseSpacingContaineroverview {
  width: auto;
  justify-self: flex-start;
  display: inline-flex;
}
.licenseIcon {
  margin-top: 16px;
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: var(--token-ey2y7HI9U2zx);
  height: var(--token-ey2y7HI9U2zx);
  column-gap: 0px;
  position: relative;
  object-fit: cover;
  max-width: 100%;
  flex-shrink: 0;
}
.licenseIconeditable {
  margin-top: 0px;
}
.licenseIconoverview {
  display: none;
}
.informationStack {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.titleInput:global(.__wab_instance) {
  max-width: 100%;
}
.licensingBodyInput:global(.__wab_instance) {
  max-width: 100%;
  margin-bottom: 2px;
}
.licensingBodyInputeditable:global(.__wab_instance) {
  margin: 8px var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk);
}
.infoBar {
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: stretch;
  margin-bottom: 4px;
  column-gap: var(--token-rB3BKCgczoWa);
  row-gap: var(--token-ptnlAHOp9Vq0);
}
.infoBareditable {
  margin: 12px var(--token-sazGmnf7GWAk) 8px;
}
.infoBaroverview {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: stretch;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  row-gap: var(--token-ptnlAHOp9Vq0);
}
.issueLocationInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.issueLocationInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot5 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.registrationNumberInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.registrationNumberInputeditable:global(.__wab_instance) {
  display: flex;
}
.registrationNumberInputoverview:global(.__wab_instance) {
  display: none;
}
.iconSpot3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.instructorInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.instructorInputeditable:global(.__wab_instance) {
  display: flex;
}
.instructorInputoverview:global(.__wab_instance) {
  display: none;
}
.iconSpot4 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.issueDateInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.issueDateInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.expirationDateInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.expirationDateInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot6 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.description:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-top: 4px;
}
.descriptioneditable:global(.__wab_instance) {
  margin: var(--token-sazGmnf7GWAk);
}
.subDeleteButton:global(.__wab_instance) {
  max-width: 100%;
  position: absolute;
  top: 16px;
  right: 16px;
  flex-shrink: 0;
  display: none;
}
.subDeleteButtoneditable:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
