.profileBannerContianer {
  box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: center;
  align-items: stretch;
  background: var(--token-xo_r2w5pebq-);
  min-height: 275px;
  align-content: stretch;
  overflow: visible;
  transition-property: all;
  transition-duration: 0.2s;
  min-width: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.2s;
  padding: 0px 36px 0px 32px;
}
.profileBannerContianercollapsed {
  min-height: 100px;
}
@media (max-width: 1200px) {
  .profileBannerContianercollapsed {
    height: auto;
    flex-wrap: wrap;
    align-items: stretch;
  }
}
.mainInformationContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: space-between;
  width: 100%;
  height: auto;
  max-width: 100%;
  flex-wrap: nowrap;
  align-content: stretch;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.mainInformationContainercollapsed {
  justify-content: space-between;
  align-items: center;
  column-gap: var(--token-sazGmnf7GWAk);
  row-gap: 0px;
}
.leftSideBanner {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: auto;
  height: 100%;
  max-width: 100%;
  padding-right: 48px;
  margin-right: 0px;
  row-gap: var(--token-sazGmnf7GWAk);
  min-height: 0;
}
.leftSideBannercollapsed {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  row-gap: 0px;
}
.profileCoreProfileImage:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk) 0px;
}
.freeBoxcollapsed {
  margin-bottom: 0px;
  margin-left: 8px;
}
.preferredNameStack {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.preferredNameStackeditable {
  width: auto;
}
.preferredName:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: auto;
}
.preferredNameeditable:global(.__wab_instance) {
  width: 100%;
  min-width: 0;
}
.bottomAdditionalInformation {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: stretch;
  column-gap: var(--token-8HwyO4mnTWdf);
  row-gap: var(--token-ptnlAHOp9Vq0);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.bottomAdditionalInformationcollapsed {
  justify-content: flex-start;
  align-content: flex-start;
}
.location:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.iconSpot {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.displayWorkingHours:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.displayWorkingHourseditable:global(.__wab_instance) {
  display: none;
}
.iconSpot2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.startTimeInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.startTimeInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot4 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.endTimeInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.endTimeInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.languages {
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: auto;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  display: none;
  padding: var(--token-4Wrp9mDZCSCQ) 0px var(--token-4Wrp9mDZCSCQ)
    var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
.languagescollapsed {
  display: none;
}
.langauge:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.langaugeeditable:global(.__wab_instance) {
  display: flex;
}
.text___3MOdG {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  padding-top: 0px;
  white-space: pre;
  min-width: 0;
}
.texteditable___3MOdGwSi2Y {
  display: none;
}
.svg__vSkg3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  margin-right: 0px;
  flex-shrink: 0;
}
.rightSideBanner {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  width: auto;
  height: 100%;
  max-width: 100%;
  min-height: 0;
}
.rightSideBannercollapsed {
  align-items: flex-end;
  justify-content: center;
}
.elevatorPitch {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 70%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__h8GgH {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  margin-bottom: 1px;
  white-space: pre-wrap;
  font-size: var(--token-AMP1angFPBtf);
  text-align: right;
  min-width: 0;
}
@media (max-width: 1200px) {
  .text__h8GgH {
    font-size: var(--token-9KumB6TRpaad);
  }
}
@media (max-width: 860px) {
  .text__h8GgH {
    font-size: var(--token-2UEfYzPsoOY0);
  }
}
@media (max-width: 480px) {
  .text__h8GgH {
    font-size: var(--token-2UEfYzPsoOY0);
  }
}
.textcollapsed__h8GgHv8Swz {
  display: none;
}
.texteditable__h8GgHwSi2Y {
  display: none;
}
.adjectiveAndTitleStack {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: auto;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  display: none;
  padding: var(--token-sazGmnf7GWAk);
}
.adjectiveAndTitleStackcollapsed {
  display: flex;
}
.adjectiveAndTitleStackeditable {
  width: 100%;
  min-width: 0;
  display: flex;
}
.pitchIntro:global(.__wab_instance) {
  position: relative;
  width: auto;
}
.pitchIntrocollapsed:global(.__wab_instance) {
  display: none;
}
.pitchIntroeditable:global(.__wab_instance) {
  width: auto;
  display: flex;
}
.pitchRole:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: auto;
}
.pitchRoleeditable:global(.__wab_instance) {
  width: 100%;
  min-width: 0;
}
.pitchDescriptor:global(.__wab_instance) {
  position: relative;
  width: auto;
  display: none;
}
.pitchDescriptoreditable:global(.__wab_instance) {
  width: 100%;
  min-width: 0;
  display: flex;
}
.interactionButtons {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  padding: var(--token-4Wrp9mDZCSCQ) 8px 0px var(--token-4Wrp9mDZCSCQ);
  margin: 16px var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk);
}
@media (max-width: 860px) {
  .interactionButtons {
    padding-right: 0px;
  }
}
.interactionButtonscollapsed {
  margin-top: 0px;
  padding-top: 0px;
}
.teamUpButton:global(.__wab_instance) {
  max-width: 100%;
  display: none;
}
.teamUpButtoneditable:global(.__wab_instance) {
  display: none;
}
.svg___9MsbW {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.text__ma02V {
  font-family: var(--token-z1yrQVi72Nj1);
}
.svg__wLzb {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.messageMeButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-bottom: 0px;
}
.svg__qvxQr {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__a8RNi {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.containerForPortNavBar {
  display: flex;
  position: absolute;
  flex-direction: column;
  height: auto;
  align-items: flex-start;
  justify-content: flex-start;
  left: 0px;
  top: auto;
  z-index: 1;
  bottom: -30px;
}
.containerForPortNavBarcollapsed {
  bottom: -47px;
}
.profileCoreBannerNavigationBar__tAhde:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 0;
  left: auto;
  top: auto;
}
