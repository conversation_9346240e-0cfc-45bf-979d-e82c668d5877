/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: wf5-zG-i1Jzo

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsEducationBlock from "../../ProfileSectionsEducationBlock"; // plasmic-import: Z1o0_V1Y-Oht/component
import ProfileSectionsExperienceBlock from "../../ProfileSectionsExperienceBlock"; // plasmic-import: 9Evwwahu_Iga/component
import ProfileSectionsIntroductionBlock from "../../ProfileSectionsIntroductionBlock"; // plasmic-import: qfVGJMrc3gkW/component
import ProfileSectionsLanguageBlock from "../../ProfileSectionsLanguageBlock"; // plasmic-import: po-xZOF5f8ym/component
import ProfileSectionsPublicationsBlock from "../../ProfileSectionsPublicationsBlock"; // plasmic-import: zdegYaKxvR34/component
import ProfileSectionsSkillsBlock from "../../ProfileSectionsSkillsBlock"; // plasmic-import: 9Tp3HjHzEzcX/component
import ProfileSectionsToolsBlock from "../../ProfileSectionsToolsBlock"; // plasmic-import: lS3Tush4KDKz/component
import ProfileSectionsPatentsBlock from "../../ProfileSectionsPatentsBlock"; // plasmic-import: n9af0JVJyMFI/component
import ProfileSectionsTrademarksBlock from "../../ProfileSectionsTrademarksBlock"; // plasmic-import: VoKRKQ4gT9bR/component
import ProfileSectionsLicensesBlock from "../../ProfileSectionsLicensesBlock"; // plasmic-import: 5i3SEiGXS3YW/component
import ProfileSectionsCertificationsBlock from "../../ProfileSectionsCertificationsBlock"; // plasmic-import: iCin3UKpQ4Hq/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileOverviewOverviewBlocks.module.css"; // plasmic-import: wf5-zG-i1Jzo/css

createPlasmicElementProxy;

export type PlasmicProfileOverviewOverviewBlocks__VariantMembers = {
  sectionContent:
    | "introduction"
    | "education"
    | "experience"
    | "licenses"
    | "certifications"
    | "patents"
    | "trademarks"
    | "publications"
    | "skills"
    | "tools";
  comingSoon: "comingSoon";
};
export type PlasmicProfileOverviewOverviewBlocks__VariantsArgs = {
  sectionContent?: SingleChoiceArg<
    | "introduction"
    | "education"
    | "experience"
    | "licenses"
    | "certifications"
    | "patents"
    | "trademarks"
    | "publications"
    | "skills"
    | "tools"
  >;
  comingSoon?: SingleBooleanChoiceArg<"comingSoon">;
};
type VariantPropType = keyof PlasmicProfileOverviewOverviewBlocks__VariantsArgs;
export const PlasmicProfileOverviewOverviewBlocks__VariantProps =
  new Array<VariantPropType>("sectionContent", "comingSoon");

export type PlasmicProfileOverviewOverviewBlocks__ArgsType = {
  allEducation?: any;
  allExperience?: any;
  allLicenses?: any;
  allCertifications?: any;
  allPatents?: any;
  allTrademarks?: any;
  allPublications?: any;
  onAllEducationChange?: (val: any) => void;
  onAllExperienceChange?: (val: any) => void;
  onAllLicensesChange?: (val: any) => void;
  onAllCertificationsChange?: (val: any) => void;
  onAllPatentsChange?: (val: any) => void;
  onAllTrademarksChange?: (val: any) => void;
  onAllPublicationsChange?: (val: any) => void;
};
type ArgPropType = keyof PlasmicProfileOverviewOverviewBlocks__ArgsType;
export const PlasmicProfileOverviewOverviewBlocks__ArgProps =
  new Array<ArgPropType>(
    "allEducation",
    "allExperience",
    "allLicenses",
    "allCertifications",
    "allPatents",
    "allTrademarks",
    "allPublications",
    "onAllEducationChange",
    "onAllExperienceChange",
    "onAllLicensesChange",
    "onAllCertificationsChange",
    "onAllPatentsChange",
    "onAllTrademarksChange",
    "onAllPublicationsChange"
  );

export type PlasmicProfileOverviewOverviewBlocks__OverridesType = {
  formattingContainer?: Flex__<"div">;
  comingSoonContainer?: Flex__<"section">;
  text?: Flex__<"div">;
  pSectionsEducation?: Flex__<typeof ProfileSectionsEducationBlock>;
  pSectionsExperience?: Flex__<typeof ProfileSectionsExperienceBlock>;
  profileSectionsIntroductionBlock?: Flex__<
    typeof ProfileSectionsIntroductionBlock
  >;
  profileSectionsLanguageBlock?: Flex__<typeof ProfileSectionsLanguageBlock>;
  pSectionsPublications?: Flex__<typeof ProfileSectionsPublicationsBlock>;
  profileSectionsSkillsBlock?: Flex__<typeof ProfileSectionsSkillsBlock>;
  profileSectionsToolsBlock?: Flex__<typeof ProfileSectionsToolsBlock>;
  pSectionsPatents?: Flex__<typeof ProfileSectionsPatentsBlock>;
  pSectionsTrademarks?: Flex__<typeof ProfileSectionsTrademarksBlock>;
  pSectionsLicenses?: Flex__<typeof ProfileSectionsLicensesBlock>;
  pSectionsCertifications?: Flex__<typeof ProfileSectionsCertificationsBlock>;
};

export interface DefaultProfileOverviewOverviewBlocksProps {
  allEducation?: any;
  allExperience?: any;
  allLicenses?: any;
  allCertifications?: any;
  allPatents?: any;
  allTrademarks?: any;
  allPublications?: any;
  onAllEducationChange?: (val: any) => void;
  onAllExperienceChange?: (val: any) => void;
  onAllLicensesChange?: (val: any) => void;
  onAllCertificationsChange?: (val: any) => void;
  onAllPatentsChange?: (val: any) => void;
  onAllTrademarksChange?: (val: any) => void;
  onAllPublicationsChange?: (val: any) => void;
  sectionContent?: SingleChoiceArg<
    | "introduction"
    | "education"
    | "experience"
    | "licenses"
    | "certifications"
    | "patents"
    | "trademarks"
    | "publications"
    | "skills"
    | "tools"
  >;
  comingSoon?: SingleBooleanChoiceArg<"comingSoon">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileOverviewOverviewBlocks__RenderFunc(props: {
  variants: PlasmicProfileOverviewOverviewBlocks__VariantsArgs;
  args: PlasmicProfileOverviewOverviewBlocks__ArgsType;
  overrides: PlasmicProfileOverviewOverviewBlocks__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "sectionContent",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.sectionContent
      },
      {
        path: "pSectionsEducation.allEducation",
        type: "writable",
        variableType: "object",

        valueProp: "allEducation",
        onChangeProp: "onAllEducationChange"
      },
      {
        path: "pSectionsExperience.allExperience",
        type: "writable",
        variableType: "object",

        valueProp: "allExperience",
        onChangeProp: "onAllExperienceChange"
      },
      {
        path: "pSectionsPublications.allPublications",
        type: "writable",
        variableType: "object",

        valueProp: "allPublications",
        onChangeProp: "onAllPublicationsChange"
      },
      {
        path: "pSectionsPatents.allPatents",
        type: "writable",
        variableType: "object",

        valueProp: "allPatents",
        onChangeProp: "onAllPatentsChange"
      },
      {
        path: "pSectionsTrademarks.allTrademarks",
        type: "writable",
        variableType: "object",

        valueProp: "allTrademarks",
        onChangeProp: "onAllTrademarksChange"
      },
      {
        path: "pSectionsLicenses.allLicenses",
        type: "writable",
        variableType: "object",

        valueProp: "allLicenses",
        onChangeProp: "onAllLicensesChange"
      },
      {
        path: "pSectionsCertifications.allCertifications",
        type: "writable",
        variableType: "object",

        valueProp: "allCertifications",
        onChangeProp: "onAllCertificationsChange"
      },
      {
        path: "comingSoon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.comingSoon
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"formattingContainer"}
      data-plasmic-override={overrides.formattingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formattingContainer,
        {
          [sty.formattingContainercomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.formattingContainersectionContent_certifications]: hasVariant(
            $state,
            "sectionContent",
            "certifications"
          ),
          [sty.formattingContainersectionContent_education]: hasVariant(
            $state,
            "sectionContent",
            "education"
          ),
          [sty.formattingContainersectionContent_experience]: hasVariant(
            $state,
            "sectionContent",
            "experience"
          ),
          [sty.formattingContainersectionContent_introduction]: hasVariant(
            $state,
            "sectionContent",
            "introduction"
          ),
          [sty.formattingContainersectionContent_licenses]: hasVariant(
            $state,
            "sectionContent",
            "licenses"
          ),
          [sty.formattingContainersectionContent_patents]: hasVariant(
            $state,
            "sectionContent",
            "patents"
          ),
          [sty.formattingContainersectionContent_publications]: hasVariant(
            $state,
            "sectionContent",
            "publications"
          ),
          [sty.formattingContainersectionContent_skills]: hasVariant(
            $state,
            "sectionContent",
            "skills"
          ),
          [sty.formattingContainersectionContent_skills_comingSoon]:
            hasVariant($state, "sectionContent", "skills") &&
            hasVariant($state, "comingSoon", "comingSoon"),
          [sty.formattingContainersectionContent_tools]: hasVariant(
            $state,
            "sectionContent",
            "tools"
          ),
          [sty.formattingContainersectionContent_trademarks]: hasVariant(
            $state,
            "sectionContent",
            "trademarks"
          )
        }
      )}
      onMouseOver={async event => {
        const $steps = {};

        $steps["runCode"] = true
          ? (() => {
              const actionArgs = {
                customFunction: async () => {
                  return undefined;
                }
              };
              return (({ customFunction }) => {
                return customFunction();
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["runCode"] != null &&
          typeof $steps["runCode"] === "object" &&
          typeof $steps["runCode"].then === "function"
        ) {
          $steps["runCode"] = await $steps["runCode"];
        }
      }}
    >
      <section
        data-plasmic-name={"comingSoonContainer"}
        data-plasmic-override={overrides.comingSoonContainer}
        className={classNames(projectcss.all, sty.comingSoonContainer, {
          [sty.comingSoonContainercomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.comingSoonContainersectionContent_skills_comingSoon]:
            hasVariant($state, "comingSoon", "comingSoon") &&
            hasVariant($state, "sectionContent", "skills")
        })}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text,
            {
              [sty.textcomingSoon]: hasVariant(
                $state,
                "comingSoon",
                "comingSoon"
              ),
              [sty.textsectionContent_skills]: hasVariant(
                $state,
                "sectionContent",
                "skills"
              )
            }
          )}
        >
          {hasVariant($state, "comingSoon", "comingSoon")
            ? " Coming Soon \n This section is \n under construction "
            : " This section is \n under construction."}
        </div>
      </section>
      <ProfileSectionsEducationBlock
        data-plasmic-name={"pSectionsEducation"}
        data-plasmic-override={overrides.pSectionsEducation}
        allEducation={generateStateValueProp($state, [
          "pSectionsEducation",
          "allEducation"
        ])}
        className={classNames("__wab_instance", sty.pSectionsEducation, {
          [sty.pSectionsEducationsectionContent_education]: hasVariant(
            $state,
            "sectionContent",
            "education"
          ),
          [sty.pSectionsEducationsectionContent_experience]: hasVariant(
            $state,
            "sectionContent",
            "experience"
          ),
          [sty.pSectionsEducationsectionContent_introduction]: hasVariant(
            $state,
            "sectionContent",
            "introduction"
          )
        })}
        onAllEducationChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsEducation",
            "allEducation"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        overviewGrid={"overviewBlock"}
      />

      <ProfileSectionsExperienceBlock
        data-plasmic-name={"pSectionsExperience"}
        data-plasmic-override={overrides.pSectionsExperience}
        allExperience={generateStateValueProp($state, [
          "pSectionsExperience",
          "allExperience"
        ])}
        className={classNames("__wab_instance", sty.pSectionsExperience, {
          [sty.pSectionsExperiencesectionContent_education]: hasVariant(
            $state,
            "sectionContent",
            "education"
          ),
          [sty.pSectionsExperiencesectionContent_experience]: hasVariant(
            $state,
            "sectionContent",
            "experience"
          )
        })}
        onAllExperienceChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsExperience",
            "allExperience"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        overviewGrid={"overvierwGrid"}
      />

      <ProfileSectionsIntroductionBlock
        data-plasmic-name={"profileSectionsIntroductionBlock"}
        data-plasmic-override={overrides.profileSectionsIntroductionBlock}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsIntroductionBlock,
          {
            [sty.profileSectionsIntroductionBlocksectionContent_introduction]:
              hasVariant($state, "sectionContent", "introduction")
          }
        )}
        overviewGridBlock={true}
      />

      <ProfileSectionsLanguageBlock
        data-plasmic-name={"profileSectionsLanguageBlock"}
        data-plasmic-override={overrides.profileSectionsLanguageBlock}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsLanguageBlock
        )}
        overviewGrid={true}
      />

      <ProfileSectionsPublicationsBlock
        data-plasmic-name={"pSectionsPublications"}
        data-plasmic-override={overrides.pSectionsPublications}
        allPublications={generateStateValueProp($state, [
          "pSectionsPublications",
          "allPublications"
        ])}
        className={classNames("__wab_instance", sty.pSectionsPublications, {
          [sty.pSectionsPublicationssectionContent_publications]: hasVariant(
            $state,
            "sectionContent",
            "publications"
          )
        })}
        onAllPublicationsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsPublications",
            "allPublications"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        overviewGrid={"overviewGridBlock"}
      />

      <ProfileSectionsSkillsBlock
        data-plasmic-name={"profileSectionsSkillsBlock"}
        data-plasmic-override={overrides.profileSectionsSkillsBlock}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsSkillsBlock,
          {
            [sty.profileSectionsSkillsBlockcomingSoon]: hasVariant(
              $state,
              "comingSoon",
              "comingSoon"
            ),
            [sty.profileSectionsSkillsBlocksectionContent_skills]: hasVariant(
              $state,
              "sectionContent",
              "skills"
            )
          }
        )}
        overviewTile={true}
      />

      <ProfileSectionsToolsBlock
        data-plasmic-name={"profileSectionsToolsBlock"}
        data-plasmic-override={overrides.profileSectionsToolsBlock}
        className={classNames("__wab_instance", sty.profileSectionsToolsBlock, {
          [sty.profileSectionsToolsBlocksectionContent_tools]: hasVariant(
            $state,
            "sectionContent",
            "tools"
          )
        })}
        overviewBlock={true}
      />

      <ProfileSectionsPatentsBlock
        data-plasmic-name={"pSectionsPatents"}
        data-plasmic-override={overrides.pSectionsPatents}
        allPatents={generateStateValueProp($state, [
          "pSectionsPatents",
          "allPatents"
        ])}
        className={classNames("__wab_instance", sty.pSectionsPatents, {
          [sty.pSectionsPatentssectionContent_patents]: hasVariant(
            $state,
            "sectionContent",
            "patents"
          ),
          [sty.pSectionsPatentssectionContent_trademarks]: hasVariant(
            $state,
            "sectionContent",
            "trademarks"
          )
        })}
        onAllPatentsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsPatents",
            "allPatents"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        overviewGrid={"overviewGridBlock"}
      />

      <ProfileSectionsTrademarksBlock
        data-plasmic-name={"pSectionsTrademarks"}
        data-plasmic-override={overrides.pSectionsTrademarks}
        allTrademarks={generateStateValueProp($state, [
          "pSectionsTrademarks",
          "allTrademarks"
        ])}
        className={classNames("__wab_instance", sty.pSectionsTrademarks, {
          [sty.pSectionsTrademarkssectionContent_patents]: hasVariant(
            $state,
            "sectionContent",
            "patents"
          ),
          [sty.pSectionsTrademarkssectionContent_trademarks]: hasVariant(
            $state,
            "sectionContent",
            "trademarks"
          )
        })}
        onAllTrademarksChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsTrademarks",
            "allTrademarks"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        overviewGrid={"overviewGridBlock"}
      />

      <ProfileSectionsLicensesBlock
        data-plasmic-name={"pSectionsLicenses"}
        data-plasmic-override={overrides.pSectionsLicenses}
        allLicenses={generateStateValueProp($state, [
          "pSectionsLicenses",
          "allLicenses"
        ])}
        className={classNames("__wab_instance", sty.pSectionsLicenses, {
          [sty.pSectionsLicensessectionContent_licenses]: hasVariant(
            $state,
            "sectionContent",
            "licenses"
          )
        })}
        onAllLicensesChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsLicenses",
            "allLicenses"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        overviewGrid={true}
      />

      <ProfileSectionsCertificationsBlock
        data-plasmic-name={"pSectionsCertifications"}
        data-plasmic-override={overrides.pSectionsCertifications}
        allCertifications={generateStateValueProp($state, [
          "pSectionsCertifications",
          "allCertifications"
        ])}
        className={classNames("__wab_instance", sty.pSectionsCertifications, {
          [sty.pSectionsCertificationssectionContent_certifications]:
            hasVariant($state, "sectionContent", "certifications")
        })}
        onAllCertificationsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsCertifications",
            "allCertifications"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        overviewGrid={true}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formattingContainer: [
    "formattingContainer",
    "comingSoonContainer",
    "text",
    "pSectionsEducation",
    "pSectionsExperience",
    "profileSectionsIntroductionBlock",
    "profileSectionsLanguageBlock",
    "pSectionsPublications",
    "profileSectionsSkillsBlock",
    "profileSectionsToolsBlock",
    "pSectionsPatents",
    "pSectionsTrademarks",
    "pSectionsLicenses",
    "pSectionsCertifications"
  ],
  comingSoonContainer: ["comingSoonContainer", "text"],
  text: ["text"],
  pSectionsEducation: ["pSectionsEducation"],
  pSectionsExperience: ["pSectionsExperience"],
  profileSectionsIntroductionBlock: ["profileSectionsIntroductionBlock"],
  profileSectionsLanguageBlock: ["profileSectionsLanguageBlock"],
  pSectionsPublications: ["pSectionsPublications"],
  profileSectionsSkillsBlock: ["profileSectionsSkillsBlock"],
  profileSectionsToolsBlock: ["profileSectionsToolsBlock"],
  pSectionsPatents: ["pSectionsPatents"],
  pSectionsTrademarks: ["pSectionsTrademarks"],
  pSectionsLicenses: ["pSectionsLicenses"],
  pSectionsCertifications: ["pSectionsCertifications"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formattingContainer: "div";
  comingSoonContainer: "section";
  text: "div";
  pSectionsEducation: typeof ProfileSectionsEducationBlock;
  pSectionsExperience: typeof ProfileSectionsExperienceBlock;
  profileSectionsIntroductionBlock: typeof ProfileSectionsIntroductionBlock;
  profileSectionsLanguageBlock: typeof ProfileSectionsLanguageBlock;
  pSectionsPublications: typeof ProfileSectionsPublicationsBlock;
  profileSectionsSkillsBlock: typeof ProfileSectionsSkillsBlock;
  profileSectionsToolsBlock: typeof ProfileSectionsToolsBlock;
  pSectionsPatents: typeof ProfileSectionsPatentsBlock;
  pSectionsTrademarks: typeof ProfileSectionsTrademarksBlock;
  pSectionsLicenses: typeof ProfileSectionsLicensesBlock;
  pSectionsCertifications: typeof ProfileSectionsCertificationsBlock;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileOverviewOverviewBlocks__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileOverviewOverviewBlocks__VariantsArgs;
    args?: PlasmicProfileOverviewOverviewBlocks__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileOverviewOverviewBlocks__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileOverviewOverviewBlocks__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileOverviewOverviewBlocks__ArgProps,
          internalVariantPropNames:
            PlasmicProfileOverviewOverviewBlocks__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileOverviewOverviewBlocks__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formattingContainer") {
    func.displayName = "PlasmicProfileOverviewOverviewBlocks";
  } else {
    func.displayName = `PlasmicProfileOverviewOverviewBlocks.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileOverviewOverviewBlocks = Object.assign(
  // Top-level PlasmicProfileOverviewOverviewBlocks renders the root element
  makeNodeComponent("formattingContainer"),
  {
    // Helper components rendering sub-elements
    comingSoonContainer: makeNodeComponent("comingSoonContainer"),
    text: makeNodeComponent("text"),
    pSectionsEducation: makeNodeComponent("pSectionsEducation"),
    pSectionsExperience: makeNodeComponent("pSectionsExperience"),
    profileSectionsIntroductionBlock: makeNodeComponent(
      "profileSectionsIntroductionBlock"
    ),
    profileSectionsLanguageBlock: makeNodeComponent(
      "profileSectionsLanguageBlock"
    ),
    pSectionsPublications: makeNodeComponent("pSectionsPublications"),
    profileSectionsSkillsBlock: makeNodeComponent("profileSectionsSkillsBlock"),
    profileSectionsToolsBlock: makeNodeComponent("profileSectionsToolsBlock"),
    pSectionsPatents: makeNodeComponent("pSectionsPatents"),
    pSectionsTrademarks: makeNodeComponent("pSectionsTrademarks"),
    pSectionsLicenses: makeNodeComponent("pSectionsLicenses"),
    pSectionsCertifications: makeNodeComponent("pSectionsCertifications"),

    // Metadata about props expected for PlasmicProfileOverviewOverviewBlocks
    internalVariantProps: PlasmicProfileOverviewOverviewBlocks__VariantProps,
    internalArgProps: PlasmicProfileOverviewOverviewBlocks__ArgProps
  }
);

export default PlasmicProfileOverviewOverviewBlocks;
/* prettier-ignore-end */
