/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: GFYS_XLCIH0W

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsIntroductionBlock from "../../ProfileSectionsIntroductionBlock"; // plasmic-import: qfVGJMrc3gkW/component
import ProfileSectionsExperienceBlock from "../../ProfileSectionsExperienceBlock"; // plasmic-import: 9Evwwahu_Iga/component
import ProfileSectionsEducationBlock from "../../ProfileSectionsEducationBlock"; // plasmic-import: Z1o0_V1Y-Oht/component
import ProfileSectionsLanguageBlock from "../../ProfileSectionsLanguageBlock"; // plasmic-import: po-xZOF5f8ym/component
import ProfileSectionsLicensesAndCertificationsComboBlock from "../../ProfileSectionsLicensesAndCertificationsComboBlock"; // plasmic-import: f3EcElmDdrge/component
import ProfileSectionsPatentsAndTrademarksComboBlock from "../../ProfileSectionsPatentsAndTrademarksComboBlock"; // plasmic-import: NkPXGNNENGDZ/component
import ProfileSectionsPublicationsBlock from "../../ProfileSectionsPublicationsBlock"; // plasmic-import: zdegYaKxvR34/component
import ProfileSectionsSkillsBlock from "../../ProfileSectionsSkillsBlock"; // plasmic-import: 9Tp3HjHzEzcX/component
import ProfileSectionsToolsBlock from "../../ProfileSectionsToolsBlock"; // plasmic-import: lS3Tush4KDKz/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileAbout.module.css"; // plasmic-import: GFYS_XLCIH0W/css

createPlasmicElementProxy;

export type PlasmicProfileAbout__VariantMembers = {};
export type PlasmicProfileAbout__VariantsArgs = {};
type VariantPropType = keyof PlasmicProfileAbout__VariantsArgs;
export const PlasmicProfileAbout__VariantProps = new Array<VariantPropType>();

export type PlasmicProfileAbout__ArgsType = {};
type ArgPropType = keyof PlasmicProfileAbout__ArgsType;
export const PlasmicProfileAbout__ArgProps = new Array<ArgPropType>();

export type PlasmicProfileAbout__OverridesType = {
  root?: Flex__<"div">;
  profileSectionsIntroductionBlock?: Flex__<
    typeof ProfileSectionsIntroductionBlock
  >;
  pSectionsExperience?: Flex__<typeof ProfileSectionsExperienceBlock>;
  pSectionsEducation?: Flex__<typeof ProfileSectionsEducationBlock>;
  profileSectionsLanguageBlock?: Flex__<typeof ProfileSectionsLanguageBlock>;
  pSectionsLicensesAndCertifications?: Flex__<
    typeof ProfileSectionsLicensesAndCertificationsComboBlock
  >;
  pSectionsPatentsAndTrademarks?: Flex__<
    typeof ProfileSectionsPatentsAndTrademarksComboBlock
  >;
  pSectionsPublications?: Flex__<typeof ProfileSectionsPublicationsBlock>;
  profileSectionsSkillsBlock?: Flex__<typeof ProfileSectionsSkillsBlock>;
  profileSectionsToolsBlock?: Flex__<typeof ProfileSectionsToolsBlock>;
};

export interface DefaultProfileAboutProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileAbout__RenderFunc(props: {
  variants: PlasmicProfileAbout__VariantsArgs;
  args: PlasmicProfileAbout__ArgsType;
  overrides: PlasmicProfileAbout__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "pSectionsExperience.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pSectionsEducation.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pSectionsLicensesAndCertifications.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pSectionsLicensesAndCertifications.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pSectionsPatentsAndTrademarks.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pSectionsPatentsAndTrademarks.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "pSectionsPublications.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <ProfileSectionsIntroductionBlock
        data-plasmic-name={"profileSectionsIntroductionBlock"}
        data-plasmic-override={overrides.profileSectionsIntroductionBlock}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsIntroductionBlock
        )}
      />

      <ProfileSectionsExperienceBlock
        data-plasmic-name={"pSectionsExperience"}
        data-plasmic-override={overrides.pSectionsExperience}
        allExperience={generateStateValueProp($state, [
          "pSectionsExperience",
          "allExperience"
        ])}
        className={classNames("__wab_instance", sty.pSectionsExperience)}
        onAllExperienceChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsExperience",
            "allExperience"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <ProfileSectionsEducationBlock
        data-plasmic-name={"pSectionsEducation"}
        data-plasmic-override={overrides.pSectionsEducation}
        allEducation={generateStateValueProp($state, [
          "pSectionsEducation",
          "allEducation"
        ])}
        className={classNames("__wab_instance", sty.pSectionsEducation)}
        onAllEducationChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsEducation",
            "allEducation"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <ProfileSectionsLanguageBlock
        data-plasmic-name={"profileSectionsLanguageBlock"}
        data-plasmic-override={overrides.profileSectionsLanguageBlock}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsLanguageBlock
        )}
      />

      <ProfileSectionsLicensesAndCertificationsComboBlock
        data-plasmic-name={"pSectionsLicensesAndCertifications"}
        data-plasmic-override={overrides.pSectionsLicensesAndCertifications}
        allCertifications={generateStateValueProp($state, [
          "pSectionsLicensesAndCertifications",
          "allCertifications"
        ])}
        allLicenses={generateStateValueProp($state, [
          "pSectionsLicensesAndCertifications",
          "allLicenses"
        ])}
        className={classNames(
          "__wab_instance",
          sty.pSectionsLicensesAndCertifications
        )}
        onAllCertificationsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsLicensesAndCertifications",
            "allCertifications"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onAllLicensesChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsLicensesAndCertifications",
            "allLicenses"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <ProfileSectionsPatentsAndTrademarksComboBlock
        data-plasmic-name={"pSectionsPatentsAndTrademarks"}
        data-plasmic-override={overrides.pSectionsPatentsAndTrademarks}
        allPatents={generateStateValueProp($state, [
          "pSectionsPatentsAndTrademarks",
          "allPatents"
        ])}
        allTrademarks={generateStateValueProp($state, [
          "pSectionsPatentsAndTrademarks",
          "allTrademarks"
        ])}
        className={classNames(
          "__wab_instance",
          sty.pSectionsPatentsAndTrademarks
        )}
        onAllPatentsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsPatentsAndTrademarks",
            "allPatents"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onAllTrademarksChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsPatentsAndTrademarks",
            "allTrademarks"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <ProfileSectionsPublicationsBlock
        data-plasmic-name={"pSectionsPublications"}
        data-plasmic-override={overrides.pSectionsPublications}
        allPublications={generateStateValueProp($state, [
          "pSectionsPublications",
          "allPublications"
        ])}
        className={classNames("__wab_instance", sty.pSectionsPublications)}
        onAllPublicationsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "pSectionsPublications",
            "allPublications"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <ProfileSectionsSkillsBlock
        data-plasmic-name={"profileSectionsSkillsBlock"}
        data-plasmic-override={overrides.profileSectionsSkillsBlock}
        className={classNames("__wab_instance", sty.profileSectionsSkillsBlock)}
      />

      <ProfileSectionsToolsBlock
        data-plasmic-name={"profileSectionsToolsBlock"}
        data-plasmic-override={overrides.profileSectionsToolsBlock}
        className={classNames("__wab_instance", sty.profileSectionsToolsBlock)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "profileSectionsIntroductionBlock",
    "pSectionsExperience",
    "pSectionsEducation",
    "profileSectionsLanguageBlock",
    "pSectionsLicensesAndCertifications",
    "pSectionsPatentsAndTrademarks",
    "pSectionsPublications",
    "profileSectionsSkillsBlock",
    "profileSectionsToolsBlock"
  ],
  profileSectionsIntroductionBlock: ["profileSectionsIntroductionBlock"],
  pSectionsExperience: ["pSectionsExperience"],
  pSectionsEducation: ["pSectionsEducation"],
  profileSectionsLanguageBlock: ["profileSectionsLanguageBlock"],
  pSectionsLicensesAndCertifications: ["pSectionsLicensesAndCertifications"],
  pSectionsPatentsAndTrademarks: ["pSectionsPatentsAndTrademarks"],
  pSectionsPublications: ["pSectionsPublications"],
  profileSectionsSkillsBlock: ["profileSectionsSkillsBlock"],
  profileSectionsToolsBlock: ["profileSectionsToolsBlock"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  profileSectionsIntroductionBlock: typeof ProfileSectionsIntroductionBlock;
  pSectionsExperience: typeof ProfileSectionsExperienceBlock;
  pSectionsEducation: typeof ProfileSectionsEducationBlock;
  profileSectionsLanguageBlock: typeof ProfileSectionsLanguageBlock;
  pSectionsLicensesAndCertifications: typeof ProfileSectionsLicensesAndCertificationsComboBlock;
  pSectionsPatentsAndTrademarks: typeof ProfileSectionsPatentsAndTrademarksComboBlock;
  pSectionsPublications: typeof ProfileSectionsPublicationsBlock;
  profileSectionsSkillsBlock: typeof ProfileSectionsSkillsBlock;
  profileSectionsToolsBlock: typeof ProfileSectionsToolsBlock;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileAbout__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileAbout__VariantsArgs;
    args?: PlasmicProfileAbout__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileAbout__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileAbout__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileAbout__ArgProps,
          internalVariantPropNames: PlasmicProfileAbout__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileAbout__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicProfileAbout";
  } else {
    func.displayName = `PlasmicProfileAbout.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileAbout = Object.assign(
  // Top-level PlasmicProfileAbout renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    profileSectionsIntroductionBlock: makeNodeComponent(
      "profileSectionsIntroductionBlock"
    ),
    pSectionsExperience: makeNodeComponent("pSectionsExperience"),
    pSectionsEducation: makeNodeComponent("pSectionsEducation"),
    profileSectionsLanguageBlock: makeNodeComponent(
      "profileSectionsLanguageBlock"
    ),
    pSectionsLicensesAndCertifications: makeNodeComponent(
      "pSectionsLicensesAndCertifications"
    ),
    pSectionsPatentsAndTrademarks: makeNodeComponent(
      "pSectionsPatentsAndTrademarks"
    ),
    pSectionsPublications: makeNodeComponent("pSectionsPublications"),
    profileSectionsSkillsBlock: makeNodeComponent("profileSectionsSkillsBlock"),
    profileSectionsToolsBlock: makeNodeComponent("profileSectionsToolsBlock"),

    // Metadata about props expected for PlasmicProfileAbout
    internalVariantProps: PlasmicProfileAbout__VariantProps,
    internalArgProps: PlasmicProfileAbout__ArgProps
  }
);

export default PlasmicProfileAbout;
/* prettier-ignore-end */
