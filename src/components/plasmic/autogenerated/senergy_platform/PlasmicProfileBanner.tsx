/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 5U1t8gggh1Oz

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileCoreProfileImage from "../../ProfileCoreProfileImage"; // plasmic-import: FmLLnhtvs10f/component
import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentButton from "../../SubcomponentButton"; // plasmic-import: ezhuRZvm_fH9/component
import ProfileCoreBannerNavigationBar from "../../ProfileCoreBannerNavigationBar"; // plasmic-import: 5qBtmyZf-TyR/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileBanner.module.css"; // plasmic-import: 5U1t8gggh1Oz/css

import GlobeIcon from "./icons/PlasmicIcon__Globe"; // plasmic-import: 4U-HfnBQfBTq/icon
import CoffeeMugIcon from "./icons/PlasmicIcon__CoffeeMug"; // plasmic-import: kJJX9PdsKpcg/icon
import LanguagIcon from "./icons/PlasmicIcon__Languag"; // plasmic-import: 01ij0lND9yQw/icon

createPlasmicElementProxy;

export type PlasmicProfileBanner__VariantMembers = {
  collapsed: "collapsed";
  editable: "editable";
};
export type PlasmicProfileBanner__VariantsArgs = {
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileBanner__VariantsArgs;
export const PlasmicProfileBanner__VariantProps = new Array<VariantPropType>(
  "collapsed",
  "editable"
);

export type PlasmicProfileBanner__ArgsType = {
  portfolioNavBarSubComponent?: React.ReactNode;
  preferredNameInputValue?: string;
  pitchIntroInputValue?: string;
  pitchRoleInputValue?: string;
  pitchDescriptorInputValue?: string;
  langaugeInputValue?: string;
  locationInputValue?: string;
  startTimeInputValue?: string;
  endTimeInputValue?: string;
  onPreferredFirstNameInputValueChange?: (val: string) => void;
  onPitchIntroInputValueChange?: (val: string) => void;
  onPitchRoleInputValueChange?: (val: string) => void;
  onPitchDescriptorInputValueChange?: (val: string) => void;
  onLangaugeInputValueChange?: (val: string) => void;
  onLocationInputValueChange?: (val: string) => void;
  onStartTimeInputValueChange?: (val: string) => void;
  onEndTimeInputValueChange?: (val: string) => void;
  emailAddress?: string;
};
type ArgPropType = keyof PlasmicProfileBanner__ArgsType;
export const PlasmicProfileBanner__ArgProps = new Array<ArgPropType>(
  "portfolioNavBarSubComponent",
  "preferredNameInputValue",
  "pitchIntroInputValue",
  "pitchRoleInputValue",
  "pitchDescriptorInputValue",
  "langaugeInputValue",
  "locationInputValue",
  "startTimeInputValue",
  "endTimeInputValue",
  "onPreferredFirstNameInputValueChange",
  "onPitchIntroInputValueChange",
  "onPitchRoleInputValueChange",
  "onPitchDescriptorInputValueChange",
  "onLangaugeInputValueChange",
  "onLocationInputValueChange",
  "onStartTimeInputValueChange",
  "onEndTimeInputValueChange",
  "emailAddress"
);

export type PlasmicProfileBanner__OverridesType = {
  profileBannerContianer?: Flex__<"div">;
  mainInformationContainer?: Flex__<"div">;
  leftSideBanner?: Flex__<"div">;
  profileCoreProfileImage?: Flex__<typeof ProfileCoreProfileImage>;
  freeBox?: Flex__<"div">;
  preferredNameStack?: Flex__<"div">;
  preferredName?: Flex__<typeof SubcomponentTextInput>;
  bottomAdditionalInformation?: Flex__<"section">;
  location?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot?: Flex__<"svg">;
  displayWorkingHours?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot2?: Flex__<"svg">;
  startTimeInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot4?: Flex__<"svg">;
  endTimeInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot3?: Flex__<"svg">;
  languages?: Flex__<"div">;
  langauge?: Flex__<typeof SubcomponentTextInput>;
  rightSideBanner?: Flex__<"div">;
  elevatorPitch?: Flex__<"div">;
  adjectiveAndTitleStack?: Flex__<"div">;
  pitchIntro?: Flex__<typeof SubcomponentTextInput>;
  pitchRole?: Flex__<typeof SubcomponentTextInput>;
  pitchDescriptor?: Flex__<typeof SubcomponentTextInput>;
  interactionButtons?: Flex__<"div">;
  teamUpButton?: Flex__<typeof SubcomponentButton>;
  messageMeButton?: Flex__<typeof SubcomponentButton>;
  containerForPortNavBar?: Flex__<"div">;
};

export interface DefaultProfileBannerProps {
  portfolioNavBarSubComponent?: React.ReactNode;
  preferredNameInputValue?: string;
  pitchIntroInputValue?: string;
  pitchRoleInputValue?: string;
  pitchDescriptorInputValue?: string;
  langaugeInputValue?: string;
  locationInputValue?: string;
  startTimeInputValue?: string;
  endTimeInputValue?: string;
  onPreferredFirstNameInputValueChange?: (val: string) => void;
  onPitchIntroInputValueChange?: (val: string) => void;
  onPitchRoleInputValueChange?: (val: string) => void;
  onPitchDescriptorInputValueChange?: (val: string) => void;
  onLangaugeInputValueChange?: (val: string) => void;
  onLocationInputValueChange?: (val: string) => void;
  onStartTimeInputValueChange?: (val: string) => void;
  onEndTimeInputValueChange?: (val: string) => void;
  emailAddress?: string;
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileBanner__RenderFunc(props: {
  variants: PlasmicProfileBanner__VariantsArgs;
  args: PlasmicProfileBanner__ArgsType;
  overrides: PlasmicProfileBanner__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "collapsed",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.collapsed
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "preferredName.value",
        type: "writable",
        variableType: "text",

        valueProp: "preferredNameInputValue",
        onChangeProp: "onPreferredFirstNameInputValueChange"
      },
      {
        path: "pitchIntro.value",
        type: "writable",
        variableType: "text",

        valueProp: "pitchIntroInputValue",
        onChangeProp: "onPitchIntroInputValueChange"
      },
      {
        path: "pitchRole.value",
        type: "writable",
        variableType: "text",

        valueProp: "pitchRoleInputValue",
        onChangeProp: "onPitchRoleInputValueChange"
      },
      {
        path: "pitchDescriptor.value",
        type: "writable",
        variableType: "text",

        valueProp: "pitchDescriptorInputValue",
        onChangeProp: "onPitchDescriptorInputValueChange"
      },
      {
        path: "langauge.value",
        type: "writable",
        variableType: "text",

        valueProp: "langaugeInputValue",
        onChangeProp: "onLangaugeInputValueChange"
      },
      {
        path: "location.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "locationInputValue",
        onChangeProp: "onLocationInputValueChange"
      },
      {
        path: "displayWorkingHours.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "startTimeInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "startTimeInputValue",
        onChangeProp: "onStartTimeInputValueChange"
      },
      {
        path: "endTimeInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "endTimeInputValue",
        onChangeProp: "onEndTimeInputValueChange"
      },
      {
        path: "preferredName.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "langauge.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "pitchIntro.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "pitchRole.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "pitchDescriptor.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"profileBannerContianer"}
      data-plasmic-override={overrides.profileBannerContianer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.profileBannerContianer,
        {
          [sty.profileBannerContianercollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.profileBannerContianereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        }
      )}
    >
      <div
        data-plasmic-name={"mainInformationContainer"}
        data-plasmic-override={overrides.mainInformationContainer}
        className={classNames(projectcss.all, sty.mainInformationContainer, {
          [sty.mainInformationContainercollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          )
        })}
      >
        <div
          data-plasmic-name={"leftSideBanner"}
          data-plasmic-override={overrides.leftSideBanner}
          className={classNames(projectcss.all, sty.leftSideBanner, {
            [sty.leftSideBannercollapsed]: hasVariant(
              $state,
              "collapsed",
              "collapsed"
            ),
            [sty.leftSideBannereditable]: hasVariant(
              $state,
              "editable",
              "editable"
            )
          })}
        >
          <ProfileCoreProfileImage
            data-plasmic-name={"profileCoreProfileImage"}
            data-plasmic-override={overrides.profileCoreProfileImage}
            className={classNames(
              "__wab_instance",
              sty.profileCoreProfileImage,
              {
                [sty.profileCoreProfileImagecollapsed]: hasVariant(
                  $state,
                  "collapsed",
                  "collapsed"
                ),
                [sty.profileCoreProfileImageeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                )
              }
            )}
            mediaType={"noMedia"}
          />

          <div
            data-plasmic-name={"freeBox"}
            data-plasmic-override={overrides.freeBox}
            className={classNames(projectcss.all, sty.freeBox, {
              [sty.freeBoxcollapsed]: hasVariant(
                $state,
                "collapsed",
                "collapsed"
              )
            })}
          >
            <div
              data-plasmic-name={"preferredNameStack"}
              data-plasmic-override={overrides.preferredNameStack}
              className={classNames(projectcss.all, sty.preferredNameStack, {
                [sty.preferredNameStackcollapsed]: hasVariant(
                  $state,
                  "collapsed",
                  "collapsed"
                ),
                [sty.preferredNameStackeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                )
              })}
            >
              <SubcomponentTextInput
                data-plasmic-name={"preferredName"}
                data-plasmic-override={overrides.preferredName}
                className={classNames("__wab_instance", sty.preferredName, {
                  [sty.preferredNameeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                })}
                displayText={(() => {
                  try {
                    return $state.preferredName.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                editView={"heading3"}
                errorMessage={generateStateValueProp($state, [
                  "preferredName",
                  "errorMessage"
                ])}
                inputAutoComplete={"name"}
                inputHoverText={"Preferred Name"}
                inputName={"Preferred Name"}
                inputNameAsPlaceholder={true}
                inputType={"text"}
                inputValue={generateStateValueProp($state, [
                  "preferredName",
                  "value"
                ])}
                onErrorMessageChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "preferredName",
                    "errorMessage"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onInputValueChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "preferredName",
                    "value"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                viewOnly={
                  hasVariant($state, "editable", "editable") ? undefined : true
                }
              />
            </div>
            <section
              data-plasmic-name={"bottomAdditionalInformation"}
              data-plasmic-override={overrides.bottomAdditionalInformation}
              className={classNames(
                projectcss.all,
                sty.bottomAdditionalInformation,
                {
                  [sty.bottomAdditionalInformationcollapsed]: hasVariant(
                    $state,
                    "collapsed",
                    "collapsed"
                  ),
                  [sty.bottomAdditionalInformationeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                }
              )}
            >
              <SubcomponentIconWithText
                data-plasmic-name={"location"}
                data-plasmic-override={overrides.location}
                className={classNames("__wab_instance", sty.location, {
                  [sty.locationeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                })}
                displayText={(() => {
                  try {
                    return $state.location.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                editable={
                  hasVariant($state, "editable", "editable")
                    ? "editableText"
                    : undefined
                }
                iconSpot2={
                  <GlobeIcon
                    data-plasmic-name={"iconSpot"}
                    data-plasmic-override={overrides.iconSpot}
                    className={classNames(projectcss.all, sty.iconSpot, {
                      [sty.iconSpotcollapsed]: hasVariant(
                        $state,
                        "collapsed",
                        "collapsed"
                      )
                    })}
                    role={"img"}
                  />
                }
                inputHoverText={"Location"}
                inputPlaceholder={"Ex: Chicago, IL"}
                inputValue={generateStateValueProp($state, [
                  "location",
                  "inputValue"
                ])}
                onInputValueChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "location",
                    "inputValue"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
              />

              <SubcomponentIconWithText
                data-plasmic-name={"displayWorkingHours"}
                data-plasmic-override={overrides.displayWorkingHours}
                className={classNames(
                  "__wab_instance",
                  sty.displayWorkingHours,
                  {
                    [sty.displayWorkingHourseditable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  }
                )}
                displayText={(() => {
                  try {
                    return (() => {
                      const formatTime = time => {
                        const [hour, minute] = time.split(":");
                        const amOrPm = parseInt(hour) >= 12 ? "pm" : "am";
                        const formattedHour =
                          parseInt(hour) > 12 ? parseInt(hour) - 12 : hour;
                        return `${formattedHour}:${minute} ${amOrPm}`;
                      };

                      const startTime = formatTime(
                        $state.startTimeInput.inputValue
                      );
                      const endTime = formatTime(
                        $state.endTimeInput.inputValue
                      );

                      return `${startTime} - ${endTime}`;
                    })();
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                iconSpot2={
                  <CoffeeMugIcon
                    data-plasmic-name={"iconSpot2"}
                    data-plasmic-override={overrides.iconSpot2}
                    className={classNames(projectcss.all, sty.iconSpot2, {
                      [sty.iconSpot2collapsed]: hasVariant(
                        $state,
                        "collapsed",
                        "collapsed"
                      )
                    })}
                    role={"img"}
                  />
                }
                inputValue={generateStateValueProp($state, [
                  "displayWorkingHours",
                  "inputValue"
                ])}
                onInputValueChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "displayWorkingHours",
                    "inputValue"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
              />

              <SubcomponentIconWithText
                data-plasmic-name={"startTimeInput"}
                data-plasmic-override={overrides.startTimeInput}
                className={classNames("__wab_instance", sty.startTimeInput, {
                  [sty.startTimeInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                })}
                editable={"editableText"}
                iconSpot2={
                  <svg
                    data-plasmic-name={"iconSpot4"}
                    data-plasmic-override={overrides.iconSpot4}
                    className={classNames(projectcss.all, sty.iconSpot4)}
                    role={"img"}
                  />
                }
                inputPlaceholder={"Start Time"}
                inputType={"time"}
                inputValue={generateStateValueProp($state, [
                  "startTimeInput",
                  "inputValue"
                ])}
                onInputValueChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "startTimeInput",
                    "inputValue"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                withoutIcon={true}
              />

              <SubcomponentIconWithText
                data-plasmic-name={"endTimeInput"}
                data-plasmic-override={overrides.endTimeInput}
                className={classNames("__wab_instance", sty.endTimeInput, {
                  [sty.endTimeInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                })}
                editable={"editableText"}
                iconSpot2={
                  <svg
                    data-plasmic-name={"iconSpot3"}
                    data-plasmic-override={overrides.iconSpot3}
                    className={classNames(projectcss.all, sty.iconSpot3)}
                    role={"img"}
                  />
                }
                inputPlaceholder={"End Time"}
                inputType={"time"}
                inputValue={generateStateValueProp($state, [
                  "endTimeInput",
                  "inputValue"
                ])}
                onInputValueChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "endTimeInput",
                    "inputValue"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                withoutIcon={true}
              />

              <div
                data-plasmic-name={"languages"}
                data-plasmic-override={overrides.languages}
                className={classNames(projectcss.all, sty.languages, {
                  [sty.languagescollapsed]: hasVariant(
                    $state,
                    "collapsed",
                    "collapsed"
                  ),
                  [sty.languageseditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                })}
              >
                {(hasVariant($state, "editable", "editable") ? true : false) ? (
                  <SubcomponentTextInput
                    data-plasmic-name={"langauge"}
                    data-plasmic-override={overrides.langauge}
                    className={classNames("__wab_instance", sty.langauge, {
                      [sty.langaugeeditable]: hasVariant(
                        $state,
                        "editable",
                        "editable"
                      )
                    })}
                    errorMessage={generateStateValueProp($state, [
                      "langauge",
                      "errorMessage"
                    ])}
                    inputValue={generateStateValueProp($state, [
                      "langauge",
                      "value"
                    ])}
                    onErrorMessageChange={async (...eventArgs: any) => {
                      generateStateOnChangeProp($state, [
                        "langauge",
                        "errorMessage"
                      ]).apply(null, eventArgs);

                      if (
                        eventArgs.length > 1 &&
                        eventArgs[1] &&
                        eventArgs[1]._plasmic_state_init_
                      ) {
                        return;
                      }
                    }}
                    onInputValueChange={async (...eventArgs: any) => {
                      generateStateOnChangeProp($state, [
                        "langauge",
                        "value"
                      ]).apply(null, eventArgs);

                      if (
                        eventArgs.length > 1 &&
                        eventArgs[1] &&
                        eventArgs[1]._plasmic_state_init_
                      ) {
                        return;
                      }
                    }}
                  />
                ) : null}
                {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))([
                  2, 3, 4
                ]).map((__plasmic_item_0, __plasmic_idx_0) => {
                  const currentItem = __plasmic_item_0;
                  const currentIndex = __plasmic_idx_0;
                  return (
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text___3MOdG,
                        {
                          [sty.texteditable___3MOdGwSi2Y]: hasVariant(
                            $state,
                            "editable",
                            "editable"
                          )
                        }
                      )}
                      key={currentIndex}
                    >
                      {"Language"}
                    </div>
                  );
                })}
                <LanguagIcon
                  className={classNames(projectcss.all, sty.svg__vSkg3, {
                    [sty.svgeditable__vSkg3WSi2Y]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              </div>
            </section>
          </div>
        </div>
        <div
          data-plasmic-name={"rightSideBanner"}
          data-plasmic-override={overrides.rightSideBanner}
          className={classNames(projectcss.all, sty.rightSideBanner, {
            [sty.rightSideBannercollapsed]: hasVariant(
              $state,
              "collapsed",
              "collapsed"
            ),
            [sty.rightSideBannereditable]: hasVariant(
              $state,
              "editable",
              "editable"
            )
          })}
        >
          <div
            data-plasmic-name={"elevatorPitch"}
            data-plasmic-override={overrides.elevatorPitch}
            className={classNames(projectcss.all, sty.elevatorPitch, {
              [sty.elevatorPitchcollapsed]: hasVariant(
                $state,
                "collapsed",
                "collapsed"
              )
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__h8GgH,
                {
                  [sty.textcollapsed__h8GgHv8Swz]: hasVariant(
                    $state,
                    "collapsed",
                    "collapsed"
                  ),
                  [sty.texteditable__h8GgHwSi2Y]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                }
              )}
            >
              <div
                className={projectcss.__wab_expr_html_text}
                dangerouslySetInnerHTML={{
                  __html: (() => {
                    try {
                      return (() => {
                        const formattedPitchIntro =
                          $state.pitchIntro.value ?? "";
                        const formattedPitchRole = $state.pitchRole.value ?? "";
                        const formattedPitchDescriptor =
                          $state.pitchDescriptor.value ?? "";
                        return `${formattedPitchIntro} <b>${formattedPitchRole}</b> ${formattedPitchDescriptor}`;
                      })();
                    } catch (e) {
                      if (
                        e instanceof TypeError ||
                        e?.plasmicType === "PlasmicUndefinedDataError"
                      ) {
                        return "";
                      }
                      throw e;
                    }
                  })()
                }}
              />
            </div>
            <div
              data-plasmic-name={"adjectiveAndTitleStack"}
              data-plasmic-override={overrides.adjectiveAndTitleStack}
              className={classNames(
                projectcss.all,
                sty.adjectiveAndTitleStack,
                {
                  [sty.adjectiveAndTitleStackcollapsed]: hasVariant(
                    $state,
                    "collapsed",
                    "collapsed"
                  ),
                  [sty.adjectiveAndTitleStackeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                }
              )}
            >
              <SubcomponentTextInput
                data-plasmic-name={"pitchIntro"}
                data-plasmic-override={overrides.pitchIntro}
                className={classNames("__wab_instance", sty.pitchIntro, {
                  [sty.pitchIntrocollapsed]: hasVariant(
                    $state,
                    "collapsed",
                    "collapsed"
                  ),
                  [sty.pitchIntroeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                })}
                displayText={(() => {
                  try {
                    return $state.pitchIntro.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                editView={"heading2"}
                errorMessage={generateStateValueProp($state, [
                  "pitchIntro",
                  "errorMessage"
                ])}
                inputHoverText={"Pitch Intro"}
                inputName={"Pitch Intro"}
                inputNameAsPlaceholder={false}
                inputPlaceholder={"Ex: Creative"}
                inputValue={generateStateValueProp($state, [
                  "pitchIntro",
                  "value"
                ])}
                leftJustification={true}
                onErrorMessageChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "pitchIntro",
                    "errorMessage"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onInputValueChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "pitchIntro",
                    "value"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                viewOnly={
                  hasVariant($state, "editable", "editable") ? undefined : true
                }
              />

              <SubcomponentTextInput
                data-plasmic-name={"pitchRole"}
                data-plasmic-override={overrides.pitchRole}
                bold={true}
                className={classNames("__wab_instance", sty.pitchRole, {
                  [sty.pitchRolecollapsed]: hasVariant(
                    $state,
                    "collapsed",
                    "collapsed"
                  ),
                  [sty.pitchRoleeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  )
                })}
                displayText={(() => {
                  try {
                    return $state.pitchRole.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                editView={"heading2"}
                errorMessage={generateStateValueProp($state, [
                  "pitchRole",
                  "errorMessage"
                ])}
                inputHoverText={"Primary Role"}
                inputName={"Primary Role"}
                inputNameAsPlaceholder={false}
                inputPlaceholder={"Software Engineer"}
                inputValue={generateStateValueProp($state, [
                  "pitchRole",
                  "value"
                ])}
                leftJustification={true}
                onErrorMessageChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "pitchRole",
                    "errorMessage"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onInputValueChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "pitchRole",
                    "value"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                viewOnly={
                  hasVariant($state, "editable", "editable") ? undefined : true
                }
              />
            </div>
            <SubcomponentTextInput
              data-plasmic-name={"pitchDescriptor"}
              data-plasmic-override={overrides.pitchDescriptor}
              className={classNames("__wab_instance", sty.pitchDescriptor, {
                [sty.pitchDescriptorcollapsed]: hasVariant(
                  $state,
                  "collapsed",
                  "collapsed"
                ),
                [sty.pitchDescriptoreditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                )
              })}
              displayText={(() => {
                try {
                  return $state.pitchDescriptor.value;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editView={"heading2"}
              errorMessage={generateStateValueProp($state, [
                "pitchDescriptor",
                "errorMessage"
              ])}
              inputHoverText={"Pitch Descriptor"}
              inputName={"Pitch Descriptor"}
              inputNameAsPlaceholder={false}
              inputPlaceholder={"building delightful digital experiences"}
              inputValue={generateStateValueProp($state, [
                "pitchDescriptor",
                "value"
              ])}
              leftJustification={true}
              onErrorMessageChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "pitchDescriptor",
                  "errorMessage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "pitchDescriptor",
                  "value"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              viewOnly={
                hasVariant($state, "editable", "editable") ? undefined : true
              }
            />
          </div>
          <div
            data-plasmic-name={"interactionButtons"}
            data-plasmic-override={overrides.interactionButtons}
            className={classNames(projectcss.all, sty.interactionButtons, {
              [sty.interactionButtonscollapsed]: hasVariant(
                $state,
                "collapsed",
                "collapsed"
              ),
              [sty.interactionButtonseditable]: hasVariant(
                $state,
                "editable",
                "editable"
              )
            })}
          >
            <SubcomponentButton
              data-plasmic-name={"teamUpButton"}
              data-plasmic-override={overrides.teamUpButton}
              className={classNames("__wab_instance", sty.teamUpButton, {
                [sty.teamUpButtoneditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                )
              })}
              endIcon={
                <svg
                  className={classNames(projectcss.all, sty.svg__wLzb)}
                  role={"img"}
                />
              }
              size={"compact"}
              startIcon={
                <svg
                  className={classNames(projectcss.all, sty.svg___9MsbW)}
                  role={"img"}
                />
              }
              styling={["nittiWColor"]}
            >
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__ma02V
                )}
              >
                {"Team Up"}
              </div>
            </SubcomponentButton>
            <SubcomponentButton
              data-plasmic-name={"messageMeButton"}
              data-plasmic-override={overrides.messageMeButton}
              className={classNames("__wab_instance", sty.messageMeButton, {
                [sty.messageMeButtoncollapsed]: hasVariant(
                  $state,
                  "collapsed",
                  "collapsed"
                ),
                [sty.messageMeButtoneditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                )
              })}
              disabledShakeText={"Coming Soon"}
              endIcon={
                <svg
                  className={classNames(projectcss.all, sty.svg__a8RNi)}
                  role={"img"}
                />
              }
              link={(() => {
                try {
                  return "mailto:" + $props.emailAddress;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return "";
                  }
                  throw e;
                }
              })()}
              size={"compact"}
              startIcon={
                <svg
                  className={classNames(projectcss.all, sty.svg__qvxQr)}
                  role={"img"}
                />
              }
              styling={["nittiWColor"]}
            >
              <React.Fragment>
                {(() => {
                  try {
                    return $state.preferredName.value
                      ? "Email " + $state.preferredName.value.split(" ")[0]
                      : "Message Me";
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return "";
                    }
                    throw e;
                  }
                })()}
              </React.Fragment>
            </SubcomponentButton>
          </div>
        </div>
      </div>
      <div
        data-plasmic-name={"containerForPortNavBar"}
        data-plasmic-override={overrides.containerForPortNavBar}
        className={classNames(projectcss.all, sty.containerForPortNavBar, {
          [sty.containerForPortNavBarcollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          )
        })}
      >
        {renderPlasmicSlot({
          defaultContents: (
            <ProfileCoreBannerNavigationBar
              className={classNames(
                "__wab_instance",
                sty.profileCoreBannerNavigationBar__tAhde
              )}
            />
          ),

          value: args.portfolioNavBarSubComponent
        })}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  profileBannerContianer: [
    "profileBannerContianer",
    "mainInformationContainer",
    "leftSideBanner",
    "profileCoreProfileImage",
    "freeBox",
    "preferredNameStack",
    "preferredName",
    "bottomAdditionalInformation",
    "location",
    "iconSpot",
    "displayWorkingHours",
    "iconSpot2",
    "startTimeInput",
    "iconSpot4",
    "endTimeInput",
    "iconSpot3",
    "languages",
    "langauge",
    "rightSideBanner",
    "elevatorPitch",
    "adjectiveAndTitleStack",
    "pitchIntro",
    "pitchRole",
    "pitchDescriptor",
    "interactionButtons",
    "teamUpButton",
    "messageMeButton",
    "containerForPortNavBar"
  ],
  mainInformationContainer: [
    "mainInformationContainer",
    "leftSideBanner",
    "profileCoreProfileImage",
    "freeBox",
    "preferredNameStack",
    "preferredName",
    "bottomAdditionalInformation",
    "location",
    "iconSpot",
    "displayWorkingHours",
    "iconSpot2",
    "startTimeInput",
    "iconSpot4",
    "endTimeInput",
    "iconSpot3",
    "languages",
    "langauge",
    "rightSideBanner",
    "elevatorPitch",
    "adjectiveAndTitleStack",
    "pitchIntro",
    "pitchRole",
    "pitchDescriptor",
    "interactionButtons",
    "teamUpButton",
    "messageMeButton"
  ],
  leftSideBanner: [
    "leftSideBanner",
    "profileCoreProfileImage",
    "freeBox",
    "preferredNameStack",
    "preferredName",
    "bottomAdditionalInformation",
    "location",
    "iconSpot",
    "displayWorkingHours",
    "iconSpot2",
    "startTimeInput",
    "iconSpot4",
    "endTimeInput",
    "iconSpot3",
    "languages",
    "langauge"
  ],
  profileCoreProfileImage: ["profileCoreProfileImage"],
  freeBox: [
    "freeBox",
    "preferredNameStack",
    "preferredName",
    "bottomAdditionalInformation",
    "location",
    "iconSpot",
    "displayWorkingHours",
    "iconSpot2",
    "startTimeInput",
    "iconSpot4",
    "endTimeInput",
    "iconSpot3",
    "languages",
    "langauge"
  ],
  preferredNameStack: ["preferredNameStack", "preferredName"],
  preferredName: ["preferredName"],
  bottomAdditionalInformation: [
    "bottomAdditionalInformation",
    "location",
    "iconSpot",
    "displayWorkingHours",
    "iconSpot2",
    "startTimeInput",
    "iconSpot4",
    "endTimeInput",
    "iconSpot3",
    "languages",
    "langauge"
  ],
  location: ["location", "iconSpot"],
  iconSpot: ["iconSpot"],
  displayWorkingHours: ["displayWorkingHours", "iconSpot2"],
  iconSpot2: ["iconSpot2"],
  startTimeInput: ["startTimeInput", "iconSpot4"],
  iconSpot4: ["iconSpot4"],
  endTimeInput: ["endTimeInput", "iconSpot3"],
  iconSpot3: ["iconSpot3"],
  languages: ["languages", "langauge"],
  langauge: ["langauge"],
  rightSideBanner: [
    "rightSideBanner",
    "elevatorPitch",
    "adjectiveAndTitleStack",
    "pitchIntro",
    "pitchRole",
    "pitchDescriptor",
    "interactionButtons",
    "teamUpButton",
    "messageMeButton"
  ],
  elevatorPitch: [
    "elevatorPitch",
    "adjectiveAndTitleStack",
    "pitchIntro",
    "pitchRole",
    "pitchDescriptor"
  ],
  adjectiveAndTitleStack: ["adjectiveAndTitleStack", "pitchIntro", "pitchRole"],
  pitchIntro: ["pitchIntro"],
  pitchRole: ["pitchRole"],
  pitchDescriptor: ["pitchDescriptor"],
  interactionButtons: ["interactionButtons", "teamUpButton", "messageMeButton"],
  teamUpButton: ["teamUpButton"],
  messageMeButton: ["messageMeButton"],
  containerForPortNavBar: ["containerForPortNavBar"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  profileBannerContianer: "div";
  mainInformationContainer: "div";
  leftSideBanner: "div";
  profileCoreProfileImage: typeof ProfileCoreProfileImage;
  freeBox: "div";
  preferredNameStack: "div";
  preferredName: typeof SubcomponentTextInput;
  bottomAdditionalInformation: "section";
  location: typeof SubcomponentIconWithText;
  iconSpot: "svg";
  displayWorkingHours: typeof SubcomponentIconWithText;
  iconSpot2: "svg";
  startTimeInput: typeof SubcomponentIconWithText;
  iconSpot4: "svg";
  endTimeInput: typeof SubcomponentIconWithText;
  iconSpot3: "svg";
  languages: "div";
  langauge: typeof SubcomponentTextInput;
  rightSideBanner: "div";
  elevatorPitch: "div";
  adjectiveAndTitleStack: "div";
  pitchIntro: typeof SubcomponentTextInput;
  pitchRole: typeof SubcomponentTextInput;
  pitchDescriptor: typeof SubcomponentTextInput;
  interactionButtons: "div";
  teamUpButton: typeof SubcomponentButton;
  messageMeButton: typeof SubcomponentButton;
  containerForPortNavBar: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileBanner__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileBanner__VariantsArgs;
    args?: PlasmicProfileBanner__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileBanner__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileBanner__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileBanner__ArgProps,
          internalVariantPropNames: PlasmicProfileBanner__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileBanner__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "profileBannerContianer") {
    func.displayName = "PlasmicProfileBanner";
  } else {
    func.displayName = `PlasmicProfileBanner.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileBanner = Object.assign(
  // Top-level PlasmicProfileBanner renders the root element
  makeNodeComponent("profileBannerContianer"),
  {
    // Helper components rendering sub-elements
    mainInformationContainer: makeNodeComponent("mainInformationContainer"),
    leftSideBanner: makeNodeComponent("leftSideBanner"),
    profileCoreProfileImage: makeNodeComponent("profileCoreProfileImage"),
    freeBox: makeNodeComponent("freeBox"),
    preferredNameStack: makeNodeComponent("preferredNameStack"),
    preferredName: makeNodeComponent("preferredName"),
    bottomAdditionalInformation: makeNodeComponent(
      "bottomAdditionalInformation"
    ),
    location: makeNodeComponent("location"),
    iconSpot: makeNodeComponent("iconSpot"),
    displayWorkingHours: makeNodeComponent("displayWorkingHours"),
    iconSpot2: makeNodeComponent("iconSpot2"),
    startTimeInput: makeNodeComponent("startTimeInput"),
    iconSpot4: makeNodeComponent("iconSpot4"),
    endTimeInput: makeNodeComponent("endTimeInput"),
    iconSpot3: makeNodeComponent("iconSpot3"),
    languages: makeNodeComponent("languages"),
    langauge: makeNodeComponent("langauge"),
    rightSideBanner: makeNodeComponent("rightSideBanner"),
    elevatorPitch: makeNodeComponent("elevatorPitch"),
    adjectiveAndTitleStack: makeNodeComponent("adjectiveAndTitleStack"),
    pitchIntro: makeNodeComponent("pitchIntro"),
    pitchRole: makeNodeComponent("pitchRole"),
    pitchDescriptor: makeNodeComponent("pitchDescriptor"),
    interactionButtons: makeNodeComponent("interactionButtons"),
    teamUpButton: makeNodeComponent("teamUpButton"),
    messageMeButton: makeNodeComponent("messageMeButton"),
    containerForPortNavBar: makeNodeComponent("containerForPortNavBar"),

    // Metadata about props expected for PlasmicProfileBanner
    internalVariantProps: PlasmicProfileBanner__VariantProps,
    internalArgProps: PlasmicProfileBanner__ArgProps
  }
);

export default PlasmicProfileBanner;
/* prettier-ignore-end */
