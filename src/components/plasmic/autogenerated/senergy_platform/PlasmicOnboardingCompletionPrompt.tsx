/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 9xrOS05wbunZ

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentSelectorButtonsWSlot from "../../SubcomponentSelectorButtonsWSlot"; // plasmic-import: FPZd4ZDDgKTk/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicOnboardingCompletionPrompt.module.css"; // plasmic-import: 9xrOS05wbunZ/css

import UserIcon from "./icons/PlasmicIcon__User"; // plasmic-import: hcR42vt5qkrz/icon
import PhotoCheckIcon from "./icons/PlasmicIcon__PhotoCheck"; // plasmic-import: 0HEABec35TzS/icon
import InfoGraphicIcon from "./icons/PlasmicIcon__InfoGraphic"; // plasmic-import: gCJGgC3_7lN-/icon
import GradCapIcon from "./icons/PlasmicIcon__GradCap"; // plasmic-import: i9bbaFOlpQVC/icon
import BuildingIcon from "./icons/PlasmicIcon__Building"; // plasmic-import: RuQrxOj-BhnB/icon
import ToolsIcon from "./icons/PlasmicIcon__Tools"; // plasmic-import: AHRVoCS9vdiJ/icon
import SkillsIcon from "./icons/PlasmicIcon__Skills"; // plasmic-import: 3yiBdPB4DXzP/icon
import LanguagIcon from "./icons/PlasmicIcon__Languag"; // plasmic-import: 01ij0lND9yQw/icon
import PatentedIcon from "./icons/PlasmicIcon__Patented"; // plasmic-import: 0zRPSen3c0Ql/icon
import PublicationBookIcon from "./icons/PlasmicIcon__PublicationBook"; // plasmic-import: 1vlHySSOKWkP/icon
import CertificateIcon from "./icons/PlasmicIcon__Certificate"; // plasmic-import: oMeblj4iBOSs/icon
import LicenseIcon from "./icons/PlasmicIcon__License"; // plasmic-import: j-ct0zISt5hl/icon
import TrademarkIcon from "./icons/PlasmicIcon__Trademark"; // plasmic-import: TJR3b86TU-Do/icon
import PlusIcon from "./icons/PlasmicIcon__Plus"; // plasmic-import: 9aghGjj-Yn0f/icon
import MinusIcon from "./icons/PlasmicIcon__Minus"; // plasmic-import: -YxnRXLJIlYW/icon

createPlasmicElementProxy;

export type PlasmicOnboardingCompletionPrompt__VariantMembers = {
  sectionOnboarding: "optionSelected" | "unnamedVariant2";
};
export type PlasmicOnboardingCompletionPrompt__VariantsArgs = {
  sectionOnboarding?: SingleChoiceArg<"optionSelected" | "unnamedVariant2">;
};
type VariantPropType = keyof PlasmicOnboardingCompletionPrompt__VariantsArgs;
export const PlasmicOnboardingCompletionPrompt__VariantProps =
  new Array<VariantPropType>("sectionOnboarding");

export type PlasmicOnboardingCompletionPrompt__ArgsType = {};
type ArgPropType = keyof PlasmicOnboardingCompletionPrompt__ArgsType;
export const PlasmicOnboardingCompletionPrompt__ArgProps =
  new Array<ArgPropType>();

export type PlasmicOnboardingCompletionPrompt__OverridesType = {
  formattingContainer?: Flex__<"div">;
  mainSectionButtons?: Flex__<"div">;
  profileBanner?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  introduction?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  caseStudy?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  scrollingOptions?: Flex__<"div">;
  educationButton2?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  experienceButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  toolsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  skillsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  languagesButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  patentsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  publicationsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  certificationsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  licensesButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  trademarksButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  seeMore?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  seeLessButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  completedSections?: Flex__<"div">;
  sectionsWithContent?: Flex__<"div">;
  introductionButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  educationButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  experienceButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  skillsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  toolsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  languagesButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  publicationsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  licensesButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  certificationsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  patentsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  trademarksButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
};

export interface DefaultOnboardingCompletionPromptProps {
  sectionOnboarding?: SingleChoiceArg<"optionSelected" | "unnamedVariant2">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicOnboardingCompletionPrompt__RenderFunc(props: {
  variants: PlasmicOnboardingCompletionPrompt__VariantsArgs;
  args: PlasmicOnboardingCompletionPrompt__ArgsType;
  overrides: PlasmicOnboardingCompletionPrompt__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "sectionOnboarding",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.sectionOnboarding
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"formattingContainer"}
      data-plasmic-override={overrides.formattingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formattingContainer
      )}
    >
      <div
        className={classNames(
          projectcss.all,
          projectcss.__wab_text,
          sty.text__wDnWi
        )}
      >
        {"You've got a great start, let's round out your profile."}
      </div>
      <div
        data-plasmic-name={"mainSectionButtons"}
        data-plasmic-override={overrides.mainSectionButtons}
        className={classNames(projectcss.all, sty.mainSectionButtons)}
      >
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"profileBanner"}
          data-plasmic-override={overrides.profileBanner}
          className={classNames("__wab_instance", sty.profileBanner)}
          color={"normal"}
          size={"medium"}
        >
          <UserIcon
            className={classNames(projectcss.all, sty.svg__uwlry)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__p8YGn
            )}
          >
            {"Profile Banner"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"introduction"}
          data-plasmic-override={overrides.introduction}
          className={classNames("__wab_instance", sty.introduction, {
            [sty.introductionsectionOnboarding_optionSelected]: hasVariant(
              $state,
              "sectionOnboarding",
              "optionSelected"
            )
          })}
          completionStatus={"incomplete"}
          size={"medium"}
        >
          <PhotoCheckIcon
            className={classNames(projectcss.all, sty.svg__gwW2E)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__bYdD
            )}
          >
            {"Introduction"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"caseStudy"}
          data-plasmic-override={overrides.caseStudy}
          className={classNames("__wab_instance", sty.caseStudy)}
          size={"medium"}
        >
          <InfoGraphicIcon
            className={classNames(projectcss.all, sty.svg__suXy)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__syE9N
            )}
          >
            {"Build a Case Study"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <div
          data-plasmic-name={"scrollingOptions"}
          data-plasmic-override={overrides.scrollingOptions}
          className={classNames(projectcss.all, sty.scrollingOptions)}
        >
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"educationButton2"}
            data-plasmic-override={overrides.educationButton2}
            className={classNames("__wab_instance", sty.educationButton2)}
            completionStatus={"complete"}
            size={"medium"}
          >
            <GradCapIcon
              className={classNames(projectcss.all, sty.svg__ksPs, {
                [sty.svgsectionOnboarding_optionSelected__ksPs55OSb]:
                  hasVariant($state, "sectionOnboarding", "optionSelected")
              })}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__nceaG
              )}
            >
              {"Education"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"experienceButton"}
            data-plasmic-override={overrides.experienceButton}
            className={classNames("__wab_instance", sty.experienceButton, {
              [sty.experienceButtonsectionOnboarding_optionSelected]:
                hasVariant($state, "sectionOnboarding", "optionSelected")
            })}
            size={"medium"}
          >
            <BuildingIcon
              className={classNames(projectcss.all, sty.svg__uWgOp, {
                [sty.svgsectionOnboarding_optionSelected__uWgOp55OSb]:
                  hasVariant($state, "sectionOnboarding", "optionSelected")
              })}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__lTjbi
              )}
            >
              {"Experiences"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"toolsButton"}
            data-plasmic-override={overrides.toolsButton}
            className={classNames("__wab_instance", sty.toolsButton, {
              [sty.toolsButtonsectionOnboarding_optionSelected]: hasVariant(
                $state,
                "sectionOnboarding",
                "optionSelected"
              )
            })}
            size={"medium"}
          >
            <ToolsIcon
              className={classNames(projectcss.all, sty.svg__ixs3E)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__q6Er7
              )}
            >
              {"Tools"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"skillsButton"}
            data-plasmic-override={overrides.skillsButton}
            className={classNames("__wab_instance", sty.skillsButton)}
            size={"medium"}
          >
            <SkillsIcon
              className={classNames(projectcss.all, sty.svg__g0Bgy)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__rRmR2
              )}
            >
              {"Skills"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"languagesButton"}
            data-plasmic-override={overrides.languagesButton}
            className={classNames("__wab_instance", sty.languagesButton)}
            size={"medium"}
          >
            <LanguagIcon
              className={classNames(projectcss.all, sty.svg__fN0Kd)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__hskqN
              )}
            >
              {"Languages"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"patentsButton"}
            data-plasmic-override={overrides.patentsButton}
            className={classNames("__wab_instance", sty.patentsButton)}
            size={"medium"}
          >
            <PatentedIcon
              className={classNames(projectcss.all, sty.svg__p7Ibt)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___7PMtU
              )}
            >
              {"Patents"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"publicationsButton"}
            data-plasmic-override={overrides.publicationsButton}
            className={classNames("__wab_instance", sty.publicationsButton)}
            size={"medium"}
          >
            <PublicationBookIcon
              className={classNames(projectcss.all, sty.svg___0FRm5)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__iw92K
              )}
            >
              {"Publications"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"certificationsButton"}
            data-plasmic-override={overrides.certificationsButton}
            className={classNames("__wab_instance", sty.certificationsButton)}
            size={"medium"}
          >
            <CertificateIcon
              className={classNames(projectcss.all, sty.svg__vHrl)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__pXmTw
              )}
            >
              {"Certificates"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"licensesButton"}
            data-plasmic-override={overrides.licensesButton}
            className={classNames("__wab_instance", sty.licensesButton)}
            size={"medium"}
          >
            <LicenseIcon
              className={classNames(projectcss.all, sty.svg__nUxZb)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__nst7H
              )}
            >
              {"Licenses"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
        </div>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"trademarksButton"}
          data-plasmic-override={overrides.trademarksButton}
          className={classNames("__wab_instance", sty.trademarksButton)}
          size={"medium"}
        >
          <TrademarkIcon
            className={classNames(projectcss.all, sty.svg__hn490)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__nVZzM
            )}
          >
            {"Trademarks"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"seeMore"}
          data-plasmic-override={overrides.seeMore}
          className={classNames("__wab_instance", sty.seeMore)}
          size={"medium"}
        >
          <PlusIcon
            className={classNames(projectcss.all, sty.svg__k3Amc)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__vXze
            )}
          >
            {"More Sections"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"seeLessButton"}
          data-plasmic-override={overrides.seeLessButton}
          className={classNames("__wab_instance", sty.seeLessButton)}
          size={"medium"}
        >
          <MinusIcon
            className={classNames(projectcss.all, sty.svg__ithXd)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__msbTx
            )}
          >
            {"Collapse Sections"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
      </div>
      <div
        data-plasmic-name={"completedSections"}
        data-plasmic-override={overrides.completedSections}
        className={classNames(projectcss.all, sty.completedSections)}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__xLbR5
          )}
        >
          {"Sections with Entries"}
        </div>
        <div
          data-plasmic-name={"sectionsWithContent"}
          data-plasmic-override={overrides.sectionsWithContent}
          className={classNames(projectcss.all, sty.sectionsWithContent)}
        >
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"introductionButtonEntry"}
            data-plasmic-override={overrides.introductionButtonEntry}
            className={classNames(
              "__wab_instance",
              sty.introductionButtonEntry
            )}
            color={"green"}
            size={"small"}
          >
            <UserIcon
              className={classNames(projectcss.all, sty.svg__rkK5Y)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__uoy1A
              )}
            >
              {"Introduction"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"educationButtonEntry"}
            data-plasmic-override={overrides.educationButtonEntry}
            className={classNames("__wab_instance", sty.educationButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <GradCapIcon
              className={classNames(projectcss.all, sty.svg__yp2V8)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__zuDGh
              )}
            >
              {"Education"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"experienceButtonEntry"}
            data-plasmic-override={overrides.experienceButtonEntry}
            className={classNames("__wab_instance", sty.experienceButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <BuildingIcon
              className={classNames(projectcss.all, sty.svg__es8Wx)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__zjjO5
              )}
            >
              {"Experiences"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"skillsButtonEntry"}
            data-plasmic-override={overrides.skillsButtonEntry}
            className={classNames("__wab_instance", sty.skillsButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <SkillsIcon
              className={classNames(projectcss.all, sty.svg__xlduR)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__eHdVo
              )}
            >
              {"Skills"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"toolsButtonEntry"}
            data-plasmic-override={overrides.toolsButtonEntry}
            className={classNames("__wab_instance", sty.toolsButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <ToolsIcon
              className={classNames(projectcss.all, sty.svg__vfsY)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__cClDm
              )}
            >
              {"Tools"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"languagesButtonEntry"}
            data-plasmic-override={overrides.languagesButtonEntry}
            className={classNames("__wab_instance", sty.languagesButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <LanguagIcon
              className={classNames(projectcss.all, sty.svg__ePhQ)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___18ODu
              )}
            >
              {"Languages"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"publicationsButtonEntry"}
            data-plasmic-override={overrides.publicationsButtonEntry}
            className={classNames(
              "__wab_instance",
              sty.publicationsButtonEntry
            )}
            color={"green"}
            size={"small"}
          >
            <PublicationBookIcon
              className={classNames(projectcss.all, sty.svg__na0HL)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__pcTp1
              )}
            >
              {"Publications"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"licensesButtonEntry"}
            data-plasmic-override={overrides.licensesButtonEntry}
            className={classNames("__wab_instance", sty.licensesButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <LicenseIcon
              className={classNames(projectcss.all, sty.svg__dKWs)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__jQbaN
              )}
            >
              {"Licenses"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"certificationsButtonEntry"}
            data-plasmic-override={overrides.certificationsButtonEntry}
            className={classNames(
              "__wab_instance",
              sty.certificationsButtonEntry
            )}
            color={"green"}
            size={"small"}
          >
            <CertificateIcon
              className={classNames(projectcss.all, sty.svg__yTQx)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__z3Es
              )}
            >
              {"Certificates"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"patentsButtonEntry"}
            data-plasmic-override={overrides.patentsButtonEntry}
            className={classNames("__wab_instance", sty.patentsButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <PatentedIcon
              className={classNames(projectcss.all, sty.svg__g2ViT)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__fGt78
              )}
            >
              {"Patents"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"trademarksButtonEntry"}
            data-plasmic-override={overrides.trademarksButtonEntry}
            className={classNames("__wab_instance", sty.trademarksButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <TrademarkIcon
              className={classNames(projectcss.all, sty.svg__y6Amh)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__zyLl4
              )}
            >
              {"Trademarks"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
        </div>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formattingContainer: [
    "formattingContainer",
    "mainSectionButtons",
    "profileBanner",
    "introduction",
    "caseStudy",
    "scrollingOptions",
    "educationButton2",
    "experienceButton",
    "toolsButton",
    "skillsButton",
    "languagesButton",
    "patentsButton",
    "publicationsButton",
    "certificationsButton",
    "licensesButton",
    "trademarksButton",
    "seeMore",
    "seeLessButton",
    "completedSections",
    "sectionsWithContent",
    "introductionButtonEntry",
    "educationButtonEntry",
    "experienceButtonEntry",
    "skillsButtonEntry",
    "toolsButtonEntry",
    "languagesButtonEntry",
    "publicationsButtonEntry",
    "licensesButtonEntry",
    "certificationsButtonEntry",
    "patentsButtonEntry",
    "trademarksButtonEntry"
  ],
  mainSectionButtons: [
    "mainSectionButtons",
    "profileBanner",
    "introduction",
    "caseStudy",
    "scrollingOptions",
    "educationButton2",
    "experienceButton",
    "toolsButton",
    "skillsButton",
    "languagesButton",
    "patentsButton",
    "publicationsButton",
    "certificationsButton",
    "licensesButton",
    "trademarksButton",
    "seeMore",
    "seeLessButton"
  ],
  profileBanner: ["profileBanner"],
  introduction: ["introduction"],
  caseStudy: ["caseStudy"],
  scrollingOptions: [
    "scrollingOptions",
    "educationButton2",
    "experienceButton",
    "toolsButton",
    "skillsButton",
    "languagesButton",
    "patentsButton",
    "publicationsButton",
    "certificationsButton",
    "licensesButton"
  ],
  educationButton2: ["educationButton2"],
  experienceButton: ["experienceButton"],
  toolsButton: ["toolsButton"],
  skillsButton: ["skillsButton"],
  languagesButton: ["languagesButton"],
  patentsButton: ["patentsButton"],
  publicationsButton: ["publicationsButton"],
  certificationsButton: ["certificationsButton"],
  licensesButton: ["licensesButton"],
  trademarksButton: ["trademarksButton"],
  seeMore: ["seeMore"],
  seeLessButton: ["seeLessButton"],
  completedSections: [
    "completedSections",
    "sectionsWithContent",
    "introductionButtonEntry",
    "educationButtonEntry",
    "experienceButtonEntry",
    "skillsButtonEntry",
    "toolsButtonEntry",
    "languagesButtonEntry",
    "publicationsButtonEntry",
    "licensesButtonEntry",
    "certificationsButtonEntry",
    "patentsButtonEntry",
    "trademarksButtonEntry"
  ],
  sectionsWithContent: [
    "sectionsWithContent",
    "introductionButtonEntry",
    "educationButtonEntry",
    "experienceButtonEntry",
    "skillsButtonEntry",
    "toolsButtonEntry",
    "languagesButtonEntry",
    "publicationsButtonEntry",
    "licensesButtonEntry",
    "certificationsButtonEntry",
    "patentsButtonEntry",
    "trademarksButtonEntry"
  ],
  introductionButtonEntry: ["introductionButtonEntry"],
  educationButtonEntry: ["educationButtonEntry"],
  experienceButtonEntry: ["experienceButtonEntry"],
  skillsButtonEntry: ["skillsButtonEntry"],
  toolsButtonEntry: ["toolsButtonEntry"],
  languagesButtonEntry: ["languagesButtonEntry"],
  publicationsButtonEntry: ["publicationsButtonEntry"],
  licensesButtonEntry: ["licensesButtonEntry"],
  certificationsButtonEntry: ["certificationsButtonEntry"],
  patentsButtonEntry: ["patentsButtonEntry"],
  trademarksButtonEntry: ["trademarksButtonEntry"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formattingContainer: "div";
  mainSectionButtons: "div";
  profileBanner: typeof SubcomponentSelectorButtonsWSlot;
  introduction: typeof SubcomponentSelectorButtonsWSlot;
  caseStudy: typeof SubcomponentSelectorButtonsWSlot;
  scrollingOptions: "div";
  educationButton2: typeof SubcomponentSelectorButtonsWSlot;
  experienceButton: typeof SubcomponentSelectorButtonsWSlot;
  toolsButton: typeof SubcomponentSelectorButtonsWSlot;
  skillsButton: typeof SubcomponentSelectorButtonsWSlot;
  languagesButton: typeof SubcomponentSelectorButtonsWSlot;
  patentsButton: typeof SubcomponentSelectorButtonsWSlot;
  publicationsButton: typeof SubcomponentSelectorButtonsWSlot;
  certificationsButton: typeof SubcomponentSelectorButtonsWSlot;
  licensesButton: typeof SubcomponentSelectorButtonsWSlot;
  trademarksButton: typeof SubcomponentSelectorButtonsWSlot;
  seeMore: typeof SubcomponentSelectorButtonsWSlot;
  seeLessButton: typeof SubcomponentSelectorButtonsWSlot;
  completedSections: "div";
  sectionsWithContent: "div";
  introductionButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  educationButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  experienceButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  skillsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  toolsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  languagesButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  publicationsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  licensesButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  certificationsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  patentsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  trademarksButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicOnboardingCompletionPrompt__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicOnboardingCompletionPrompt__VariantsArgs;
    args?: PlasmicOnboardingCompletionPrompt__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicOnboardingCompletionPrompt__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicOnboardingCompletionPrompt__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicOnboardingCompletionPrompt__ArgProps,
          internalVariantPropNames:
            PlasmicOnboardingCompletionPrompt__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicOnboardingCompletionPrompt__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formattingContainer") {
    func.displayName = "PlasmicOnboardingCompletionPrompt";
  } else {
    func.displayName = `PlasmicOnboardingCompletionPrompt.${nodeName}`;
  }
  return func;
}

export const PlasmicOnboardingCompletionPrompt = Object.assign(
  // Top-level PlasmicOnboardingCompletionPrompt renders the root element
  makeNodeComponent("formattingContainer"),
  {
    // Helper components rendering sub-elements
    mainSectionButtons: makeNodeComponent("mainSectionButtons"),
    profileBanner: makeNodeComponent("profileBanner"),
    introduction: makeNodeComponent("introduction"),
    caseStudy: makeNodeComponent("caseStudy"),
    scrollingOptions: makeNodeComponent("scrollingOptions"),
    educationButton2: makeNodeComponent("educationButton2"),
    experienceButton: makeNodeComponent("experienceButton"),
    toolsButton: makeNodeComponent("toolsButton"),
    skillsButton: makeNodeComponent("skillsButton"),
    languagesButton: makeNodeComponent("languagesButton"),
    patentsButton: makeNodeComponent("patentsButton"),
    publicationsButton: makeNodeComponent("publicationsButton"),
    certificationsButton: makeNodeComponent("certificationsButton"),
    licensesButton: makeNodeComponent("licensesButton"),
    trademarksButton: makeNodeComponent("trademarksButton"),
    seeMore: makeNodeComponent("seeMore"),
    seeLessButton: makeNodeComponent("seeLessButton"),
    completedSections: makeNodeComponent("completedSections"),
    sectionsWithContent: makeNodeComponent("sectionsWithContent"),
    introductionButtonEntry: makeNodeComponent("introductionButtonEntry"),
    educationButtonEntry: makeNodeComponent("educationButtonEntry"),
    experienceButtonEntry: makeNodeComponent("experienceButtonEntry"),
    skillsButtonEntry: makeNodeComponent("skillsButtonEntry"),
    toolsButtonEntry: makeNodeComponent("toolsButtonEntry"),
    languagesButtonEntry: makeNodeComponent("languagesButtonEntry"),
    publicationsButtonEntry: makeNodeComponent("publicationsButtonEntry"),
    licensesButtonEntry: makeNodeComponent("licensesButtonEntry"),
    certificationsButtonEntry: makeNodeComponent("certificationsButtonEntry"),
    patentsButtonEntry: makeNodeComponent("patentsButtonEntry"),
    trademarksButtonEntry: makeNodeComponent("trademarksButtonEntry"),

    // Metadata about props expected for PlasmicOnboardingCompletionPrompt
    internalVariantProps: PlasmicOnboardingCompletionPrompt__VariantProps,
    internalArgProps: PlasmicOnboardingCompletionPrompt__ArgProps
  }
);

export default PlasmicOnboardingCompletionPrompt;
/* prettier-ignore-end */
