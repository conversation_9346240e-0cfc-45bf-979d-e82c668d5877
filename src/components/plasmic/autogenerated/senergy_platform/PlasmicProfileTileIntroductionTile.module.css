.introductionFormatting {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  flex-shrink: 1;
  height: auto;
  margin-bottom: 0px;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  margin-top: 0px;
  margin-left: 0px;
  row-gap: var(--token-j0qnbpah5w9U);
  position: relative;
  justify-self: flex-start;
  flex-wrap: nowrap;
  flex-grow: 0;
  align-content: stretch;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: 16px 16px 8px 0px;
}
@media (max-width: 860px) {
  .introductionFormatting {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    row-gap: var(--token-4Wrp9mDZCSCQ);
    column-gap: 0px;
  }
}
.introductionFormattingeditable {
  display: flex;
  flex-direction: row;
  row-gap: 0px;
}
.introSummaryDisplay {
  width: auto;
  height: auto;
  flex-grow: 0;
  flex-shrink: 1;
  left: auto;
  top: auto;
  position: relative;
  padding: var(--token-j0qnbpah5w9U);
}
@media (max-width: 860px) {
  .introSummaryDisplay {
    left: auto;
    top: auto;
  }
}
.imageContainerWithUpload {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: column;
  left: auto;
  top: auto;
  padding: var(--token-sazGmnf7GWAk);
}
@media (max-width: 860px) {
  .imageContainerWithUpload {
    left: auto;
    top: auto;
  }
}
.introductionImageFile {
  position: relative;
  object-fit: cover;
  width: 350px;
  height: 400px;
  flex-shrink: 0;
  margin: var(--token-4Wrp9mDZCSCQ);
}
.introductionImageFile > picture > img {
  object-fit: cover;
}
.introductionImageFileoverview_introOverview {
  display: none;
}
.introductionImageFileintroOnboarding_introComposition {
  display: block;
}
.subcomponentUploadButton:global(.__wab_instance) {
  position: absolute;
  top: 0px;
  left: 0px;
  display: none;
}
.subcomponentUploadButtoneditable:global(.__wab_instance) {
  display: block;
}
.subcomponentUploadButtonintroOnboarding_introComposition:global(
    .__wab_instance
  ) {
  display: none;
}
.subcomponentUploadButtonintroOnboarding_imageUpload:global(.__wab_instance) {
  display: block;
}
