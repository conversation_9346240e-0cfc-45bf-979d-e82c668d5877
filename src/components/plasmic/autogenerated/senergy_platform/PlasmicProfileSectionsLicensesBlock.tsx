/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 5i3SEiGXS3YW

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileLicenseTile from "../../ProfileTileLicenseTile"; // plasmic-import: NlwHt2k50uIY/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsLicensesBlock.module.css"; // plasmic-import: 5i3SEiGXS3YW/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsLicensesBlock__VariantMembers = {
  overviewGrid: "overviewGrid";
  editable: "editable";
};
export type PlasmicProfileSectionsLicensesBlock__VariantsArgs = {
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileSectionsLicensesBlock__VariantsArgs;
export const PlasmicProfileSectionsLicensesBlock__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileSectionsLicensesBlock__ArgsType = {
  allLicenses?: any;
  onAllLicensesChange?: (val: string) => void;
  addButtonExpandType?: string;
  addButtonOnClick?: (event: any) => void;
  addButtonLicenseOnClick?: (event: any) => void;
  addButtonCertificationOnClick?: (event: any) => void;
  addButtonPatentOnClick?: (event: any) => void;
  addButtonTrademarkOnClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicProfileSectionsLicensesBlock__ArgsType;
export const PlasmicProfileSectionsLicensesBlock__ArgProps =
  new Array<ArgPropType>(
    "allLicenses",
    "onAllLicensesChange",
    "addButtonExpandType",
    "addButtonOnClick",
    "addButtonLicenseOnClick",
    "addButtonCertificationOnClick",
    "addButtonPatentOnClick",
    "addButtonTrademarkOnClick"
  );

export type PlasmicProfileSectionsLicensesBlock__OverridesType = {
  licensesAndCertificationsSection?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  displayContainer?: Flex__<"section">;
  tLicenseTile?: Flex__<typeof ProfileTileLicenseTile>;
};

export interface DefaultProfileSectionsLicensesBlockProps {
  allLicenses?: any;
  onAllLicensesChange?: (val: string) => void;
  addButtonExpandType?: string;
  addButtonOnClick?: (event: any) => void;
  addButtonLicenseOnClick?: (event: any) => void;
  addButtonCertificationOnClick?: (event: any) => void;
  addButtonPatentOnClick?: (event: any) => void;
  addButtonTrademarkOnClick?: (event: any) => void;
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsLicensesBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsLicensesBlock__VariantsArgs;
  args: PlasmicProfileSectionsLicensesBlock__ArgsType;
  overrides: PlasmicProfileSectionsLicensesBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "tLicenseTile[].titleInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].issueDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].registrationNumberInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].instructorInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].issueLocationInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].expirationDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].licensingBodyInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].deleteButtonClickStage",
        type: "private",
        variableType: "number"
      },
      {
        path: "tLicenseTile[].deleteButtonDisabled",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].descriptionInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "allLicenses",
        type: "writable",
        variableType: "object",

        valueProp: "allLicenses",
        onChangeProp: "onAllLicensesChange"
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"licensesAndCertificationsSection"}
      data-plasmic-override={overrides.licensesAndCertificationsSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.licensesAndCertificationsSection
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        addButtonBaseOnClick={args.addButtonOnClick}
        addButtonCertificationOnClick={args.addButtonCertificationOnClick}
        addButtonLicenseOnClick={args.addButtonLicenseOnClick}
        addButtonPatentOnClick={args.addButtonPatentOnClick}
        addButtonTrademarkOnClick={args.addButtonTrademarkOnClick}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewGrid]: hasVariant(
              $state,
              "overviewGrid",
              "overviewGrid"
            )
          }
        )}
        editable={
          hasVariant($state, "editable", "editable")
            ? true
            : (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })()
        }
        overviewGrid={
          hasVariant($state, "overviewGrid", "overviewGrid") ? true : undefined
        }
      >
        {"Licenses"}
      </ProfileSectionsProfileSectionHeading>
      <section
        data-plasmic-name={"displayContainer"}
        data-plasmic-override={overrides.displayContainer}
        className={classNames(projectcss.all, sty.displayContainer, {
          [sty.displayContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
          (() => {
            try {
              return $state.allLicenses.data;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()
        ).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (() => {
            const child$Props = {
              className: classNames("__wab_instance", sty.tLicenseTile, {
                [sty.tLicenseTileoverviewGrid]: hasVariant(
                  $state,
                  "overviewGrid",
                  "overviewGrid"
                )
              }),
              deleteButtonClickStage: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "deleteButtonClickStage"
              ]),
              deleteButtonDisabled: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "deleteButtonDisabled"
              ]),
              descriptionInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "descriptionInputValue"
              ]),
              editable: (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })(),
              expirationDateInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "expirationDateInputValue"
              ]),
              instructorInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "instructorInputValue"
              ]),
              issueDateInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "issueDateInputValue"
              ]),
              issueLocationInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "issueLocationInputValue"
              ]),
              key: currentIndex,
              licenseId: (() => {
                try {
                  return currentItem.id;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })(),
              licensingBodyInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "licensingBodyInputValue"
              ]),
              onDeleteButtonClickStageChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "deleteButtonClickStage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonDisabledChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "deleteButtonDisabled"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDescriptionInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "descriptionInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onExpirationDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "expirationDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onInstructorInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "instructorInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onIssueDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "issueDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onIssueLocationInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "issueLocationInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onLicensingBodyInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "licensingBodyInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onRegistrationNumberInputValueChange: async (
                ...eventArgs: any
              ) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "registrationNumberInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onTitleInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "titleInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              overview: hasVariant($state, "overviewGrid", "overviewGrid")
                ? true
                : undefined,
              registrationNumberInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "registrationNumberInputValue"
              ]),
              titleInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "titleInputValue"
              ])
            };

            initializePlasmicStates(
              $state,
              [
                {
                  name: "tLicenseTile[].titleInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.title;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].issueDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.issue_date
                          ? currentItem.issue_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].registrationNumberInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.registration_number;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].instructorInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.instructor;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].issueLocationInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.issue_location;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].expirationDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.expiration_date
                          ? currentItem.expiration_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].licensingBodyInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.licensing_body;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].deleteButtonClickStage",
                  initFunc: ({ $props, $state, $queries }) => 0
                },
                {
                  name: "tLicenseTile[].deleteButtonDisabled",
                  initFunc: ({ $props, $state, $queries }) => undefined
                },
                {
                  name: "tLicenseTile[].descriptionInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.additional_info;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                }
              ],
              [__plasmic_idx_0]
            );
            return (
              <ProfileTileLicenseTile
                data-plasmic-name={"tLicenseTile"}
                data-plasmic-override={overrides.tLicenseTile}
                {...child$Props}
              />
            );
          })();
        })}
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  licensesAndCertificationsSection: [
    "licensesAndCertificationsSection",
    "profileSectionsProfileSectionHeading",
    "displayContainer",
    "tLicenseTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading"
  ],
  displayContainer: ["displayContainer", "tLicenseTile"],
  tLicenseTile: ["tLicenseTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  licensesAndCertificationsSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  displayContainer: "section";
  tLicenseTile: typeof ProfileTileLicenseTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsLicensesBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsLicensesBlock__VariantsArgs;
    args?: PlasmicProfileSectionsLicensesBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsLicensesBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsLicensesBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileSectionsLicensesBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsLicensesBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsLicensesBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "licensesAndCertificationsSection") {
    func.displayName = "PlasmicProfileSectionsLicensesBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsLicensesBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsLicensesBlock = Object.assign(
  // Top-level PlasmicProfileSectionsLicensesBlock renders the root element
  makeNodeComponent("licensesAndCertificationsSection"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    displayContainer: makeNodeComponent("displayContainer"),
    tLicenseTile: makeNodeComponent("tLicenseTile"),

    // Metadata about props expected for PlasmicProfileSectionsLicensesBlock
    internalVariantProps: PlasmicProfileSectionsLicensesBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsLicensesBlock__ArgProps
  }
);

export default PlasmicProfileSectionsLicensesBlock;
/* prettier-ignore-end */
