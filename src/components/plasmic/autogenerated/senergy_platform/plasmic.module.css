@import url("https://fonts.googleapis.com/css2?family=Inconsolata%3Aital%2Cwght%400%2C400%3B0%2C600%3B0%2C700&display=swap");

.plasmic_tokens {
  --token-sazGmnf7GWAk: 0px;
  --plasmic-token-default-padding: var(--token-sazGmnf7GWAk);
  --token-v4jufhOu3lt9: #5e4556;
  --plasmic-token-eggplant: var(--token-v4jufhOu3lt9);
  --token-cXZCUAmMzuNV: #f5ba61;
  --plasmic-token-sunflower: var(--token-cXZCUAmMzuNV);
  --token-K5FbAPSIIrXM: #292929;
  --plasmic-token-stone: var(--token-K5FbAPSIIrXM);
  --token-PJrtmRUMFAJb: #d5cec3;
  --plasmic-token-clay: var(--token-PJrtmRUMFAJb);
  --token-lUfRIntGAfk9: #a4baa1;
  --plasmic-token-sage: var(--token-lUfRIntGAfk9);
  --token-XQQdLIBrONt7: #4d7058;
  --plasmic-token-forest: var(--token-XQQdLIBrONt7);
  --token-to1fT9g-t4Js: #ba563b;
  --plasmic-token-rustic-brick: var(--token-to1fT9g-t4Js);
  --token-1AMvw6c2eIK7: #f9f9f9;
  --plasmic-token-off-white: var(--token-1AMvw6c2eIK7);
  --token-hKlnSDJVAhUx: 46px;
  --plasmic-token-master-header: var(--token-hKlnSDJVAhUx);
  --token-hZBgG8kTaig3: 35px;
  --plasmic-token-heading-1: var(--token-hZBgG8kTaig3);
  --token-AMP1angFPBtf: 30px;
  --plasmic-token-heading-2: var(--token-AMP1angFPBtf);
  --token-9KumB6TRpaad: 24px;
  --plasmic-token-heading-3: var(--token-9KumB6TRpaad);
  --token-_-i82ElPHE7I: 20px;
  --plasmic-token-sub-heading: var(--token-_-i82ElPHE7I);
  --token-2UEfYzPsoOY0: 16px;
  --plasmic-token-body-text: var(--token-2UEfYzPsoOY0);
  --token-Q3bPqi9oloJF: #e2d7de;
  --plasmic-token-eggplant-80: var(--token-Q3bPqi9oloJF);
  --token-qpkjQiN0Xn2i: #c4afbd;
  --plasmic-token-eggplant-60: var(--token-qpkjQiN0Xn2i);
  --token-J3XggExhYpve: #a7879d;
  --plasmic-token-eggplant-40: var(--token-J3XggExhYpve);
  --token-oR0zJLY_XTdW: #86637b;
  --plasmic-token-eggplant-20: var(--token-oR0zJLY_XTdW);
  --token-_gt8keH_-0aP: #669776;
  --plasmic-token-forest-20: var(--token-_gt8keH_-0aP);
  --token-SAyLjgjq93pE: #8cb198;
  --plasmic-token-forest-40: var(--token-SAyLjgjq93pE);
  --token-vVQ8x0vVx8ie: #b3cbba;
  --plasmic-token-forest-60: var(--token-vVQ8x0vVx8ie);
  --token-Q80HikmPcRac: #d9e5dd;
  --plasmic-token-forest-80: var(--token-Q80HikmPcRac);
  --token-F0-tInDly4PE: #eeebe7;
  --plasmic-token-clay-60: var(--token-F0-tInDly4PE);
  --token-2vSHtOO_pyS1: #c8d5c7;
  --plasmic-token-sage-40: var(--token-2vSHtOO_pyS1);
  --token-zdHH9r9UDmmt: #edf1ec;
  --plasmic-token-sage-80: var(--token-zdHH9r9UDmmt);
  --token-WnTQM0lL0ydn: #cc765e;
  --plasmic-token-rustic-brick-20: var(--token-WnTQM0lL0ydn);
  --token-ivwQfC6mlK8Q: #d99886;
  --plasmic-token-rustic-brick-40: var(--token-ivwQfC6mlK8Q);
  --token-k3-H4jDQ4YK9: #e6baae;
  --plasmic-token-rustic-brick-60: var(--token-k3-H4jDQ4YK9);
  --token-FNgHGKQGu4fn: #f2ddd7;
  --plasmic-token-rustic-brick-80: var(--token-FNgHGKQGu4fn);
  --token-p09LDPmbF81_: #d4d4d4;
  --plasmic-token-stone-80: var(--token-p09LDPmbF81_);
  --token-ttBxfk-RsDf2: #a9a9a9;
  --plasmic-token-stone-60: var(--token-ttBxfk-RsDf2);
  --token-3OLw18f8JRN3: #7e7e7e;
  --plasmic-token-stone-40: var(--token-3OLw18f8JRN3);
  --token-yPq8Z3hhDPZH: #545454;
  --plasmic-token-stone-20: var(--token-yPq8Z3hhDPZH);
  --token-nO74MOax1D2a: #f7c880;
  --plasmic-token-sunflower-20: var(--token-nO74MOax1D2a);
  --token-sHgYTLDCNKpB: #f9d5a0;
  --plasmic-token-sunflower-40: var(--token-sHgYTLDCNKpB);
  --token-OenzbqCFq48y: #fbe3c0;
  --plasmic-token-sunflower-60: var(--token-OenzbqCFq48y);
  --token-2uHFSQaT2eZU: #fdf1df;
  --plasmic-token-sunflower-80: var(--token-2uHFSQaT2eZU);
  --token-4Wrp9mDZCSCQ: 8px;
  --plasmic-token-standard-padding: var(--token-4Wrp9mDZCSCQ);
  --token-IfwtVZvVvF7g: Gelato Typewriter;
  --plasmic-token-gelato-typewriter: var(--token-IfwtVZvVvF7g);
  --token-yAY_UcQXgxsF: #eed202;
  --plasmic-token-caution: var(--token-yAY_UcQXgxsF);
  --token-Jis9JaUzrgN0: #000000;
  --plasmic-token-pure-black: var(--token-Jis9JaUzrgN0);
  --token-xo_r2w5pebq-: #ffffff;
  --plasmic-token-pure-white: var(--token-xo_r2w5pebq-);
  --token-vmreU-dg02sh: #e90000;
  --plasmic-token-danger: var(--token-vmreU-dg02sh);
  --token-MPvkQ1rq8Due: #ffffff00;
  --plasmic-token-clear: var(--token-MPvkQ1rq8Due);
  --token-j0qnbpah5w9U: 20px;
  --plasmic-token-default-icon: var(--token-j0qnbpah5w9U);
  --token-M1l4keX1sfKm: 30px;
  --plasmic-token-header-spacing: var(--token-M1l4keX1sfKm);
  --token-ey2y7HI9U2zx: 65px;
  --plasmic-token-tile-icon-size: var(--token-ey2y7HI9U2zx);
  --token-M1cOQzpe0467: 55px;
  --plasmic-token-banner-photo: var(--token-M1cOQzpe0467);
  --token-agfFfkxgxH6d: 14px;
  --plasmic-token-sub-text: var(--token-agfFfkxgxH6d);
  --token-ptnlAHOp9Vq0: 4px;
  --plasmic-token-tag-gap: var(--token-ptnlAHOp9Vq0);
  --token-yOrnGWfG0DtN: #ded8cf;
  --plasmic-token-clay-20: var(--token-yOrnGWfG0DtN);
  --token-6c2Z9MBevRAq: #e6e2db;
  --plasmic-token-clay-40: var(--token-6c2Z9MBevRAq);
  --token-5_Q90hFZ9CmK: #f7f5f3;
  --plasmic-token-clay-80: var(--token-5_Q90hFZ9CmK);
  --token-rB3BKCgczoWa: 14px;
  --plasmic-token-small-icon: var(--token-rB3BKCgczoWa);
  --token-0GsCPVoopKtO: 4px;
  --plasmic-token-small-padding: var(--token-0GsCPVoopKtO);
  --token-VuPwbNNk9FIa: 40px;
  --plasmic-token-message-icon: var(--token-VuPwbNNk9FIa);
  --token-z1yrQVi72Nj1: Nitti Typewriter;
  --plasmic-token-nitti-typewriter: var(--token-z1yrQVi72Nj1);
  --token-8HwyO4mnTWdf: 12px;
  --plasmic-token-block-spacing: var(--token-8HwyO4mnTWdf);
  --token-xKF2PGG60dHg: 40px;
  --plasmic-token-message-photo: var(--token-xKF2PGG60dHg);
  --token-Ab1KZcxm-kp_: #ededed;
  --plasmic-token-highlight-color-for-animation: var(--token-Ab1KZcxm-kp_);
  --token-R0GUjkbC5dJn: 48px;
  --plasmic-token-m-preview-tile-image: var(--token-R0GUjkbC5dJn);
  --token-xPLlOa54vJla: #2929291a;
  --plasmic-token-profile-image-overlay: var(--token-xPLlOa54vJla);
  --token-FtzoSQMzGLh-: 215px;
  --plasmic-token-navbar-small-width: var(--token-FtzoSQMzGLh-);
  --token-3mz1TnB2n28G: 515px;
  --plasmic-token-navbar-sub-bar-small-spacing: var(--token-3mz1TnB2n28G);
  --token-qEtVY7lTL0pW: 250px;
  --plasmic-token-navbar-default-width: var(--token-qEtVY7lTL0pW);
  --token-p8EG8cGBGqkM: #d2dcd0;
  --plasmic-token-sage-50: var(--token-p8EG8cGBGqkM);
  --token-YiZ306VSHyEA: #bbcbb9;
  --plasmic-token-sage-25: var(--token-YiZ306VSHyEA);
  --token-hx9QrD7iOwnt: var(--antd-colorIcon);
  --plasmic-token-system-icon: var(--token-hx9QrD7iOwnt);
  --token-u7F5TheZwaeC: Inter;
  --plasmic-token-sans-serif: var(--token-u7F5TheZwaeC);
  --token-SveQ5q2SMxX2: 1rem;
  --plasmic-token-font-md: var(--token-SveQ5q2SMxX2);
  --token-j3Ih8_spkvAf: 1.25;
  --plasmic-token-line-height-md: var(--token-j3Ih8_spkvAf);
  --token-TvgjBJhUN9vy: 1rem;
  --plasmic-token-size-16: var(--token-TvgjBJhUN9vy);
  --token-iU7CJ2I63sse: 1.5rem;
  --plasmic-token-size-24: var(--token-iU7CJ2I63sse);
  --token-7HK3l00dwlW6: 1.25rem;
  --plasmic-token-font-lg: var(--token-7HK3l00dwlW6);
  --token-AF7_Ok_pcOUI: 0.75rem;
  --plasmic-token-size-12: var(--token-AF7_Ok_pcOUI);
  --token-n__VlLqZzTUZ: 0.5rem;
  --plasmic-token-size-8: var(--token-n__VlLqZzTUZ);
  --token-vqzmnn2AP7oy: 0.375rem;
  --plasmic-token-size-6: var(--token-vqzmnn2AP7oy);
  --token-fK9esUultTwY: 0.25rem;
  --plasmic-token-size-4: var(--token-fK9esUultTwY);
  --token-KHmmf3a-LkQj: 0.75rem;
  --plasmic-token-font-sm: var(--token-KHmmf3a-LkQj);
  --token-sKmUf0bRx7gU: 1;
  --plasmic-token-line-height-sm: var(--token-sKmUf0bRx7gU);
  --token-EHQ7667TNoDw: 1.4;
  --plasmic-token-line-height-lg: var(--token-EHQ7667TNoDw);
  --token-P8fEX3pwqply: 0.125rem;
  --plasmic-token-size-2: var(--token-P8fEX3pwqply);
  --token-jTnA6Nx6GBJM: 0.625rem;
  --plasmic-token-size-10: var(--token-jTnA6Nx6GBJM);
}

.plasmic_tokens {
  --plsmc-standard-width: 800px;
  --plsmc-wide-width: 1280px;
  --plsmc-viewport-gap: 16px;
  --plsmc-wide-chunk: calc(
    ((var(--plsmc-wide-width) - var(--plsmc-standard-width)) / 2) -
      var(--plsmc-viewport-gap)
  );
}

.plasmic_default_styles {
  --mixin-DVHTMUeZxkiW_font-family: "Arial";
  --mixin-DVHTMUeZxkiW_font-size: var(--token-2UEfYzPsoOY0);
  --mixin-DVHTMUeZxkiW_font-weight: 400;
  --mixin-DVHTMUeZxkiW_font-style: normal;
  --mixin-DVHTMUeZxkiW_color: var(--token-K5FbAPSIIrXM);
  --mixin-DVHTMUeZxkiW_text-align: left;
  --mixin-DVHTMUeZxkiW_text-transform: none;
  --mixin-DVHTMUeZxkiW_line-height: 1.5;
  --mixin-DVHTMUeZxkiW_letter-spacing: normal;
  --mixin-DVHTMUeZxkiW_white-space: pre-wrap;
  --mixin-DVHTMUeZxkiW_user-select: text;
  --mixin-DVHTMUeZxkiW_text-decoration-line: none;
  --mixin-DVHTMUeZxkiW_text-overflow: clip;
  --mixin-FLl2gpVKHWYx_color: #000000;
  --mixin-FLl2gpVKHWYx_font-weight: 700;
  --mixin-FLl2gpVKHWYx_font-size: 64px;
  --mixin-FLl2gpVKHWYx_line-height: 1;
  --mixin-FLl2gpVKHWYx_letter-spacing: -1px;
  --mixin-FLl2gpVKHWYx_white-space: pre-wrap;
  --mixin-oAZmz7rKnGKZ_color: #000000;
  --mixin-oAZmz7rKnGKZ_font-size: 48px;
  --mixin-oAZmz7rKnGKZ_font-weight: 700;
  --mixin-oAZmz7rKnGKZ_letter-spacing: -0.5px;
  --mixin-oAZmz7rKnGKZ_line-height: 1.1;
  --mixin-oAZmz7rKnGKZ_white-space: pre-wrap;
  --mixin-y_TCs12AGhMj_color: #0070f3;
  --mixin-y_TCs12AGhMj_white-space: pre-wrap;
  --mixin-5Qz0KztT6G5s_color: #000000;
  --mixin-5Qz0KztT6G5s_font-size: 32px;
  --mixin-5Qz0KztT6G5s_font-weight: 600;
  --mixin-5Qz0KztT6G5s_line-height: 1.2;
  --mixin-5Qz0KztT6G5s_white-space: pre-wrap;
  --mixin-HtpouCmKQlY9_color: #000000;
  --mixin-HtpouCmKQlY9_font-size: 24px;
  --mixin-HtpouCmKQlY9_font-weight: 600;
  --mixin-HtpouCmKQlY9_line-height: 1.3;
  --mixin-HtpouCmKQlY9_white-space: pre-wrap;
  --mixin-ajoy1VbZh3yG_background: linear-gradient(#f8f8f8, #f8f8f8);
  --mixin-ajoy1VbZh3yG_border-bottom-color: #dddddd;
  --mixin-ajoy1VbZh3yG_border-bottom-style: solid;
  --mixin-ajoy1VbZh3yG_border-bottom-width: 1px;
  --mixin-ajoy1VbZh3yG_border-left-color: #dddddd;
  --mixin-ajoy1VbZh3yG_border-left-style: solid;
  --mixin-ajoy1VbZh3yG_border-left-width: 1px;
  --mixin-ajoy1VbZh3yG_border-right-color: #dddddd;
  --mixin-ajoy1VbZh3yG_border-right-style: solid;
  --mixin-ajoy1VbZh3yG_border-right-width: 1px;
  --mixin-ajoy1VbZh3yG_border-top-color: #dddddd;
  --mixin-ajoy1VbZh3yG_border-top-style: solid;
  --mixin-ajoy1VbZh3yG_border-top-width: 1px;
  --mixin-ajoy1VbZh3yG_border-bottom-left-radius: 3px;
  --mixin-ajoy1VbZh3yG_border-bottom-right-radius: 3px;
  --mixin-ajoy1VbZh3yG_border-top-left-radius: 3px;
  --mixin-ajoy1VbZh3yG_border-top-right-radius: 3px;
  --mixin-ajoy1VbZh3yG_font-family: "Inconsolata";
  --mixin-ajoy1VbZh3yG_padding-bottom: 1px;
  --mixin-ajoy1VbZh3yG_padding-left: 4px;
  --mixin-ajoy1VbZh3yG_padding-right: 4px;
  --mixin-ajoy1VbZh3yG_padding-top: 1px;
  --mixin-ajoy1VbZh3yG_white-space: pre-wrap;
  --mixin-Jp9lEKdKByZH_border-left-color: #dddddd;
  --mixin-Jp9lEKdKByZH_border-left-style: solid;
  --mixin-Jp9lEKdKByZH_border-left-width: 3px;
  --mixin-Jp9lEKdKByZH_color: #888888;
  --mixin-Jp9lEKdKByZH_padding-left: 10px;
  --mixin-Jp9lEKdKByZH_white-space: pre-wrap;
  --mixin--QOO7Vq1aTFi_background: linear-gradient(#f8f8f8, #f8f8f8);
  --mixin--QOO7Vq1aTFi_border-bottom-color: #dddddd;
  --mixin--QOO7Vq1aTFi_border-bottom-style: solid;
  --mixin--QOO7Vq1aTFi_border-bottom-width: 1px;
  --mixin--QOO7Vq1aTFi_border-left-color: #dddddd;
  --mixin--QOO7Vq1aTFi_border-left-style: solid;
  --mixin--QOO7Vq1aTFi_border-left-width: 1px;
  --mixin--QOO7Vq1aTFi_border-right-color: #dddddd;
  --mixin--QOO7Vq1aTFi_border-right-style: solid;
  --mixin--QOO7Vq1aTFi_border-right-width: 1px;
  --mixin--QOO7Vq1aTFi_border-top-color: #dddddd;
  --mixin--QOO7Vq1aTFi_border-top-style: solid;
  --mixin--QOO7Vq1aTFi_border-top-width: 1px;
  --mixin--QOO7Vq1aTFi_border-bottom-left-radius: 3px;
  --mixin--QOO7Vq1aTFi_border-bottom-right-radius: 3px;
  --mixin--QOO7Vq1aTFi_border-top-left-radius: 3px;
  --mixin--QOO7Vq1aTFi_border-top-right-radius: 3px;
  --mixin--QOO7Vq1aTFi_font-family: "Inconsolata";
  --mixin--QOO7Vq1aTFi_padding-bottom: 3px;
  --mixin--QOO7Vq1aTFi_padding-left: 6px;
  --mixin--QOO7Vq1aTFi_padding-right: 6px;
  --mixin--QOO7Vq1aTFi_padding-top: 3px;
  --mixin--QOO7Vq1aTFi_white-space: pre-wrap;
  --mixin-KqCgI7MzKvg3_display: flex;
  --mixin-KqCgI7MzKvg3_flex-direction: column;
  --mixin-KqCgI7MzKvg3_align-items: stretch;
  --mixin-KqCgI7MzKvg3_justify-content: flex-start;
  --mixin-KqCgI7MzKvg3_list-style-position: outside;
  --mixin-KqCgI7MzKvg3_padding-left: 40px;
  --mixin-KqCgI7MzKvg3_position: relative;
  --mixin-KqCgI7MzKvg3_list-style-type: disc;
  --mixin-KqCgI7MzKvg3_white-space: pre-wrap;
  --mixin-ru-4-vD6C5-4_display: flex;
  --mixin-ru-4-vD6C5-4_flex-direction: column;
  --mixin-ru-4-vD6C5-4_align-items: stretch;
  --mixin-ru-4-vD6C5-4_justify-content: flex-start;
  --mixin-ru-4-vD6C5-4_list-style-position: outside;
  --mixin-ru-4-vD6C5-4_padding-left: 40px;
  --mixin-ru-4-vD6C5-4_position: relative;
  --mixin-ru-4-vD6C5-4_list-style-type: decimal;
  --mixin-ru-4-vD6C5-4_white-space: pre-wrap;
  --mixin-7jCLY220ogpb_color: #000000;
  --mixin-7jCLY220ogpb_font-size: 20px;
  --mixin-7jCLY220ogpb_font-weight: 600;
  --mixin-7jCLY220ogpb_line-height: 1.5;
  --mixin-7jCLY220ogpb_white-space: pre-wrap;
  --mixin-Z0_TyNPmzwDF_color: #000000;
  --mixin-Z0_TyNPmzwDF_font-size: 16px;
  --mixin-Z0_TyNPmzwDF_font-weight: 600;
  --mixin-Z0_TyNPmzwDF_line-height: 1.5;
  --mixin-Z0_TyNPmzwDF_white-space: pre-wrap;
  --mixin-rR9HkFETU-13_color: #3291ff;
  --mixin-rR9HkFETU-13_white-space: pre-wrap;
  --mixin-B_Jc7QxALCP1_white-space: pre-wrap;
  --mixin-hpvVNVgEHXpC_white-space: pre-wrap;
  --mixin-uN6WVWBpCM_L_white-space: pre-wrap;
}

@media (max-width: 480px) {
  .plasmic_default_styles {
    --mixin-FLl2gpVKHWYx_white-space: pre-wrap;
    --mixin-oAZmz7rKnGKZ_white-space: pre-wrap;
  }
}

.plasmic_mixins {
  --mixin-dt24Vi68ck9z_box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  --plasmic-mixin-light-drop-shadow_box-shadow: var(
    --mixin-dt24Vi68ck9z_box-shadow
  );
  --mixin-dt24Vi68ck9z_white-space: pre-wrap;
  --plasmic-mixin-light-drop-shadow_white-space: var(
    --mixin-dt24Vi68ck9z_white-space
  );
  --mixin-Dqvr01xbJKxm_white-space: pre-wrap;
  --plasmic-mixin-profile-section-header-spacing_white-space: var(
    --mixin-Dqvr01xbJKxm_white-space
  );
  --mixin-VUrbmCk6op5L_white-space: pre-wrap;
  --plasmic-mixin-tile-spacing_white-space: var(
    --mixin-VUrbmCk6op5L_white-space
  );
  --mixin-qyGQsLvcq6x7_white-space: pre-wrap;
  --plasmic-mixin-tile-icon-spacing_white-space: var(
    --mixin-qyGQsLvcq6x7_white-space
  );
  --mixin-t35tYFWIkslK_white-space: pre-wrap;
  --plasmic-mixin-card-bounce-scale_white-space: var(
    --mixin-t35tYFWIkslK_white-space
  );
  --mixin-IwzxwtAaJzmZ_white-space: pre-wrap;
  --plasmic-mixin-info-bar-spacing_white-space: var(
    --mixin-IwzxwtAaJzmZ_white-space
  );
  --mixin-Jf3k9juuCFuD_white-space: pre-wrap;
  --plasmic-mixin-card-bounce-animation_white-space: var(
    --mixin-Jf3k9juuCFuD_white-space
  );
  --mixin-tGnZS2-mXGUJ_white-space: pre-wrap;
  --plasmic-mixin-overview-slot_white-space: var(
    --mixin-tGnZS2-mXGUJ_white-space
  );
  --mixin-1KfIkDgx3wNa_box-shadow: 0px 2px 20px -2px #8b8b8b33;
  --plasmic-mixin-drop-shadow-with-border_box-shadow: var(
    --mixin-1KfIkDgx3wNa_box-shadow
  );
  --mixin-1KfIkDgx3wNa_white-space: pre-wrap;
  --plasmic-mixin-drop-shadow-with-border_white-space: var(
    --mixin-1KfIkDgx3wNa_white-space
  );
  --mixin-IdCglH055zJE_white-space: pre-wrap;
  --plasmic-mixin-preview-tiles-animations_white-space: var(
    --mixin-IdCglH055zJE_white-space
  );
  --mixin-tYLJEQxZFijU_white-space: pre-wrap;
  --plasmic-mixin-glass-overlay_white-space: var(
    --mixin-tYLJEQxZFijU_white-space
  );
  --mixin-ovPAUn3IlNlj_white-space: pre-wrap;
  --plasmic-mixin-glass-overlay-2_white-space: var(
    --mixin-ovPAUn3IlNlj_white-space
  );
  --mixin-uOHwwsiLm_U9_box-shadow: 0px 4px 16px -8px rgba(0, 0, 0, 0.2);
  --plasmic-mixin-navbar-shadow_box-shadow: var(
    --mixin-uOHwwsiLm_U9_box-shadow
  );
  --mixin-uOHwwsiLm_U9_white-space: pre-wrap;
  --plasmic-mixin-navbar-shadow_white-space: var(
    --mixin-uOHwwsiLm_U9_white-space
  );
  --mixin-1SAQd062zfmh_white-space: pre-wrap;
  --plasmic-mixin-bottom-boarder-style_white-space: var(
    --mixin-1SAQd062zfmh_white-space
  );
}

:where(.all) {
  display: block;
  white-space: inherit;
  grid-row: auto;
  grid-column: auto;
  position: relative;
  background: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  row-gap: 0px;
  column-gap: 0px;
  box-shadow: none;
  box-sizing: border-box;
  text-decoration-line: none;
  margin: 0;
  border-width: 0px;
}
:where(.__wab_expr_html_text *) {
  white-space: inherit;
  grid-row: auto;
  grid-column: auto;
  background: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  row-gap: 0px;
  column-gap: 0px;
  box-shadow: none;
  box-sizing: border-box;
  margin: 0;
  border-width: 0px;
}

:where(.img) {
  display: inline-block;
}
:where(.__wab_expr_html_text img) {
  white-space: inherit;
}

:where(.li) {
  display: list-item;
}
:where(.__wab_expr_html_text li) {
  white-space: inherit;
}

:where(.span) {
  display: inline;
  position: static;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
}
:where(.__wab_expr_html_text span) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
}

:where(.input) {
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: linear-gradient(#ffffff, #ffffff);
  padding: 2px;
  border: 1px solid lightgray;
}
:where(.__wab_expr_html_text input) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: linear-gradient(#ffffff, #ffffff);
  padding: 2px;
  border: 1px solid lightgray;
}

:where(.textarea) {
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  padding: 2px;
  border: 1px solid lightgray;
}
:where(.__wab_expr_html_text textarea) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  padding: 2px;
  border: 1px solid lightgray;
}

:where(.button) {
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: none;
  align-items: flex-start;
  text-align: center;
  padding: 2px 6px;
  border: 1px solid lightgray;
}
:where(.__wab_expr_html_text button) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: none;
  align-items: flex-start;
  text-align: center;
  padding: 2px 6px;
  border: 1px solid lightgray;
}

:where(.code) {
  font-family: inherit;
  line-height: inherit;
}
:where(.__wab_expr_html_text code) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
}

:where(.pre) {
  font-family: inherit;
  line-height: inherit;
}
:where(.__wab_expr_html_text pre) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
}

:where(.p) {
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
}
:where(.__wab_expr_html_text p) {
  white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
}

:where(.h1) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h1) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h2) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h2) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h3) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h3) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h4) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h4) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h5) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h5) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.h6) {
  font-size: inherit;
  font-weight: inherit;
}
:where(.__wab_expr_html_text h6) {
  white-space: inherit;
  font-size: inherit;
  font-weight: inherit;
}

:where(.address) {
  font-style: inherit;
}
:where(.__wab_expr_html_text address) {
  white-space: inherit;
  font-style: inherit;
}

:where(.a) {
  color: inherit;
}
:where(.__wab_expr_html_text a) {
  white-space: inherit;
  color: inherit;
}

:where(.ol) {
  list-style-type: none;
  padding: 0;
}
:where(.__wab_expr_html_text ol) {
  white-space: inherit;
  list-style-type: none;
  padding: 0;
}

:where(.ul) {
  list-style-type: none;
  padding: 0;
}
:where(.__wab_expr_html_text ul) {
  white-space: inherit;
  list-style-type: none;
  padding: 0;
}

:where(.select) {
  padding: 2px 6px;
}
:where(.__wab_expr_html_text select) {
  white-space: inherit;
  padding: 2px 6px;
}

.plasmic_default__component_wrapper {
  display: grid;
}
.plasmic_default__inline {
  display: inline;
}
.plasmic_page_wrapper {
  display: flex;
  width: 100%;
  min-height: 100vh;
  align-items: stretch;
  align-self: start;
}
.plasmic_page_wrapper > * {
  height: auto !important;
}
.__wab_expr_html_text {
  white-space: normal;
}
:where(.root_reset) {
  font-family: var(--mixin-DVHTMUeZxkiW_font-family);
  font-size: var(--mixin-DVHTMUeZxkiW_font-size);
  font-weight: var(--mixin-DVHTMUeZxkiW_font-weight);
  font-style: var(--mixin-DVHTMUeZxkiW_font-style);
  color: var(--mixin-DVHTMUeZxkiW_color);
  text-align: var(--mixin-DVHTMUeZxkiW_text-align);
  text-transform: var(--mixin-DVHTMUeZxkiW_text-transform);
  line-height: var(--mixin-DVHTMUeZxkiW_line-height);
  letter-spacing: var(--mixin-DVHTMUeZxkiW_letter-spacing);
  white-space: var(--mixin-DVHTMUeZxkiW_white-space);
}

:where(.root_reset) h1:where(.h1),
h1:where(.root_reset.h1),
:where(.root_reset .__wab_expr_html_text) h1,
:where(.root_reset_tags) h1,
h1:where(.root_reset_tags) {
  color: var(--mixin-FLl2gpVKHWYx_color);
  font-weight: var(--mixin-FLl2gpVKHWYx_font-weight);
  font-size: var(--mixin-FLl2gpVKHWYx_font-size);
  line-height: var(--mixin-FLl2gpVKHWYx_line-height);
  letter-spacing: var(--mixin-FLl2gpVKHWYx_letter-spacing);
}

:where(.root_reset) h2:where(.h2),
h2:where(.root_reset.h2),
:where(.root_reset .__wab_expr_html_text) h2,
:where(.root_reset_tags) h2,
h2:where(.root_reset_tags) {
  color: var(--mixin-oAZmz7rKnGKZ_color);
  font-size: var(--mixin-oAZmz7rKnGKZ_font-size);
  font-weight: var(--mixin-oAZmz7rKnGKZ_font-weight);
  letter-spacing: var(--mixin-oAZmz7rKnGKZ_letter-spacing);
  line-height: var(--mixin-oAZmz7rKnGKZ_line-height);
}

:where(.root_reset) a:where(.a),
a:where(.root_reset.a),
:where(.root_reset .__wab_expr_html_text) a,
:where(.root_reset_tags) a,
a:where(.root_reset_tags) {
  color: var(--mixin-y_TCs12AGhMj_color);
}

:where(.root_reset) h3:where(.h3),
h3:where(.root_reset.h3),
:where(.root_reset .__wab_expr_html_text) h3,
:where(.root_reset_tags) h3,
h3:where(.root_reset_tags) {
  color: var(--mixin-5Qz0KztT6G5s_color);
  font-size: var(--mixin-5Qz0KztT6G5s_font-size);
  font-weight: var(--mixin-5Qz0KztT6G5s_font-weight);
  line-height: var(--mixin-5Qz0KztT6G5s_line-height);
}

:where(.root_reset) h4:where(.h4),
h4:where(.root_reset.h4),
:where(.root_reset .__wab_expr_html_text) h4,
:where(.root_reset_tags) h4,
h4:where(.root_reset_tags) {
  color: var(--mixin-HtpouCmKQlY9_color);
  font-size: var(--mixin-HtpouCmKQlY9_font-size);
  font-weight: var(--mixin-HtpouCmKQlY9_font-weight);
  line-height: var(--mixin-HtpouCmKQlY9_line-height);
}

:where(.root_reset) code:where(.code),
code:where(.root_reset.code),
:where(.root_reset .__wab_expr_html_text) code,
:where(.root_reset_tags) code,
code:where(.root_reset_tags) {
  background: #f8f8f8;
  font-family: var(--mixin-ajoy1VbZh3yG_font-family);
  border-radius: var(--mixin-ajoy1VbZh3yG_border-top-left-radius)
    var(--mixin-ajoy1VbZh3yG_border-top-right-radius)
    var(--mixin-ajoy1VbZh3yG_border-bottom-right-radius)
    var(--mixin-ajoy1VbZh3yG_border-bottom-left-radius);
  padding: var(--mixin-ajoy1VbZh3yG_padding-top)
    var(--mixin-ajoy1VbZh3yG_padding-right)
    var(--mixin-ajoy1VbZh3yG_padding-bottom)
    var(--mixin-ajoy1VbZh3yG_padding-left);
  border-top: var(--mixin-ajoy1VbZh3yG_border-top-width)
    var(--mixin-ajoy1VbZh3yG_border-top-style)
    var(--mixin-ajoy1VbZh3yG_border-top-color);
  border-right: var(--mixin-ajoy1VbZh3yG_border-right-width)
    var(--mixin-ajoy1VbZh3yG_border-right-style)
    var(--mixin-ajoy1VbZh3yG_border-right-color);
  border-bottom: var(--mixin-ajoy1VbZh3yG_border-bottom-width)
    var(--mixin-ajoy1VbZh3yG_border-bottom-style)
    var(--mixin-ajoy1VbZh3yG_border-bottom-color);
  border-left: var(--mixin-ajoy1VbZh3yG_border-left-width)
    var(--mixin-ajoy1VbZh3yG_border-left-style)
    var(--mixin-ajoy1VbZh3yG_border-left-color);
}

:where(.root_reset) blockquote:where(.blockquote),
blockquote:where(.root_reset.blockquote),
:where(.root_reset .__wab_expr_html_text) blockquote,
:where(.root_reset_tags) blockquote,
blockquote:where(.root_reset_tags) {
  color: var(--mixin-Jp9lEKdKByZH_color);
  padding-left: var(--mixin-Jp9lEKdKByZH_padding-left);
  border-left: var(--mixin-Jp9lEKdKByZH_border-left-width)
    var(--mixin-Jp9lEKdKByZH_border-left-style)
    var(--mixin-Jp9lEKdKByZH_border-left-color);
}

:where(.root_reset) pre:where(.pre),
pre:where(.root_reset.pre),
:where(.root_reset .__wab_expr_html_text) pre,
:where(.root_reset_tags) pre,
pre:where(.root_reset_tags) {
  background: #f8f8f8;
  font-family: var(--mixin--QOO7Vq1aTFi_font-family);
  border-radius: var(--mixin--QOO7Vq1aTFi_border-top-left-radius)
    var(--mixin--QOO7Vq1aTFi_border-top-right-radius)
    var(--mixin--QOO7Vq1aTFi_border-bottom-right-radius)
    var(--mixin--QOO7Vq1aTFi_border-bottom-left-radius);
  padding: var(--mixin--QOO7Vq1aTFi_padding-top)
    var(--mixin--QOO7Vq1aTFi_padding-right)
    var(--mixin--QOO7Vq1aTFi_padding-bottom)
    var(--mixin--QOO7Vq1aTFi_padding-left);
  border-top: var(--mixin--QOO7Vq1aTFi_border-top-width)
    var(--mixin--QOO7Vq1aTFi_border-top-style)
    var(--mixin--QOO7Vq1aTFi_border-top-color);
  border-right: var(--mixin--QOO7Vq1aTFi_border-right-width)
    var(--mixin--QOO7Vq1aTFi_border-right-style)
    var(--mixin--QOO7Vq1aTFi_border-right-color);
  border-bottom: var(--mixin--QOO7Vq1aTFi_border-bottom-width)
    var(--mixin--QOO7Vq1aTFi_border-bottom-style)
    var(--mixin--QOO7Vq1aTFi_border-bottom-color);
  border-left: var(--mixin--QOO7Vq1aTFi_border-left-width)
    var(--mixin--QOO7Vq1aTFi_border-left-style)
    var(--mixin--QOO7Vq1aTFi_border-left-color);
}

:where(.root_reset) ul:where(.ul),
ul:where(.root_reset.ul),
:where(.root_reset .__wab_expr_html_text) ul,
:where(.root_reset_tags) ul,
ul:where(.root_reset_tags) {
  display: var(--mixin-KqCgI7MzKvg3_display);
  flex-direction: var(--mixin-KqCgI7MzKvg3_flex-direction);
  align-items: var(--mixin-KqCgI7MzKvg3_align-items);
  justify-content: var(--mixin-KqCgI7MzKvg3_justify-content);
  list-style-position: var(--mixin-KqCgI7MzKvg3_list-style-position);
  padding-left: var(--mixin-KqCgI7MzKvg3_padding-left);
  position: var(--mixin-KqCgI7MzKvg3_position);
  list-style-type: var(--mixin-KqCgI7MzKvg3_list-style-type);
  column-gap: var(--mixin-KqCgI7MzKvg3_column-gap);
}

:where(.root_reset) ol:where(.ol),
ol:where(.root_reset.ol),
:where(.root_reset .__wab_expr_html_text) ol,
:where(.root_reset_tags) ol,
ol:where(.root_reset_tags) {
  display: var(--mixin-ru-4-vD6C5-4_display);
  flex-direction: var(--mixin-ru-4-vD6C5-4_flex-direction);
  align-items: var(--mixin-ru-4-vD6C5-4_align-items);
  justify-content: var(--mixin-ru-4-vD6C5-4_justify-content);
  list-style-position: var(--mixin-ru-4-vD6C5-4_list-style-position);
  padding-left: var(--mixin-ru-4-vD6C5-4_padding-left);
  position: var(--mixin-ru-4-vD6C5-4_position);
  list-style-type: var(--mixin-ru-4-vD6C5-4_list-style-type);
  column-gap: var(--mixin-ru-4-vD6C5-4_column-gap);
}

:where(.root_reset) h5:where(.h5),
h5:where(.root_reset.h5),
:where(.root_reset .__wab_expr_html_text) h5,
:where(.root_reset_tags) h5,
h5:where(.root_reset_tags) {
  color: var(--mixin-7jCLY220ogpb_color);
  font-size: var(--mixin-7jCLY220ogpb_font-size);
  font-weight: var(--mixin-7jCLY220ogpb_font-weight);
  line-height: var(--mixin-7jCLY220ogpb_line-height);
}

:where(.root_reset) h6:where(.h6),
h6:where(.root_reset.h6),
:where(.root_reset .__wab_expr_html_text) h6,
:where(.root_reset_tags) h6,
h6:where(.root_reset_tags) {
  color: var(--mixin-Z0_TyNPmzwDF_color);
  font-size: var(--mixin-Z0_TyNPmzwDF_font-size);
  font-weight: var(--mixin-Z0_TyNPmzwDF_font-weight);
  line-height: var(--mixin-Z0_TyNPmzwDF_line-height);
}

:where(.root_reset) a:where(.a):hover,
a:where(.root_reset.a):hover,
:where(.root_reset .__wab_expr_html_text) a:hover,
:where(.root_reset_tags) a:hover,
a:where(.root_reset_tags):hover {
  color: var(--mixin-rR9HkFETU-13_color);
}

:where(.root_reset) li:where(.li),
li:where(.root_reset.li),
:where(.root_reset .__wab_expr_html_text) li,
:where(.root_reset_tags) li,
li:where(.root_reset_tags) {
}

:where(.root_reset) p:where(.p),
p:where(.root_reset.p),
:where(.root_reset .__wab_expr_html_text) p,
:where(.root_reset_tags) p,
p:where(.root_reset_tags) {
}

:where(.root_reset) em:where(.em),
em:where(.root_reset.em),
:where(.root_reset .__wab_expr_html_text) em,
:where(.root_reset_tags) em,
em:where(.root_reset_tags) {
}
