/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 3wep4sj90mYo

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentButton from "../../SubcomponentButton"; // plasmic-import: ezhuRZvm_fH9/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicMessagesMessageHeader.module.css"; // plasmic-import: 3wep4sj90mYo/css

createPlasmicElementProxy;

export type PlasmicMessagesMessageHeader__VariantMembers = {};
export type PlasmicMessagesMessageHeader__VariantsArgs = {};
type VariantPropType = keyof PlasmicMessagesMessageHeader__VariantsArgs;
export const PlasmicMessagesMessageHeader__VariantProps =
  new Array<VariantPropType>();

export type PlasmicMessagesMessageHeader__ArgsType = {};
type ArgPropType = keyof PlasmicMessagesMessageHeader__ArgsType;
export const PlasmicMessagesMessageHeader__ArgProps = new Array<ArgPropType>();

export type PlasmicMessagesMessageHeader__OverridesType = {
  messagesBanner?: Flex__<"div">;
  messageBannerProfilePhoto?: Flex__<typeof PlasmicImg__>;
  freeBox?: Flex__<"div">;
  text?: Flex__<"div">;
  viewProfileButton?: Flex__<typeof SubcomponentButton>;
};

export interface DefaultMessagesMessageHeaderProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicMessagesMessageHeader__RenderFunc(props: {
  variants: PlasmicMessagesMessageHeader__VariantsArgs;
  args: PlasmicMessagesMessageHeader__ArgsType;
  overrides: PlasmicMessagesMessageHeader__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"messagesBanner"}
      data-plasmic-override={overrides.messagesBanner}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.messagesBanner
      )}
    >
      <PlasmicImg__
        data-plasmic-name={"messageBannerProfilePhoto"}
        data-plasmic-override={overrides.messageBannerProfilePhoto}
        alt={""}
        className={classNames(sty.messageBannerProfilePhoto)}
        displayHeight={"55px"}
        displayMaxHeight={"none"}
        displayMaxWidth={"100%"}
        displayMinHeight={"0"}
        displayMinWidth={"0"}
        displayWidth={"55px"}
        loading={"lazy"}
        src={{
          src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
          fullWidth: 800,
          fullHeight: 600,
          aspectRatio: undefined
        }}
      />

      <div
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        className={classNames(projectcss.all, sty.freeBox)}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text
          )}
        >
          {"Firstname, Lastname"}
        </div>
      </div>
      <SubcomponentButton
        data-plasmic-name={"viewProfileButton"}
        data-plasmic-override={overrides.viewProfileButton}
        className={classNames("__wab_instance", sty.viewProfileButton)}
        endIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__smfum)}
            role={"img"}
          />
        }
        size={"compact"}
        startIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__flJa0)}
            role={"img"}
          />
        }
      >
        {"View Profile"}
      </SubcomponentButton>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  messagesBanner: [
    "messagesBanner",
    "messageBannerProfilePhoto",
    "freeBox",
    "text",
    "viewProfileButton"
  ],
  messageBannerProfilePhoto: ["messageBannerProfilePhoto"],
  freeBox: ["freeBox", "text"],
  text: ["text"],
  viewProfileButton: ["viewProfileButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  messagesBanner: "div";
  messageBannerProfilePhoto: typeof PlasmicImg__;
  freeBox: "div";
  text: "div";
  viewProfileButton: typeof SubcomponentButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicMessagesMessageHeader__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicMessagesMessageHeader__VariantsArgs;
    args?: PlasmicMessagesMessageHeader__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicMessagesMessageHeader__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicMessagesMessageHeader__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicMessagesMessageHeader__ArgProps,
          internalVariantPropNames: PlasmicMessagesMessageHeader__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicMessagesMessageHeader__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "messagesBanner") {
    func.displayName = "PlasmicMessagesMessageHeader";
  } else {
    func.displayName = `PlasmicMessagesMessageHeader.${nodeName}`;
  }
  return func;
}

export const PlasmicMessagesMessageHeader = Object.assign(
  // Top-level PlasmicMessagesMessageHeader renders the root element
  makeNodeComponent("messagesBanner"),
  {
    // Helper components rendering sub-elements
    messageBannerProfilePhoto: makeNodeComponent("messageBannerProfilePhoto"),
    freeBox: makeNodeComponent("freeBox"),
    text: makeNodeComponent("text"),
    viewProfileButton: makeNodeComponent("viewProfileButton"),

    // Metadata about props expected for PlasmicMessagesMessageHeader
    internalVariantProps: PlasmicMessagesMessageHeader__VariantProps,
    internalArgProps: PlasmicMessagesMessageHeader__ArgProps
  }
);

export default PlasmicMessagesMessageHeader;
/* prettier-ignore-end */
