.caseStudyFormatting {
  box-shadow: 0px 2px 20px -2px #8b8b8b33;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: center;
  align-items: center;
  transition-property: all;
  transition-duration: 0.25s;
  background: var(--token-1AMvw6c2eIK7);
  justify-self: flex-start;
  min-width: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.25s;
  padding: var(--token-VuPwbNNk9FIa);
  border: 0.01cm none #e4e4e7;
}
.caseStudyFormattinghoverAndClick_hover {
  transform: scaleX(1.02) scaleY(1.015) scaleZ(1);
  background: var(--token-5_Q90hFZ9CmK);
}
.caseStudyFormattinghoverAndClick_click {
  transform: scaleX(1.015) scaleY(1.01) scaleZ(1);
  background: var(--token-zdHH9r9UDmmt);
}
.caseStudyFormattingselected {
  background: var(--token-zdHH9r9UDmmt);
}
.caseStudyFormattingsize_small {
  width: 150px;
  height: 100px;
  padding: var(--token-ptnlAHOp9Vq0);
}
.caseStudyFormattingsize_medium {
  width: 200px;
  height: 200px;
  background: var(--token-xo_r2w5pebq-);
  padding: var(--token-4Wrp9mDZCSCQ);
}
.caseStudyFormattingsize_large {
  width: 300px;
  height: 300px;
  padding: var(--token-j0qnbpah5w9U);
}
.caseStudyFormattingcolor_gray {
  background: var(--token-Ab1KZcxm-kp_);
}
.caseStudyFormattingcolor_green {
  background: var(--token-2vSHtOO_pyS1);
}
.caseStudyFormattingcompletionStatus_complete {
  background: var(--token-zdHH9r9UDmmt);
}
.caseStudyFormattingcompletionStatus_incomplete {
  background: none;
}
.checkbox {
  position: absolute;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: auto;
  height: auto;
  max-width: 100%;
  right: 0px;
  background: var(--token-_gt8keH_-0aP);
  top: 10px;
  box-shadow: 0px 2px 2px 0px var(--token-p09LDPmbF81_);
  column-gap: 2px;
  display: none;
  padding: 0px var(--token-0GsCPVoopKtO) 0px 4px;
  margin: var(--token-sazGmnf7GWAk);
}
.checkboxcompletionStatus_complete {
  display: flex;
}
.checkboxcompletionStatus_incomplete {
  background: var(--token-to1fT9g-t4Js);
  display: flex;
}
.text {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  color: var(--token-xo_r2w5pebq-);
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-agfFfkxgxH6d);
  padding-bottom: 1px;
  min-width: 0;
}
.checkmark2 {
  object-fit: cover;
  max-width: 100%;
  height: 1em;
  width: auto;
  color: var(--token-xo_r2w5pebq-);
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  max-width: 100%;
  row-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  min-height: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
