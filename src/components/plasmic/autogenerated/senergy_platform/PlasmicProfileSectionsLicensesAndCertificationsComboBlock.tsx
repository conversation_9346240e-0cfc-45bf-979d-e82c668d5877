/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: f3EcElmDdrge

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileLicenseTile from "../../ProfileTileLicenseTile"; // plasmic-import: NlwHt2k50uIY/component
import ProfileTileCertificationTile from "../../ProfileTileCertificationTile"; // plasmic-import: ld24XSt22oQ7/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsLicensesAndCertificationsComboBlock.module.css"; // plasmic-import: f3EcElmDdrge/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsLicensesAndCertificationsComboBlock__VariantMembers =
  {
    overviewGrid: "overviewGrid";
    editable: "editable";
  };
export type PlasmicProfileSectionsLicensesAndCertificationsComboBlock__VariantsArgs =
  {
    overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
    editable?: SingleBooleanChoiceArg<"editable">;
  };
type VariantPropType =
  keyof PlasmicProfileSectionsLicensesAndCertificationsComboBlock__VariantsArgs;
export const PlasmicProfileSectionsLicensesAndCertificationsComboBlock__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileSectionsLicensesAndCertificationsComboBlock__ArgsType =
  {
    allLicenses?: any;
    allCertifications?: any;
    onAllLicensesChange?: (val: string) => void;
    onAllCertificationsChange?: (val: string) => void;
    addButtonExpandType?: string;
    addButtonOnClick?: (event: any) => void;
    addButtonLicenseOnClick?: (event: any) => void;
    addButtonCertificationOnClick?: (event: any) => void;
    addButtonPatentOnClick?: (event: any) => void;
    addButtonTrademarkOnClick?: (event: any) => void;
  };
type ArgPropType =
  keyof PlasmicProfileSectionsLicensesAndCertificationsComboBlock__ArgsType;
export const PlasmicProfileSectionsLicensesAndCertificationsComboBlock__ArgProps =
  new Array<ArgPropType>(
    "allLicenses",
    "allCertifications",
    "onAllLicensesChange",
    "onAllCertificationsChange",
    "addButtonExpandType",
    "addButtonOnClick",
    "addButtonLicenseOnClick",
    "addButtonCertificationOnClick",
    "addButtonPatentOnClick",
    "addButtonTrademarkOnClick"
  );

export type PlasmicProfileSectionsLicensesAndCertificationsComboBlock__OverridesType =
  {
    licensesAndCertificationsSection?: Flex__<"div">;
    profileSectionsProfileSectionHeading?: Flex__<
      typeof ProfileSectionsProfileSectionHeading
    >;
    displayContainer?: Flex__<"section">;
    tLicenseTile?: Flex__<typeof ProfileTileLicenseTile>;
    tCertificationTile?: Flex__<typeof ProfileTileCertificationTile>;
  };

export interface DefaultProfileSectionsLicensesAndCertificationsComboBlockProps {
  allLicenses?: any;
  allCertifications?: any;
  onAllLicensesChange?: (val: string) => void;
  onAllCertificationsChange?: (val: string) => void;
  addButtonExpandType?: string;
  addButtonOnClick?: (event: any) => void;
  addButtonLicenseOnClick?: (event: any) => void;
  addButtonCertificationOnClick?: (event: any) => void;
  addButtonPatentOnClick?: (event: any) => void;
  addButtonTrademarkOnClick?: (event: any) => void;
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsLicensesAndCertificationsComboBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsLicensesAndCertificationsComboBlock__VariantsArgs;
  args: PlasmicProfileSectionsLicensesAndCertificationsComboBlock__ArgsType;
  overrides: PlasmicProfileSectionsLicensesAndCertificationsComboBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "tLicenseTile[].titleInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].issueDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].registrationNumberInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].instructorInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].issueLocationInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].expirationDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].licensingBodyInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].deleteButtonClickStage",
        type: "private",
        variableType: "number"
      },
      {
        path: "tLicenseTile[].deleteButtonDisabled",
        type: "private",
        variableType: "text"
      },
      {
        path: "tCertificationTile[].titleInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tCertificationTile[].completionDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tCertificationTile[].registrationNumberInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tCertificationTile[].instructorInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tCertificationTile[].locationOfAwardInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tCertificationTile[].expirationDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tCertificationTile[].certifyingBodyInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tCertificationTile[].deleteButtonClickStage",
        type: "private",
        variableType: "number"
      },
      {
        path: "tCertificationTile[].deleteButtonDisabled",
        type: "private",
        variableType: "text"
      },
      {
        path: "tCertificationTile[].descriptionInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tLicenseTile[].descriptionInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "allLicenses",
        type: "writable",
        variableType: "object",

        valueProp: "allLicenses",
        onChangeProp: "onAllLicensesChange"
      },
      {
        path: "allCertifications",
        type: "writable",
        variableType: "object",

        valueProp: "allCertifications",
        onChangeProp: "onAllCertificationsChange"
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"licensesAndCertificationsSection"}
      data-plasmic-override={overrides.licensesAndCertificationsSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.licensesAndCertificationsSection
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        addButtonBaseOnClick={args.addButtonOnClick}
        addButtonCertificationOnClick={args.addButtonCertificationOnClick}
        addButtonLicenseOnClick={args.addButtonLicenseOnClick}
        addButtonPatentOnClick={args.addButtonPatentOnClick}
        addButtonSectionType={
          hasVariant($state, "editable", "editable")
            ? "licenseorcertification"
            : undefined
        }
        addButtonTrademarkOnClick={args.addButtonTrademarkOnClick}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewGrid]: hasVariant(
              $state,
              "overviewGrid",
              "overviewGrid"
            )
          }
        )}
        editable={
          hasVariant($state, "editable", "editable")
            ? true
            : (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })()
        }
        overviewGrid={
          hasVariant($state, "overviewGrid", "overviewGrid") ? true : undefined
        }
      >
        {"Licenses and Certifications"}
      </ProfileSectionsProfileSectionHeading>
      <section
        data-plasmic-name={"displayContainer"}
        data-plasmic-override={overrides.displayContainer}
        className={classNames(projectcss.all, sty.displayContainer, {
          [sty.displayContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
          (() => {
            try {
              return $state.allLicenses.data;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()
        ).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (() => {
            const child$Props = {
              className: classNames("__wab_instance", sty.tLicenseTile, {
                [sty.tLicenseTileoverviewGrid]: hasVariant(
                  $state,
                  "overviewGrid",
                  "overviewGrid"
                )
              }),
              deleteButtonClickStage: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "deleteButtonClickStage"
              ]),
              deleteButtonDisabled: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "deleteButtonDisabled"
              ]),
              descriptionInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "descriptionInputValue"
              ]),
              editable: (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })(),
              expirationDateInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "expirationDateInputValue"
              ]),
              instructorInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "instructorInputValue"
              ]),
              issueDateInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "issueDateInputValue"
              ]),
              issueLocationInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "issueLocationInputValue"
              ]),
              key: currentIndex,
              licenseId: (() => {
                try {
                  return currentItem.id;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })(),
              licensingBodyInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "licensingBodyInputValue"
              ]),
              onDeleteButtonClickStageChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "deleteButtonClickStage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonDisabledChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "deleteButtonDisabled"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDescriptionInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "descriptionInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onExpirationDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "expirationDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onInstructorInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "instructorInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onIssueDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "issueDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onIssueLocationInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "issueLocationInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onLicensingBodyInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "licensingBodyInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onRegistrationNumberInputValueChange: async (
                ...eventArgs: any
              ) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "registrationNumberInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onTitleInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tLicenseTile",
                  __plasmic_idx_0,
                  "titleInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              overview: hasVariant($state, "overviewGrid", "overviewGrid")
                ? true
                : undefined,
              registrationNumberInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "registrationNumberInputValue"
              ]),
              titleInputValue: generateStateValueProp($state, [
                "tLicenseTile",
                __plasmic_idx_0,
                "titleInputValue"
              ])
            };

            initializePlasmicStates(
              $state,
              [
                {
                  name: "tLicenseTile[].titleInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.title;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].issueDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.issue_date
                          ? currentItem.issue_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].registrationNumberInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.registration_number;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].instructorInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.instructor;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].issueLocationInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.issue_location;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].expirationDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.expiration_date
                          ? currentItem.expiration_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].licensingBodyInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.licensing_body;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tLicenseTile[].deleteButtonClickStage",
                  initFunc: ({ $props, $state, $queries }) => 0
                },
                {
                  name: "tLicenseTile[].deleteButtonDisabled",
                  initFunc: ({ $props, $state, $queries }) => undefined
                },
                {
                  name: "tLicenseTile[].descriptionInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.additional_info;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                }
              ],
              [__plasmic_idx_0]
            );
            return (
              <ProfileTileLicenseTile
                data-plasmic-name={"tLicenseTile"}
                data-plasmic-override={overrides.tLicenseTile}
                {...child$Props}
              />
            );
          })();
        })}
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
          (() => {
            try {
              return $state.allCertifications.data;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()
        ).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (() => {
            const child$Props = {
              certificationId: (() => {
                try {
                  return currentItem.id;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })(),
              certifyingBodyInputValue: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "certifyingBodyInputValue"
              ]),
              className: classNames("__wab_instance", sty.tCertificationTile, {
                [sty.tCertificationTileoverviewGrid]: hasVariant(
                  $state,
                  "overviewGrid",
                  "overviewGrid"
                )
              }),
              completionDateInputValue: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "completionDateInputValue"
              ]),
              deleteButtonClickStage: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "deleteButtonClickStage"
              ]),
              deleteButtonDisabled: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "deleteButtonDisabled"
              ]),
              descriptionInputValue: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "descriptionInputValue"
              ]),
              editable: (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })(),
              expirationDateInputValue: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "expirationDateInputValue"
              ]),
              instructorInputValue: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "instructorInputValue"
              ]),
              key: currentIndex,
              locationOfAwardInputValue: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "locationOfAwardInputValue"
              ]),
              onCertifyingBodyInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "certifyingBodyInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onCompletionDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "completionDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonClickStageChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "deleteButtonClickStage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonDisabledChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "deleteButtonDisabled"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDescriptionInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "descriptionInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onExpirationDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "expirationDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onInstructorInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "instructorInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onLocationOfAwardInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "locationOfAwardInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onRegistrationNumberInputValueChange: async (
                ...eventArgs: any
              ) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "registrationNumberInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onTitleInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tCertificationTile",
                  __plasmic_idx_0,
                  "titleInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              overview: hasVariant($state, "overviewGrid", "overviewGrid")
                ? true
                : undefined,
              registrationNumberInputValue: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "registrationNumberInputValue"
              ]),
              titleInputValue: generateStateValueProp($state, [
                "tCertificationTile",
                __plasmic_idx_0,
                "titleInputValue"
              ])
            };

            initializePlasmicStates(
              $state,
              [
                {
                  name: "tCertificationTile[].titleInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.title;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tCertificationTile[].completionDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.completion_date
                          ? currentItem.completion_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tCertificationTile[].registrationNumberInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.registration_number;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tCertificationTile[].instructorInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.instructor;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tCertificationTile[].locationOfAwardInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.award_location;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tCertificationTile[].expirationDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.expiration_date
                          ? currentItem.expiration_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tCertificationTile[].certifyingBodyInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.certifying_body;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tCertificationTile[].deleteButtonClickStage",
                  initFunc: ({ $props, $state, $queries }) => 0
                },
                {
                  name: "tCertificationTile[].deleteButtonDisabled",
                  initFunc: ({ $props, $state, $queries }) => undefined
                },
                {
                  name: "tCertificationTile[].descriptionInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.additional_info;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                }
              ],
              [__plasmic_idx_0]
            );
            return (
              <ProfileTileCertificationTile
                data-plasmic-name={"tCertificationTile"}
                data-plasmic-override={overrides.tCertificationTile}
                {...child$Props}
              />
            );
          })();
        })}
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  licensesAndCertificationsSection: [
    "licensesAndCertificationsSection",
    "profileSectionsProfileSectionHeading",
    "displayContainer",
    "tLicenseTile",
    "tCertificationTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading"
  ],
  displayContainer: ["displayContainer", "tLicenseTile", "tCertificationTile"],
  tLicenseTile: ["tLicenseTile"],
  tCertificationTile: ["tCertificationTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  licensesAndCertificationsSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  displayContainer: "section";
  tLicenseTile: typeof ProfileTileLicenseTile;
  tCertificationTile: typeof ProfileTileCertificationTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsLicensesAndCertificationsComboBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsLicensesAndCertificationsComboBlock__VariantsArgs;
    args?: PlasmicProfileSectionsLicensesAndCertificationsComboBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsLicensesAndCertificationsComboBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsLicensesAndCertificationsComboBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicProfileSectionsLicensesAndCertificationsComboBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsLicensesAndCertificationsComboBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsLicensesAndCertificationsComboBlock__RenderFunc(
      { variants, args, overrides, forNode: nodeName }
    );
  };
  if (nodeName === "licensesAndCertificationsSection") {
    func.displayName =
      "PlasmicProfileSectionsLicensesAndCertificationsComboBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsLicensesAndCertificationsComboBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsLicensesAndCertificationsComboBlock =
  Object.assign(
    // Top-level PlasmicProfileSectionsLicensesAndCertificationsComboBlock renders the root element
    makeNodeComponent("licensesAndCertificationsSection"),
    {
      // Helper components rendering sub-elements
      profileSectionsProfileSectionHeading: makeNodeComponent(
        "profileSectionsProfileSectionHeading"
      ),
      displayContainer: makeNodeComponent("displayContainer"),
      tLicenseTile: makeNodeComponent("tLicenseTile"),
      tCertificationTile: makeNodeComponent("tCertificationTile"),

      // Metadata about props expected for PlasmicProfileSectionsLicensesAndCertificationsComboBlock
      internalVariantProps:
        PlasmicProfileSectionsLicensesAndCertificationsComboBlock__VariantProps,
      internalArgProps:
        PlasmicProfileSectionsLicensesAndCertificationsComboBlock__ArgProps
    }
  );

export default PlasmicProfileSectionsLicensesAndCertificationsComboBlock;
/* prettier-ignore-end */
