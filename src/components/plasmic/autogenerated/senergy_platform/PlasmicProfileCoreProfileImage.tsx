/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: FmLLnhtvs10f

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileCoreProfileImage.module.css"; // plasmic-import: FmLLnhtvs10f/css

createPlasmicElementProxy;

export type PlasmicProfileCoreProfileImage__VariantMembers = {
  mediaType: "audio" | "video" | "noMedia";
  hoverState: "hoverState";
};
export type PlasmicProfileCoreProfileImage__VariantsArgs = {
  mediaType?: SingleChoiceArg<"audio" | "video" | "noMedia">;
  hoverState?: SingleBooleanChoiceArg<"hoverState">;
};
type VariantPropType = keyof PlasmicProfileCoreProfileImage__VariantsArgs;
export const PlasmicProfileCoreProfileImage__VariantProps =
  new Array<VariantPropType>("mediaType", "hoverState");

export type PlasmicProfileCoreProfileImage__ArgsType = {};
type ArgPropType = keyof PlasmicProfileCoreProfileImage__ArgsType;
export const PlasmicProfileCoreProfileImage__ArgProps =
  new Array<ArgPropType>();

export type PlasmicProfileCoreProfileImage__OverridesType = {
  profileImageCompContainer?: Flex__<"div">;
  overlayOverflowContainer?: Flex__<"section">;
  overlayColor?: Flex__<"section">;
  hoverOverlay?: Flex__<"section">;
  img?: Flex__<typeof PlasmicImg__>;
  mediaTypeIcon?: Flex__<"svg">;
  audioPlayerPopout?: Flex__<"section">;
  audioPlayerContainer?: Flex__<"section">;
  videoPlayerPopout?: Flex__<"section">;
};

export interface DefaultProfileCoreProfileImageProps {
  mediaType?: SingleChoiceArg<"audio" | "video" | "noMedia">;
  hoverState?: SingleBooleanChoiceArg<"hoverState">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileCoreProfileImage__RenderFunc(props: {
  variants: PlasmicProfileCoreProfileImage__VariantsArgs;
  args: PlasmicProfileCoreProfileImage__ArgsType;
  overrides: PlasmicProfileCoreProfileImage__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "mediaType",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.mediaType
      },
      {
        path: "expandedPlayer",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.expandedPlayer
      },
      {
        path: "hoverState",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hoverState
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"profileImageCompContainer"}
      data-plasmic-override={overrides.profileImageCompContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.profileImageCompContainer,
        {
          [sty.profileImageCompContainermediaType_audio]: hasVariant(
            $state,
            "mediaType",
            "audio"
          ),
          [sty.profileImageCompContainermediaType_video]: hasVariant(
            $state,
            "mediaType",
            "video"
          )
        }
      )}
      onMouseLeave={async event => {
        const $steps = {};

        $steps["updateHoverState"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "hoverState",
                operation: 1,
                value: []
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, undefined);
                return undefined;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverState"] != null &&
          typeof $steps["updateHoverState"] === "object" &&
          typeof $steps["updateHoverState"].then === "function"
        ) {
          $steps["updateHoverState"] = await $steps["updateHoverState"];
        }
      }}
      onMouseOver={async event => {
        const $steps = {};

        $steps["updateHoverState"] = true
          ? (() => {
              const actionArgs = { vgroup: "hoverState", operation: 4 };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, true);
                return true;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverState"] != null &&
          typeof $steps["updateHoverState"] === "object" &&
          typeof $steps["updateHoverState"].then === "function"
        ) {
          $steps["updateHoverState"] = await $steps["updateHoverState"];
        }
      }}
    >
      <section
        data-plasmic-name={"overlayOverflowContainer"}
        data-plasmic-override={overrides.overlayOverflowContainer}
        className={classNames(projectcss.all, sty.overlayOverflowContainer, {
          [sty.overlayOverflowContainerhoverState]: hasVariant(
            $state,
            "hoverState",
            "hoverState"
          ),
          [sty.overlayOverflowContainermediaType_noMedia]: hasVariant(
            $state,
            "mediaType",
            "noMedia"
          )
        })}
      >
        <section
          data-plasmic-name={"overlayColor"}
          data-plasmic-override={overrides.overlayColor}
          className={classNames(projectcss.all, sty.overlayColor, {
            [sty.overlayColorhoverState]: hasVariant(
              $state,
              "hoverState",
              "hoverState"
            ),
            [sty.overlayColormediaType_video]: hasVariant(
              $state,
              "mediaType",
              "video"
            )
          })}
        />
      </section>
      <section
        data-plasmic-name={"hoverOverlay"}
        data-plasmic-override={overrides.hoverOverlay}
        className={classNames(projectcss.all, sty.hoverOverlay, {
          [sty.hoverOverlayhoverState]: hasVariant(
            $state,
            "hoverState",
            "hoverState"
          ),
          [sty.hoverOverlaymediaType_audio]: hasVariant(
            $state,
            "mediaType",
            "audio"
          ),
          [sty.hoverOverlaymediaType_noMedia]: hasVariant(
            $state,
            "mediaType",
            "noMedia"
          )
        })}
      />

      <PlasmicImg__
        data-plasmic-name={"img"}
        data-plasmic-override={overrides.img}
        alt={""}
        className={classNames(sty.img, {
          [sty.imghoverState]: hasVariant($state, "hoverState", "hoverState"),
          [sty.imgmediaType_audio]: hasVariant($state, "mediaType", "audio"),
          [sty.imgmediaType_noMedia]: hasVariant(
            $state,
            "mediaType",
            "noMedia"
          ),
          [sty.imgmediaType_video]: hasVariant($state, "mediaType", "video")
        })}
        displayHeight={"100px"}
        displayMaxHeight={"none"}
        displayMaxWidth={"100%"}
        displayMinHeight={"0"}
        displayMinWidth={"0"}
        displayWidth={"100px"}
        loading={"lazy"}
        src={{
          src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
          fullWidth: 800,
          fullHeight: 600,
          aspectRatio: undefined
        }}
      />

      <svg
        data-plasmic-name={"mediaTypeIcon"}
        data-plasmic-override={overrides.mediaTypeIcon}
        className={classNames(projectcss.all, sty.mediaTypeIcon, {
          [sty.mediaTypeIconhoverState]: hasVariant(
            $state,
            "hoverState",
            "hoverState"
          ),
          [sty.mediaTypeIconmediaType_audio]: hasVariant(
            $state,
            "mediaType",
            "audio"
          ),
          [sty.mediaTypeIconmediaType_noMedia]: hasVariant(
            $state,
            "mediaType",
            "noMedia"
          ),
          [sty.mediaTypeIconmediaType_noMedia_hoverState]:
            hasVariant($state, "hoverState", "hoverState") &&
            hasVariant($state, "mediaType", "noMedia"),
          [sty.mediaTypeIconmediaType_video]: hasVariant(
            $state,
            "mediaType",
            "video"
          )
        })}
        role={"img"}
      />

      <section
        data-plasmic-name={"audioPlayerPopout"}
        data-plasmic-override={overrides.audioPlayerPopout}
        className={classNames(projectcss.all, sty.audioPlayerPopout, {
          [sty.audioPlayerPopoutmediaType_audio]: hasVariant(
            $state,
            "mediaType",
            "audio"
          )
        })}
      >
        <section
          data-plasmic-name={"audioPlayerContainer"}
          data-plasmic-override={overrides.audioPlayerContainer}
          className={classNames(projectcss.all, sty.audioPlayerContainer)}
        />
      </section>
      <section
        data-plasmic-name={"videoPlayerPopout"}
        data-plasmic-override={overrides.videoPlayerPopout}
        className={classNames(projectcss.all, sty.videoPlayerPopout)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  profileImageCompContainer: [
    "profileImageCompContainer",
    "overlayOverflowContainer",
    "overlayColor",
    "hoverOverlay",
    "img",
    "mediaTypeIcon",
    "audioPlayerPopout",
    "audioPlayerContainer",
    "videoPlayerPopout"
  ],
  overlayOverflowContainer: ["overlayOverflowContainer", "overlayColor"],
  overlayColor: ["overlayColor"],
  hoverOverlay: ["hoverOverlay"],
  img: ["img"],
  mediaTypeIcon: ["mediaTypeIcon"],
  audioPlayerPopout: ["audioPlayerPopout", "audioPlayerContainer"],
  audioPlayerContainer: ["audioPlayerContainer"],
  videoPlayerPopout: ["videoPlayerPopout"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  profileImageCompContainer: "div";
  overlayOverflowContainer: "section";
  overlayColor: "section";
  hoverOverlay: "section";
  img: typeof PlasmicImg__;
  mediaTypeIcon: "svg";
  audioPlayerPopout: "section";
  audioPlayerContainer: "section";
  videoPlayerPopout: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileCoreProfileImage__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileCoreProfileImage__VariantsArgs;
    args?: PlasmicProfileCoreProfileImage__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileCoreProfileImage__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileCoreProfileImage__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileCoreProfileImage__ArgProps,
          internalVariantPropNames: PlasmicProfileCoreProfileImage__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileCoreProfileImage__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "profileImageCompContainer") {
    func.displayName = "PlasmicProfileCoreProfileImage";
  } else {
    func.displayName = `PlasmicProfileCoreProfileImage.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileCoreProfileImage = Object.assign(
  // Top-level PlasmicProfileCoreProfileImage renders the root element
  makeNodeComponent("profileImageCompContainer"),
  {
    // Helper components rendering sub-elements
    overlayOverflowContainer: makeNodeComponent("overlayOverflowContainer"),
    overlayColor: makeNodeComponent("overlayColor"),
    hoverOverlay: makeNodeComponent("hoverOverlay"),
    img: makeNodeComponent("img"),
    mediaTypeIcon: makeNodeComponent("mediaTypeIcon"),
    audioPlayerPopout: makeNodeComponent("audioPlayerPopout"),
    audioPlayerContainer: makeNodeComponent("audioPlayerContainer"),
    videoPlayerPopout: makeNodeComponent("videoPlayerPopout"),

    // Metadata about props expected for PlasmicProfileCoreProfileImage
    internalVariantProps: PlasmicProfileCoreProfileImage__VariantProps,
    internalArgProps: PlasmicProfileCoreProfileImage__ArgProps
  }
);

export default PlasmicProfileCoreProfileImage;
/* prettier-ignore-end */
