.overviewContentBlock {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: stretch;
  background: none;
  flex-wrap: wrap;
  align-content: flex-start;
  min-width: 0;
}
.overviewHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-top: 0px;
}
.text {
  font-size: var(--token-hKlnSDJVAhUx);
}
.featuredWorksArea {
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.profileOverviewHighlightedWorksLayout:global(.__wab_instance) {
  max-width: 100%;
}
.seeMoreButtonContainer2 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.profileOverviewSeeMoreButton__zMfR6:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.summaryArea {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: 0px var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk);
}
.personalSummaryHeading:global(.__wab_instance) {
  max-width: 100%;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
.introductionBlock:global(.__wab_instance) {
  max-width: 100%;
}
.overviewBlockGrid {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  padding: 0px var(--token-4Wrp9mDZCSCQ) var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
@media (max-width: 860px) {
  .overviewBlockGrid {
    display: flex;
    flex-direction: column;
    row-gap: var(--token-4Wrp9mDZCSCQ);
    column-gap: 0px;
  }
}
@media (max-width: 480px) {
  .overviewBlockGrid {
    display: flex;
    flex-direction: column;
    row-gap: var(--token-4Wrp9mDZCSCQ);
    column-gap: 0px;
  }
}
.skillsAndToolsStack {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  row-gap: var(--token-4Wrp9mDZCSCQ);
  padding: var(--token-sazGmnf7GWAk);
}
.skillsBlock:global(.__wab_instance) {
  max-width: 550px;
  width: 100%;
  min-width: 0;
}
.toolsBlock:global(.__wab_instance) {
  position: relative;
  max-width: 550px;
  width: 100%;
  min-width: 0;
}
.leftStackUserDefined {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  flex-wrap: wrap;
  align-content: flex-start;
  row-gap: var(--token-j0qnbpah5w9U);
  min-width: 0;
}
._2XWideBlock:global(.__wab_instance) {
  max-width: 100%;
  width: 100%;
  margin-bottom: 8px;
  min-width: 0;
}
.bottomBlocksSections {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
@media (max-width: 1200px) {
  .bottomBlocksSections {
    flex-wrap: nowrap;
    align-items: stretch;
    align-content: stretch;
  }
}
@media (max-width: 860px) {
  .bottomBlocksSections {
    display: flex;
    flex-direction: column;
    row-gap: var(--token-4Wrp9mDZCSCQ);
    column-gap: 0px;
  }
}
.rightBottomBlock:global(.__wab_instance) {
  position: relative;
}
.seeMoreButtonContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.profileOverviewSeeMoreButton__w8Sl6:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
