/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: C1Ttec365eGb

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicMessagesAttachments.module.css"; // plasmic-import: C1Ttec365eGb/css

import FileInfoIcon from "./icons/PlasmicIcon__FileInfo"; // plasmic-import: 8rjY-nsmb3pJ/icon
import DownloadIcon from "./icons/PlasmicIcon__Download"; // plasmic-import: GsLUL8ZP9xQL/icon

createPlasmicElementProxy;

export type PlasmicMessagesAttachments__VariantMembers = {
  image: "image";
};
export type PlasmicMessagesAttachments__VariantsArgs = {
  image?: SingleBooleanChoiceArg<"image">;
};
type VariantPropType = keyof PlasmicMessagesAttachments__VariantsArgs;
export const PlasmicMessagesAttachments__VariantProps =
  new Array<VariantPropType>("image");

export type PlasmicMessagesAttachments__ArgsType = {};
type ArgPropType = keyof PlasmicMessagesAttachments__ArgsType;
export const PlasmicMessagesAttachments__ArgProps = new Array<ArgPropType>();

export type PlasmicMessagesAttachments__OverridesType = {
  attachmentsContainer?: Flex__<"div">;
  filetypeIcon?: Flex__<"svg">;
  img?: Flex__<typeof PlasmicImg__>;
  fileInformation?: Flex__<"div">;
  downloadButtonPopUp?: Flex__<"section">;
  svg?: Flex__<"svg">;
};

export interface DefaultMessagesAttachmentsProps {
  image?: SingleBooleanChoiceArg<"image">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicMessagesAttachments__RenderFunc(props: {
  variants: PlasmicMessagesAttachments__VariantsArgs;
  args: PlasmicMessagesAttachments__ArgsType;
  overrides: PlasmicMessagesAttachments__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "image",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.image
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const [isAttachmentsContainerHover, triggerAttachmentsContainerHoverProps] =
    useTrigger("useHover", {});
  const triggers = {
    hover_attachmentsContainer: isAttachmentsContainerHover
  };

  return (
    <div
      data-plasmic-name={"attachmentsContainer"}
      data-plasmic-override={overrides.attachmentsContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.attachmentsContainer,
        {
          [sty.attachmentsContainerimage]: hasVariant($state, "image", "image")
        }
      )}
      data-plasmic-trigger-props={[triggerAttachmentsContainerHoverProps]}
    >
      <FileInfoIcon
        data-plasmic-name={"filetypeIcon"}
        data-plasmic-override={overrides.filetypeIcon}
        className={classNames(projectcss.all, sty.filetypeIcon, {
          [sty.filetypeIconimage]: hasVariant($state, "image", "image")
        })}
        role={"img"}
      />

      <PlasmicImg__
        data-plasmic-name={"img"}
        data-plasmic-override={overrides.img}
        alt={""}
        className={classNames(sty.img, {
          [sty.imgimage]: hasVariant($state, "image", "image")
        })}
        displayHeight={"auto"}
        displayMaxHeight={"350px"}
        displayMaxWidth={"350px"}
        displayMinHeight={"50px"}
        displayMinWidth={"50px"}
        displayWidth={"auto"}
        loading={"lazy"}
        src={{
          src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
          fullWidth: 800,
          fullHeight: 600,
          aspectRatio: undefined
        }}
      />

      <div
        data-plasmic-name={"fileInformation"}
        data-plasmic-override={overrides.fileInformation}
        className={classNames(projectcss.all, sty.fileInformation, {
          [sty.fileInformationimage]: hasVariant($state, "image", "image")
        })}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__ytpWw,
            {
              [sty.textimage__ytpWwqH9T6]: hasVariant($state, "image", "image")
            }
          )}
        >
          {"Filename and extension"}
        </div>
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text___944W5,
            {
              [sty.textimage___944W5QH9T6]: hasVariant($state, "image", "image")
            }
          )}
        >
          {triggers.hover_attachmentsContainer
            ? "Download filetype"
            : "filetype"}
        </div>
      </div>
      <section
        data-plasmic-name={"downloadButtonPopUp"}
        data-plasmic-override={overrides.downloadButtonPopUp}
        className={classNames(projectcss.all, sty.downloadButtonPopUp, {
          [sty.downloadButtonPopUpimage]: hasVariant($state, "image", "image")
        })}
      >
        <DownloadIcon
          data-plasmic-name={"svg"}
          data-plasmic-override={overrides.svg}
          className={classNames(projectcss.all, sty.svg, {
            [sty.svgimage]: hasVariant($state, "image", "image")
          })}
          role={"img"}
        />
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  attachmentsContainer: [
    "attachmentsContainer",
    "filetypeIcon",
    "img",
    "fileInformation",
    "downloadButtonPopUp",
    "svg"
  ],
  filetypeIcon: ["filetypeIcon"],
  img: ["img"],
  fileInformation: ["fileInformation"],
  downloadButtonPopUp: ["downloadButtonPopUp", "svg"],
  svg: ["svg"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  attachmentsContainer: "div";
  filetypeIcon: "svg";
  img: typeof PlasmicImg__;
  fileInformation: "div";
  downloadButtonPopUp: "section";
  svg: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicMessagesAttachments__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicMessagesAttachments__VariantsArgs;
    args?: PlasmicMessagesAttachments__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicMessagesAttachments__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicMessagesAttachments__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicMessagesAttachments__ArgProps,
          internalVariantPropNames: PlasmicMessagesAttachments__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicMessagesAttachments__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "attachmentsContainer") {
    func.displayName = "PlasmicMessagesAttachments";
  } else {
    func.displayName = `PlasmicMessagesAttachments.${nodeName}`;
  }
  return func;
}

export const PlasmicMessagesAttachments = Object.assign(
  // Top-level PlasmicMessagesAttachments renders the root element
  makeNodeComponent("attachmentsContainer"),
  {
    // Helper components rendering sub-elements
    filetypeIcon: makeNodeComponent("filetypeIcon"),
    img: makeNodeComponent("img"),
    fileInformation: makeNodeComponent("fileInformation"),
    downloadButtonPopUp: makeNodeComponent("downloadButtonPopUp"),
    svg: makeNodeComponent("svg"),

    // Metadata about props expected for PlasmicMessagesAttachments
    internalVariantProps: PlasmicMessagesAttachments__VariantProps,
    internalArgProps: PlasmicMessagesAttachments__ArgProps
  }
);

export default PlasmicMessagesAttachments;
/* prettier-ignore-end */
