.educationSpacingContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  flex-shrink: 0;
  height: auto;
  margin-bottom: 12px;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  margin-top: 4px;
  margin-left: 4px;
  row-gap: 0px;
  position: relative;
  min-width: 0;
  padding: 16px 16px 8px 0px;
}
.educationSpacingContaineroverview {
  width: auto;
  justify-self: flex-start;
  display: inline-flex;
}
.schoolIconDisplay {
  margin-top: 0px;
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: var(--token-ey2y7HI9U2zx);
  height: var(--token-ey2y7HI9U2zx);
  column-gap: 0px;
  position: relative;
  object-fit: cover;
  max-width: 100%;
  flex-shrink: 0;
}
.schoolIconDisplayoverview {
  display: none;
}
.inforationFormatting {
  display: flex;
  position: relative;
  flex-direction: column;
  padding-top: 0px;
  max-width: 100%;
  height: auto;
  width: 100%;
  justify-content: flex-start;
  align-items: flex-start;
  min-width: 0;
}
.titleContainer {
  display: flex;
  position: relative;
  flex-direction: column;
  max-width: 100%;
  height: auto;
  width: 100%;
  justify-content: flex-start;
  align-items: stretch;
  min-width: 0;
}
.degreeNameContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  margin-bottom: -6px;
  align-items: center;
  justify-content: flex-start;
  column-gap: var(--token-8HwyO4mnTWdf);
}
.degreeNameContainereditable {
  margin-bottom: 8px;
}
.subcomponentDropdownSelector:global(.__wab_instance) {
  margin-bottom: 12px;
}
.degreeNameInputAndFullDisplay:global(.__wab_instance) {
  max-width: 100%;
  margin-bottom: 2px;
}
.educationalInstitutionInput:global(.__wab_instance) {
  margin-bottom: 2px;
  max-width: 100%;
}
.locationInput:global(.__wab_instance) {
  position: relative;
  max-width: 100%;
  align-self: flex-start;
}
.locationInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot3 {
  position: relative;
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin-right: 0;
  max-width: 100%;
  min-width: 0;
  min-height: 0;
}
.eduInfoBar {
  display: flex;
  position: relative;
  flex-direction: row;
  margin-top: 8px;
  column-gap: var(--token-rB3BKCgczoWa);
}
.eduInfoBaroverview {
  display: flex;
  flex-direction: column;
  row-gap: var(--token-ptnlAHOp9Vq0);
  column-gap: 0px;
}
.graduationDatesDisplay:global(.__wab_instance) {
  position: relative;
}
.graduationDatesDisplayeditable:global(.__wab_instance) {
  display: none;
}
.iconSpot4 {
  position: relative;
  object-fit: cover;
  width: 100%;
  height: 100%;
  max-width: 100%;
  margin-right: 0;
  min-width: 0;
  min-height: 0;
}
.startDateInput:global(.__wab_instance) {
  position: relative;
  max-width: 100%;
  display: none;
}
.startDateInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot5 {
  position: relative;
  object-fit: cover;
  width: 100%;
  height: 100%;
  margin-right: 0;
  max-width: 100%;
  min-width: 0;
  min-height: 0;
}
.endDateInput:global(.__wab_instance) {
  position: relative;
  max-width: 100%;
  display: none;
}
.endDateInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot6 {
  position: relative;
  object-fit: cover;
  width: 100%;
  height: 100%;
  margin-right: 0;
  max-width: 100%;
  min-width: 0;
  min-height: 0;
}
.graduatedInput:global(.__wab_instance) {
  position: relative;
  max-width: 100%;
  display: none;
}
.graduatedInputeditable:global(.__wab_instance) {
  display: flex;
}
.subDeleteButton:global(.__wab_instance) {
  position: absolute;
  right: 16px;
  top: 16px;
  max-width: 100%;
  flex-shrink: 0;
  display: none;
}
.subDeleteButtoneditable:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
