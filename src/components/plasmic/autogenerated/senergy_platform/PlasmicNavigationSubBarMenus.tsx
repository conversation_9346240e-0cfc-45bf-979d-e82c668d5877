/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 19fXsiHSxAKf

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentIconButton from "../../SubcomponentIconButton"; // plasmic-import: JPWHSAS340Sf/component
import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import BookmarksBookmarkTiles from "../../BookmarksBookmarkTiles"; // plasmic-import: _UuKQc4xy7Sd/component
import MessagesMessagePreview from "../../MessagesMessagePreview"; // plasmic-import: iicTcR3JeMJ9/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationSubBarMenus.module.css"; // plasmic-import: 19fXsiHSxAKf/css

import SearchIcon from "./icons/PlasmicIcon__Search"; // plasmic-import: hosM0D-aZ2A1/icon

createPlasmicElementProxy;

export type PlasmicNavigationSubBarMenus__VariantMembers = {
  subMenuContent: "messages" | "search" | "bookmarks";
  comingSoon: "comingSoon";
};
export type PlasmicNavigationSubBarMenus__VariantsArgs = {
  subMenuContent?: SingleChoiceArg<"messages" | "search" | "bookmarks">;
  comingSoon?: SingleBooleanChoiceArg<"comingSoon">;
};
type VariantPropType = keyof PlasmicNavigationSubBarMenus__VariantsArgs;
export const PlasmicNavigationSubBarMenus__VariantProps =
  new Array<VariantPropType>("subMenuContent", "comingSoon");

export type PlasmicNavigationSubBarMenus__ArgsType = {
  onClickCloseButton?: (event: any) => void;
};
type ArgPropType = keyof PlasmicNavigationSubBarMenus__ArgsType;
export const PlasmicNavigationSubBarMenus__ArgProps = new Array<ArgPropType>(
  "onClickCloseButton"
);

export type PlasmicNavigationSubBarMenus__OverridesType = {
  subNavBarContainer?: Flex__<"div">;
  headerContainer?: Flex__<"section">;
  headerFormat?: Flex__<"div">;
  text?: Flex__<"div">;
  subcomponentIconButton?: Flex__<typeof SubcomponentIconButton>;
  contentContainer?: Flex__<"section">;
  searchFormatting?: Flex__<"div">;
  searchBar?: Flex__<typeof SubcomponentTextInput>;
  icon?: Flex__<"svg">;
  glassOverlay?: Flex__<"section">;
  h3?: Flex__<"h3">;
  bookmarksBookmarkTiles?: Flex__<typeof BookmarksBookmarkTiles>;
  messagesMessagePreview?: Flex__<typeof MessagesMessagePreview>;
};

export interface DefaultNavigationSubBarMenusProps {
  onClickCloseButton?: (event: any) => void;
  subMenuContent?: SingleChoiceArg<"messages" | "search" | "bookmarks">;
  comingSoon?: SingleBooleanChoiceArg<"comingSoon">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationSubBarMenus__RenderFunc(props: {
  variants: PlasmicNavigationSubBarMenus__VariantsArgs;
  args: PlasmicNavigationSubBarMenus__ArgsType;
  overrides: PlasmicNavigationSubBarMenus__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "subMenuContent",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.subMenuContent
      },
      {
        path: "searchBar.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "comingSoon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.comingSoon
      },
      {
        path: "searchBar.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"subNavBarContainer"}
      data-plasmic-override={overrides.subNavBarContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.subNavBarContainer,
        {
          [sty.subNavBarContainercomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.subNavBarContainersubMenuContent_bookmarks]: hasVariant(
            $state,
            "subMenuContent",
            "bookmarks"
          ),
          [sty.subNavBarContainersubMenuContent_messages]: hasVariant(
            $state,
            "subMenuContent",
            "messages"
          ),
          [sty.subNavBarContainersubMenuContent_search]: hasVariant(
            $state,
            "subMenuContent",
            "search"
          )
        }
      )}
    >
      <section
        data-plasmic-name={"headerContainer"}
        data-plasmic-override={overrides.headerContainer}
        className={classNames(projectcss.all, sty.headerContainer, {
          [sty.headerContainercomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.headerContainersubMenuContent_bookmarks]: hasVariant(
            $state,
            "subMenuContent",
            "bookmarks"
          ),
          [sty.headerContainersubMenuContent_messages]: hasVariant(
            $state,
            "subMenuContent",
            "messages"
          )
        })}
      >
        <div
          data-plasmic-name={"headerFormat"}
          data-plasmic-override={overrides.headerFormat}
          className={classNames(projectcss.all, sty.headerFormat, {
            [sty.headerFormatsubMenuContent_messages]: hasVariant(
              $state,
              "subMenuContent",
              "messages"
            ),
            [sty.headerFormatsubMenuContent_search]: hasVariant(
              $state,
              "subMenuContent",
              "search"
            )
          })}
        >
          <div
            data-plasmic-name={"text"}
            data-plasmic-override={overrides.text}
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text,
              {
                [sty.textsubMenuContent_bookmarks]: hasVariant(
                  $state,
                  "subMenuContent",
                  "bookmarks"
                ),
                [sty.textsubMenuContent_messages]: hasVariant(
                  $state,
                  "subMenuContent",
                  "messages"
                ),
                [sty.textsubMenuContent_search]: hasVariant(
                  $state,
                  "subMenuContent",
                  "search"
                )
              }
            )}
          >
            {hasVariant($state, "subMenuContent", "bookmarks")
              ? "Bookmarks"
              : hasVariant($state, "subMenuContent", "search")
              ? "Search"
              : hasVariant($state, "subMenuContent", "messages")
              ? "Messages"
              : "Header"}
          </div>
          <SubcomponentIconButton
            data-plasmic-name={"subcomponentIconButton"}
            data-plasmic-override={overrides.subcomponentIconButton}
            className={classNames(
              "__wab_instance",
              sty.subcomponentIconButton,
              {
                [sty.subcomponentIconButtonsubMenuContent_messages]: hasVariant(
                  $state,
                  "subMenuContent",
                  "messages"
                )
              }
            )}
            onClickCloseButton={args.onClickCloseButton}
          />
        </div>
      </section>
      <section
        data-plasmic-name={"contentContainer"}
        data-plasmic-override={overrides.contentContainer}
        className={classNames(projectcss.all, sty.contentContainer, {
          [sty.contentContainercomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.contentContainersubMenuContent_bookmarks]: hasVariant(
            $state,
            "subMenuContent",
            "bookmarks"
          ),
          [sty.contentContainersubMenuContent_messages]: hasVariant(
            $state,
            "subMenuContent",
            "messages"
          ),
          [sty.contentContainersubMenuContent_search]: hasVariant(
            $state,
            "subMenuContent",
            "search"
          )
        })}
      >
        <div
          data-plasmic-name={"searchFormatting"}
          data-plasmic-override={overrides.searchFormatting}
          className={classNames(projectcss.all, sty.searchFormatting, {
            [sty.searchFormattingsubMenuContent_bookmarks]: hasVariant(
              $state,
              "subMenuContent",
              "bookmarks"
            ),
            [sty.searchFormattingsubMenuContent_messages]: hasVariant(
              $state,
              "subMenuContent",
              "messages"
            ),
            [sty.searchFormattingsubMenuContent_search]: hasVariant(
              $state,
              "subMenuContent",
              "search"
            )
          })}
        >
          <SubcomponentTextInput
            data-plasmic-name={"searchBar"}
            data-plasmic-override={overrides.searchBar}
            className={classNames("__wab_instance", sty.searchBar, {
              [sty.searchBarcomingSoon]: hasVariant(
                $state,
                "comingSoon",
                "comingSoon"
              ),
              [sty.searchBarsubMenuContent_bookmarks]: hasVariant(
                $state,
                "subMenuContent",
                "bookmarks"
              ),
              [sty.searchBarsubMenuContent_messages]: hasVariant(
                $state,
                "subMenuContent",
                "messages"
              ),
              [sty.searchBarsubMenuContent_search]: hasVariant(
                $state,
                "subMenuContent",
                "search"
              )
            })}
            editView={"core"}
            errorMessage={generateStateValueProp($state, [
              "searchBar",
              "errorMessage"
            ])}
            inputPlaceholder={"Search"}
            inputValue={generateStateValueProp($state, ["searchBar", "value"])}
            onErrorMessageChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "searchBar",
                "errorMessage"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, ["searchBar", "value"]).apply(
                null,
                eventArgs
              );

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
          />

          <SearchIcon
            data-plasmic-name={"icon"}
            data-plasmic-override={overrides.icon}
            className={classNames(projectcss.all, sty.icon, {
              [sty.iconsubMenuContent_messages]: hasVariant(
                $state,
                "subMenuContent",
                "messages"
              )
            })}
            role={"img"}
          />
        </div>
        <section
          data-plasmic-name={"glassOverlay"}
          data-plasmic-override={overrides.glassOverlay}
          className={classNames(projectcss.all, sty.glassOverlay, {
            [sty.glassOverlaycomingSoon]: hasVariant(
              $state,
              "comingSoon",
              "comingSoon"
            ),
            [sty.glassOverlaycomingSoon_subMenuContent_search]:
              hasVariant($state, "subMenuContent", "search") &&
              hasVariant($state, "comingSoon", "comingSoon"),
            [sty.glassOverlaysubMenuContent_bookmarks]: hasVariant(
              $state,
              "subMenuContent",
              "bookmarks"
            ),
            [sty.glassOverlaysubMenuContent_messages]: hasVariant(
              $state,
              "subMenuContent",
              "messages"
            ),
            [sty.glassOverlaysubMenuContent_search]: hasVariant(
              $state,
              "subMenuContent",
              "search"
            )
          })}
        />

        <h3
          data-plasmic-name={"h3"}
          data-plasmic-override={overrides.h3}
          className={classNames(
            projectcss.all,
            projectcss.h3,
            projectcss.__wab_text,
            sty.h3,
            {
              [sty.h3comingSoon]: hasVariant(
                $state,
                "comingSoon",
                "comingSoon"
              ),
              [sty.h3subMenuContent_bookmarks]: hasVariant(
                $state,
                "subMenuContent",
                "bookmarks"
              ),
              [sty.h3subMenuContent_messages]: hasVariant(
                $state,
                "subMenuContent",
                "messages"
              ),
              [sty.h3subMenuContent_search]: hasVariant(
                $state,
                "subMenuContent",
                "search"
              )
            }
          )}
        >
          {" Coming Soon "}
        </h3>
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))([
          2, 3, 4
        ]).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (
            <BookmarksBookmarkTiles
              data-plasmic-name={"bookmarksBookmarkTiles"}
              data-plasmic-override={overrides.bookmarksBookmarkTiles}
              className={classNames(
                "__wab_instance",
                sty.bookmarksBookmarkTiles,
                {
                  [sty.bookmarksBookmarkTilessubMenuContent_bookmarks]:
                    hasVariant($state, "subMenuContent", "bookmarks"),
                  [sty.bookmarksBookmarkTilessubMenuContent_messages]:
                    hasVariant($state, "subMenuContent", "messages"),
                  [sty.bookmarksBookmarkTilessubMenuContent_search]: hasVariant(
                    $state,
                    "subMenuContent",
                    "search"
                  )
                }
              )}
              key={currentIndex}
            />
          );
        })}
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))([
          2, 3, 4
        ]).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (
            <MessagesMessagePreview
              data-plasmic-name={"messagesMessagePreview"}
              data-plasmic-override={overrides.messagesMessagePreview}
              className={classNames(
                "__wab_instance",
                sty.messagesMessagePreview,
                {
                  [sty.messagesMessagePreviewsubMenuContent_bookmarks]:
                    hasVariant($state, "subMenuContent", "bookmarks"),
                  [sty.messagesMessagePreviewsubMenuContent_messages]:
                    hasVariant($state, "subMenuContent", "messages")
                }
              )}
              key={currentIndex}
            />
          );
        })}
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  subNavBarContainer: [
    "subNavBarContainer",
    "headerContainer",
    "headerFormat",
    "text",
    "subcomponentIconButton",
    "contentContainer",
    "searchFormatting",
    "searchBar",
    "icon",
    "glassOverlay",
    "h3",
    "bookmarksBookmarkTiles",
    "messagesMessagePreview"
  ],
  headerContainer: [
    "headerContainer",
    "headerFormat",
    "text",
    "subcomponentIconButton"
  ],
  headerFormat: ["headerFormat", "text", "subcomponentIconButton"],
  text: ["text"],
  subcomponentIconButton: ["subcomponentIconButton"],
  contentContainer: [
    "contentContainer",
    "searchFormatting",
    "searchBar",
    "icon",
    "glassOverlay",
    "h3",
    "bookmarksBookmarkTiles",
    "messagesMessagePreview"
  ],
  searchFormatting: ["searchFormatting", "searchBar", "icon"],
  searchBar: ["searchBar"],
  icon: ["icon"],
  glassOverlay: ["glassOverlay"],
  h3: ["h3"],
  bookmarksBookmarkTiles: ["bookmarksBookmarkTiles"],
  messagesMessagePreview: ["messagesMessagePreview"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  subNavBarContainer: "div";
  headerContainer: "section";
  headerFormat: "div";
  text: "div";
  subcomponentIconButton: typeof SubcomponentIconButton;
  contentContainer: "section";
  searchFormatting: "div";
  searchBar: typeof SubcomponentTextInput;
  icon: "svg";
  glassOverlay: "section";
  h3: "h3";
  bookmarksBookmarkTiles: typeof BookmarksBookmarkTiles;
  messagesMessagePreview: typeof MessagesMessagePreview;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationSubBarMenus__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationSubBarMenus__VariantsArgs;
    args?: PlasmicNavigationSubBarMenus__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicNavigationSubBarMenus__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationSubBarMenus__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationSubBarMenus__ArgProps,
          internalVariantPropNames: PlasmicNavigationSubBarMenus__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationSubBarMenus__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "subNavBarContainer") {
    func.displayName = "PlasmicNavigationSubBarMenus";
  } else {
    func.displayName = `PlasmicNavigationSubBarMenus.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationSubBarMenus = Object.assign(
  // Top-level PlasmicNavigationSubBarMenus renders the root element
  makeNodeComponent("subNavBarContainer"),
  {
    // Helper components rendering sub-elements
    headerContainer: makeNodeComponent("headerContainer"),
    headerFormat: makeNodeComponent("headerFormat"),
    text: makeNodeComponent("text"),
    subcomponentIconButton: makeNodeComponent("subcomponentIconButton"),
    contentContainer: makeNodeComponent("contentContainer"),
    searchFormatting: makeNodeComponent("searchFormatting"),
    searchBar: makeNodeComponent("searchBar"),
    icon: makeNodeComponent("icon"),
    glassOverlay: makeNodeComponent("glassOverlay"),
    h3: makeNodeComponent("h3"),
    bookmarksBookmarkTiles: makeNodeComponent("bookmarksBookmarkTiles"),
    messagesMessagePreview: makeNodeComponent("messagesMessagePreview"),

    // Metadata about props expected for PlasmicNavigationSubBarMenus
    internalVariantProps: PlasmicNavigationSubBarMenus__VariantProps,
    internalArgProps: PlasmicNavigationSubBarMenus__ArgProps
  }
);

export default PlasmicNavigationSubBarMenus;
/* prettier-ignore-end */
