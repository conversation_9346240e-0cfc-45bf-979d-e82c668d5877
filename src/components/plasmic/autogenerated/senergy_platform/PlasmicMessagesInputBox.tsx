/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: _xepyTVjeZkI

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentButton from "../../SubcomponentButton"; // plasmic-import: ezhuRZvm_fH9/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicMessagesInputBox.module.css"; // plasmic-import: _xepyTVjeZkI/css

import PaperClipFileIcon from "./icons/PlasmicIcon__PaperClipFile"; // plasmic-import: hVrGYzCddhms/icon
import SendPaperPlaneIcon from "./icons/PlasmicIcon__SendPaperPlane"; // plasmic-import: osM7S8sYo-QG/icon

createPlasmicElementProxy;

export type PlasmicMessagesInputBox__VariantMembers = {
  active: "active";
};
export type PlasmicMessagesInputBox__VariantsArgs = {
  active?: SingleBooleanChoiceArg<"active">;
};
type VariantPropType = keyof PlasmicMessagesInputBox__VariantsArgs;
export const PlasmicMessagesInputBox__VariantProps = new Array<VariantPropType>(
  "active"
);

export type PlasmicMessagesInputBox__ArgsType = {};
type ArgPropType = keyof PlasmicMessagesInputBox__ArgsType;
export const PlasmicMessagesInputBox__ArgProps = new Array<ArgPropType>();

export type PlasmicMessagesInputBox__OverridesType = {
  formattingContainer?: Flex__<"div">;
  textEntryContainer?: Flex__<"section">;
  messageInput?: Flex__<"input">;
  sendAndFileIcons?: Flex__<"div">;
  subButton?: Flex__<typeof SubcomponentButton>;
  text?: Flex__<"div">;
};

export interface DefaultMessagesInputBoxProps {
  active?: SingleBooleanChoiceArg<"active">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicMessagesInputBox__RenderFunc(props: {
  variants: PlasmicMessagesInputBox__VariantsArgs;
  args: PlasmicMessagesInputBox__ArgsType;
  overrides: PlasmicMessagesInputBox__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "messageInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ``
      },
      {
        path: "active",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.active
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"formattingContainer"}
      data-plasmic-override={overrides.formattingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formattingContainer,
        {
          [sty.formattingContaineractive]: hasVariant(
            $state,
            "active",
            "active"
          )
        }
      )}
    >
      <section
        data-plasmic-name={"textEntryContainer"}
        data-plasmic-override={overrides.textEntryContainer}
        className={classNames(projectcss.all, sty.textEntryContainer)}
      >
        <input
          data-plasmic-name={"messageInput"}
          data-plasmic-override={overrides.messageInput}
          className={classNames(
            projectcss.all,
            projectcss.input,
            sty.messageInput,
            { [sty.messageInputactive]: hasVariant($state, "active", "active") }
          )}
          disabled={hasVariant($state, "active", "active") ? false : false}
          onChange={async (...eventArgs: any) => {
            (e => {
              generateStateOnChangeProp($state, ["messageInput", "value"])(
                e.target.value
              );
            }).apply(null, eventArgs);

            (async event => {
              const $steps = {};

              $steps["updateActive"] =
                $state.messageInput.value !== null
                  ? (() => {
                      const actionArgs = {
                        vgroup: "active",
                        operation: 4,
                        value: "active"
                      };
                      return (({ vgroup, value }) => {
                        if (typeof value === "string") {
                          value = [value];
                        }

                        $stateSet($state, vgroup, true);
                        return true;
                      })?.apply(null, [actionArgs]);
                    })()
                  : undefined;
              if (
                $steps["updateActive"] != null &&
                typeof $steps["updateActive"] === "object" &&
                typeof $steps["updateActive"].then === "function"
              ) {
                $steps["updateActive"] = await $steps["updateActive"];
              }

              $steps["updateActive2"] =
                $state.messageInput.value == ""
                  ? (() => {
                      const actionArgs = { vgroup: "active", operation: 6 };
                      return (({ vgroup, value }) => {
                        if (typeof value === "string") {
                          value = [value];
                        }

                        $stateSet($state, vgroup, false);
                        return false;
                      })?.apply(null, [actionArgs]);
                    })()
                  : undefined;
              if (
                $steps["updateActive2"] != null &&
                typeof $steps["updateActive2"] === "object" &&
                typeof $steps["updateActive2"].then === "function"
              ) {
                $steps["updateActive2"] = await $steps["updateActive2"];
              }
            }).apply(null, eventArgs);
          }}
          placeholder={"Message Firstname Lastname"}
          ref={ref => {
            $refs["messageInput"] = ref;
          }}
          type={"text"}
          value={
            generateStateValueProp($state, ["messageInput", "value"]) ?? ""
          }
        />

        <div
          data-plasmic-name={"sendAndFileIcons"}
          data-plasmic-override={overrides.sendAndFileIcons}
          className={classNames(projectcss.all, sty.sendAndFileIcons)}
        >
          <PaperClipFileIcon
            className={classNames(projectcss.all, sty.svg__qYzAd, {
              [sty.svgactive__qYzADddr0R]: hasVariant(
                $state,
                "active",
                "active"
              )
            })}
            role={"img"}
          />

          <SubcomponentButton
            data-plasmic-name={"subButton"}
            data-plasmic-override={overrides.subButton}
            className={classNames("__wab_instance", sty.subButton, {
              [sty.subButtonactive]: hasVariant($state, "active", "active")
            })}
            endIcon={
              <svg
                className={classNames(projectcss.all, sty.svg__eP5Ux)}
                role={"img"}
              />
            }
            showStartIcon={true}
            size={"minimal"}
            startIcon={
              <SendPaperPlaneIcon
                className={classNames(projectcss.all, sty.svg__fL1G, {
                  [sty.svgactive__fL1Gddr0R]: hasVariant(
                    $state,
                    "active",
                    "active"
                  )
                })}
                role={"img"}
              />
            }
            styling={["nittiWColor"]}
          >
            {"Send"}
          </SubcomponentButton>
        </div>
      </section>
      <div
        data-plasmic-name={"text"}
        data-plasmic-override={overrides.text}
        className={classNames(projectcss.all, projectcss.__wab_text, sty.text, {
          [sty.textactive]: hasVariant($state, "active", "active")
        })}
      >
        {hasVariant($state, "active", "active") ? (
          <React.Fragment>
            <span
              className={"plasmic_default__all plasmic_default__span"}
              style={{ fontWeight: 700 }}
            >
              {"Shift"}
            </span>
            <React.Fragment> </React.Fragment>
            <span
              className={"plasmic_default__all plasmic_default__span"}
              style={{ fontWeight: 700 }}
            >
              {"+"}
            </span>
            <React.Fragment> </React.Fragment>
            <span
              className={"plasmic_default__all plasmic_default__span"}
              style={{ fontWeight: 700 }}
            >
              {"Return"}
            </span>
            <React.Fragment>{" to add a new line"}</React.Fragment>
          </React.Fragment>
        ) : (
          <React.Fragment>
            <span
              className={"plasmic_default__all plasmic_default__span"}
              style={{ fontWeight: 700 }}
            >
              {"Shift + Return"}
            </span>
            <React.Fragment>{" to add a new line"}</React.Fragment>
          </React.Fragment>
        )}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formattingContainer: [
    "formattingContainer",
    "textEntryContainer",
    "messageInput",
    "sendAndFileIcons",
    "subButton",
    "text"
  ],
  textEntryContainer: [
    "textEntryContainer",
    "messageInput",
    "sendAndFileIcons",
    "subButton"
  ],
  messageInput: ["messageInput"],
  sendAndFileIcons: ["sendAndFileIcons", "subButton"],
  subButton: ["subButton"],
  text: ["text"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formattingContainer: "div";
  textEntryContainer: "section";
  messageInput: "input";
  sendAndFileIcons: "div";
  subButton: typeof SubcomponentButton;
  text: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicMessagesInputBox__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicMessagesInputBox__VariantsArgs;
    args?: PlasmicMessagesInputBox__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicMessagesInputBox__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicMessagesInputBox__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicMessagesInputBox__ArgProps,
          internalVariantPropNames: PlasmicMessagesInputBox__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicMessagesInputBox__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formattingContainer") {
    func.displayName = "PlasmicMessagesInputBox";
  } else {
    func.displayName = `PlasmicMessagesInputBox.${nodeName}`;
  }
  return func;
}

export const PlasmicMessagesInputBox = Object.assign(
  // Top-level PlasmicMessagesInputBox renders the root element
  makeNodeComponent("formattingContainer"),
  {
    // Helper components rendering sub-elements
    textEntryContainer: makeNodeComponent("textEntryContainer"),
    messageInput: makeNodeComponent("messageInput"),
    sendAndFileIcons: makeNodeComponent("sendAndFileIcons"),
    subButton: makeNodeComponent("subButton"),
    text: makeNodeComponent("text"),

    // Metadata about props expected for PlasmicMessagesInputBox
    internalVariantProps: PlasmicMessagesInputBox__VariantProps,
    internalArgProps: PlasmicMessagesInputBox__ArgProps
  }
);

export default PlasmicMessagesInputBox;
/* prettier-ignore-end */
