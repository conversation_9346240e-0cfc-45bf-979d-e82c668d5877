.onboardingModalContainer {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 800px;
  justify-content: flex-start;
  align-items: center;
  background: var(--token-5_Q90hFZ9CmK);
  transition-property: all;
  transition-duration: 1s;
  min-width: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 1s;
  padding: 30px 36px 16px;
}
.headerSection {
  display: grid;
  position: relative;
  justify-items: center;
  width: 100%;
  height: auto;
  min-width: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.headerSection > * {
  grid-column: 4;
}
.headerSectionstage__0 > * {
  grid-column: 4;
}
.headerSectionstage__1 > * {
  grid-column: 4;
}
.headerSectionstage__2 > * {
  grid-column: 4;
}
.headerSectionstage__3 {
  margin-bottom: 110px;
}
.headerSectionstage__3 > * {
  grid-column: 4;
}
.headerSectionstage__4 {
  margin-bottom: 110px;
}
.headerSectionstage__4 > * {
  grid-column: 4;
}
.headerSectionstage__5 {
  margin-bottom: 110px;
}
.headerSectionstage__5 > * {
  grid-column: 4;
}
.headerSectionstage__6 {
  margin-bottom: 110px;
}
.headerSectionstage__6 > * {
  grid-column: 4;
}
.headerSectionstage__7 {
  margin-bottom: 110px;
}
.headerSectionstage__7 > * {
  grid-column: 4;
}
.headerSectionstage__8 {
  margin-bottom: 0px;
}
.headerSectionstage__8 > * {
  grid-column: 4;
}
.headerSectionstage_nextStepsInfo > * {
  grid-column: 4;
}
.text__auVRm {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  text-align: center;
  font-family: var(--token-IfwtVZvVvF7g);
  font-size: var(--token-hKlnSDJVAhUx);
  margin-bottom: 16px;
  min-width: 0;
}
.textstage_nextStepsInfo__auVRm1Qfba {
  margin-bottom: 35px;
}
.progressButtons {
  display: flex;
  position: absolute;
  width: 100%;
  flex-direction: row;
  justify-content: flex-end;
  align-items: flex-end;
  margin-top: 0px;
  left: 0px;
  z-index: 1;
  height: auto;
  bottom: 0px;
  margin-bottom: 36px;
  column-gap: var(--token-j0qnbpah5w9U);
  min-width: 0;
  padding: var(--token-j0qnbpah5w9U) 65px var(--token-j0qnbpah5w9U)
    var(--token-j0qnbpah5w9U);
}
.progressButtonsstage__0 {
  display: none;
}
.progressButtonsstage__2 {
  margin-top: 42px;
}
.progressButtonsstage__3 {
  margin-top: 42px;
}
.progressButtonsstage__4 {
  margin-top: 42px;
}
.progressButtonsstage__5 {
  margin-top: 42px;
}
.progressButtonsstage__6 {
  margin-top: 42px;
}
.progressButtonsstage__7 {
  margin-top: 42px;
}
.progressButtonsstage_nextStepsInfo {
  display: none;
}
.goBackButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.svg__hrfmq {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__zPLlc {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.continueButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.svg__a15V5 {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__oIvR0 {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.lastStepChecklist {
  position: relative;
  width: 90%;
  flex-direction: column;
  background: none;
  height: 100%;
  margin-bottom: 0px;
  align-items: center;
  justify-content: flex-start;
  padding-top: 40px;
  min-height: 0;
  display: none;
}
.lastStepCheckliststage_nextStepsInfo {
  display: flex;
}
.text__sJ38L {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-_-i82ElPHE7I);
  text-align: center;
  margin-left: 50px;
  margin-right: 51px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.freeBox__m0DrX {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  padding: 0px var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk);
  margin: 30px 0px 20px;
}
.freeBox__xyWx7 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  padding: var(--token-sazGmnf7GWAk);
}
.svg___6Xvh3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  height: 1em;
}
.text__aCs1E {
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.freeBox__pkx3 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.svg__zDrEg {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  height: 1em;
}
.text__tXkKd {
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  margin-right: 0px;
}
.text__ygDmZ {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  margin-top: 20px;
  text-align: center;
  min-width: 0;
}
.textstage_nextStepsInfo__ygDmZ1Qfba {
  display: none;
}
.contExploreButtonStack {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-M1l4keX1sfKm);
  min-width: 0;
  padding: var(--token-M1l4keX1sfKm);
  margin: 20px var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk);
}
.subcomponentSelectorButtonsWSlot___0Nwxq:global(.__wab_instance) {
  max-width: 100%;
  flex-shrink: 0;
}
.subcomponentSelectorButtonsWSlotstage_nextStepsInfo___0Nwxq1Qfba:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.svg___8DtLr {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__wkIdM {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-2UEfYzPsoOY0);
}
.subcomponentSelectorButtonsWSlot__u5VOj:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.subcomponentSelectorButtonsWSlotstage_nextStepsInfo__u5VOj1Qfba:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.svg___8Z9Jn {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__lTxg {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-2UEfYzPsoOY0);
}
.subcomponentSelectorButtonsWSlot__uN8PP:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.subcomponentSelectorButtonsWSlotstage_nextStepsInfo__uN8PP1Qfba:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
  display: none;
}
.svg__qyAuf {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__qy3U {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-2UEfYzPsoOY0);
}
.subcomponentSelectorButtonsWSlot__q4CxW:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.subcomponentSelectorButtonsWSlotstage_nextStepsInfo__q4CxW1Qfba:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.svg__a5Gki {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text___1EO9M {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-2UEfYzPsoOY0);
}
.freeBox__w4IPg {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk) 0px;
}
.text__wuCq {
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-AMP1angFPBtf);
  text-align: center;
  padding-right: 144px;
  padding-left: 144px;
  margin-bottom: 65px;
  min-width: 0;
  display: none;
}
.textstage__1__wuCqnaH0S {
  margin-bottom: 20px;
  padding-right: 130px;
  padding-left: 130px;
  display: block;
}
.textstage__2__wuCqBu8Sz {
  display: block;
}
.textstage__3__wuCq772Qm {
  display: none;
}
.textstage__4__wuCq012I3 {
  display: none;
}
.textstage__5__wuCqJtAdo {
  display: none;
}
.textstage__6__wuCqMwi4C {
  display: none;
}
.textstage__7__wuCqFs7M {
  display: none;
}
.textstage__8__wuCq2KsfG {
  display: block;
}
.bannerSetupSection {
  box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  position: relative;
  width: 100%;
  flex-direction: column;
  height: auto;
  justify-content: center;
  align-items: stretch;
  min-height: 275px;
  background: var(--token-xo_r2w5pebq-);
  margin-bottom: 0px;
  margin-top: 0px;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk) 36px;
}
.bannerSetupSectionstage__1 {
  display: none;
}
.bannerSetupSectionstage__2 {
  display: flex;
}
.bannerSetupSectionstage__3 {
  display: flex;
}
.bannerSetupSectionstage__4 {
  display: flex;
}
.bannerSetupSectionstage__5 {
  display: flex;
}
.bannerSetupSectionstage__6 {
  display: flex;
}
.bannerSetupSectionstage__7 {
  display: flex;
}
.bannerSetupSectionstage__8 {
  display: flex;
}
.bannerContentFormatting {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  height: auto;
  flex-wrap: wrap;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.leftSideBanner {
  display: flex;
  width: auto;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  position: relative;
  padding: var(--token-sazGmnf7GWAk);
}
.profileCoreProfileImage__idDcM:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.nameStack {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__cuyw5 {
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
}
.text__ovts {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
}
.additionalInformation {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.location2:global(.__wab_instance) {
  max-width: 100%;
}
.iconSpot {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.workingHours2:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.iconSpot2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.rightSideBanner {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  height: auto;
  flex-grow: 0;
  flex-shrink: 1;
}
.elevatorPitch {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.adjectiveAndTitle {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__br0V9 {
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
}
.text__j2U8L {
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
  font-weight: 700;
}
.text__mAyp2 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
}
.subcomponentButton__giXws:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.svg__mbVu3 {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__hbPze {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.exampleBanner {
  box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  position: relative;
  width: 100%;
  flex-direction: column;
  height: auto;
  justify-content: center;
  align-items: stretch;
  min-height: 275px;
  background: var(--token-xo_r2w5pebq-);
  margin-bottom: 0px;
  margin-top: 0px;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk) 36px;
}
.exampleBannerstage__1 {
  display: flex;
}
.exampleBannerstage__2 {
  display: none;
}
.exampleBannerstage__3 {
  display: none;
}
.exampleBannerstage__4 {
  display: none;
}
.exampleBannerstage__5 {
  display: none;
}
.exampleBannerstage__6 {
  display: none;
}
.exampleBannerstage__7 {
  display: none;
}
.exampleBannerstage__8 {
  display: none;
}
.bannerContentFormatting2 {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  height: auto;
  flex-wrap: wrap;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.leftSideBanner2 {
  display: flex;
  width: auto;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  position: relative;
  padding: var(--token-sazGmnf7GWAk);
}
.profileCoreProfileImage__mpEw:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.nameStack2 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__doyx2 {
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
}
.text__tApJa {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
}
.additionalInformation2 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.location3:global(.__wab_instance) {
  max-width: 100%;
}
.iconSpot3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.workingHours3:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.iconSpot4 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.rightSideBanner2 {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  height: auto;
  flex-grow: 0;
  flex-shrink: 1;
}
.elevatorPitch2 {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.adjectiveAndTitle2 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__wdIoh {
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
}
.text__rdbUx {
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
  font-weight: 700;
}
.text__htGoP {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
}
.subcomponentButton__vwrff:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.svg__qmmdy {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__wg2Os {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.onboardingProcess {
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100%;
  background: var(--token-xo_r2w5pebq-);
  transition-property: all;
  transition-duration: 0.5s;
  min-width: 0;
  display: none;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.5s;
  padding: var(--token-VuPwbNNk9FIa);
}
.onboardingProcess > * {
  grid-column: 4;
}
.onboardingProcessstage__0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background: none;
  column-gap: 0px;
  padding: var(--token-j0qnbpah5w9U);
}
.onboardingProcessstage__0 > * {
  grid-column: 4;
}
.onboardingProcessstage__1 {
  background: none;
  display: none;
}
.onboardingProcessstage__1 > * {
  grid-column: 4;
}
.onboardingProcessstage__2 {
  background: none;
  justify-items: center;
  align-content: center;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  column-gap: 0px;
  padding: var(--token-j0qnbpah5w9U);
}
.onboardingProcessstage__2 > * {
  grid-column: 4;
}
.onboardingProcessstage__3 {
  background: none;
  justify-items: center;
  align-content: center;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  column-gap: 0px;
  padding: var(--token-j0qnbpah5w9U);
}
.onboardingProcessstage__3 > * {
  grid-column: 4;
}
.onboardingProcessstage__4 {
  background: none;
  justify-items: center;
  align-content: center;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  column-gap: 0px;
  padding: var(--token-j0qnbpah5w9U);
}
.onboardingProcessstage__4 > * {
  grid-column: 4;
}
.onboardingProcessstage__5 {
  background: none;
  justify-items: center;
  align-content: center;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  column-gap: 0px;
  padding: var(--token-j0qnbpah5w9U);
}
.onboardingProcessstage__5 > * {
  grid-column: 4;
}
.onboardingProcessstage__6 {
  background: none;
  justify-items: center;
  align-content: center;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  column-gap: 0px;
  padding: var(--token-j0qnbpah5w9U);
}
.onboardingProcessstage__6 > * {
  grid-column: 4;
}
.onboardingProcessstage__7 {
  background: none;
  justify-items: center;
  align-content: center;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  column-gap: 0px;
  padding: var(--token-j0qnbpah5w9U);
}
.onboardingProcessstage__7 > * {
  grid-column: 4;
}
.onboardingProcessstage__8 {
  background: none;
  display: grid;
}
.onboardingProcessstage__8 > * {
  grid-column: 4;
}
.text__bhg2E {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-hKlnSDJVAhUx);
  min-width: 0;
  display: none;
}
.textstage__0__bhg2Eg1XPd {
  text-align: center;
  font-size: var(--token-AMP1angFPBtf);
  margin-bottom: 24px;
  width: auto;
  margin-right: 144px;
  margin-left: 144px;
  padding-right: 0px;
  display: block;
}
.buttonContainer {
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  display: none;
  padding: var(--token-j0qnbpah5w9U);
}
.buttonContainerstage__0 {
  display: flex;
}
.buttonContainerstage__2 {
  display: none;
}
.topStack {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  flex-wrap: nowrap;
  align-content: stretch;
  margin-bottom: 20px;
  column-gap: var(--token-j0qnbpah5w9U);
  min-width: 0;
}
.subcomponentSelectorButtonsWSlot__hNybT:global(.__wab_instance) {
  max-width: 100%;
  display: none;
}
.subcomponentSelectorButtonsWSlotstage__0__hNybTg1XPd:global(.__wab_instance) {
  display: flex;
}
.svg__qwhlw {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 60px;
  height: 60px;
  margin-bottom: 8px;
}
.text__z4Cf9 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.subcomponentSelectorButtonsWSlot__myyse:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.svg__fWW7 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 60px;
  height: 60px;
  margin-bottom: 8px;
}
.text___1MPbg {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-top: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.subcomponentSelectorButtonsWSlot___6H6Q:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.freeBox__ivz1 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: auto;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk) 8px;
}
.svg__bXyQ4 {
  object-fit: cover;
  max-width: 100%;
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}
.svg__txs2Q {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}
.text__kmVzb {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.text__pSBlh {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-AMP1angFPBtf);
  text-align: center;
  min-width: 0;
}
.textstage__1__pSBlhnaH0S {
  display: none;
}
.textstage__2__pSBlhBu8Sz {
  margin-bottom: 12px;
  display: block;
}
.textstage__3__pSBlh772Qm {
  margin-bottom: 12px;
  padding-bottom: 0px;
  display: block;
}
.textstage__4__pSBlh012I3 {
  margin-bottom: 12px;
  display: block;
}
.textstage__5__pSBlhJtAdo {
  margin-bottom: 12px;
  padding-top: 0px;
  display: block;
}
.textstage__6__pSBlhMwi4C {
  margin-bottom: 12px;
  display: block;
}
.textstage__7__pSBlhFs7M {
  margin-bottom: 12px;
  display: block;
}
.textstage__8__pSBlh2KsfG {
  display: block;
}
.freeBox___3ClDj {
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: var(--token-4Wrp9mDZCSCQ);
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBoxstage__2___3ClDjbu8Sz {
  justify-content: center;
  align-items: center;
  display: flex;
}
.freeBoxstage__3___3ClDj772Qm {
  justify-content: center;
  align-items: center;
  display: flex;
}
.freeBoxstage__4___3ClDj012I3 {
  justify-content: center;
  align-items: center;
  display: flex;
}
.freeBoxstage__5___3ClDjjtAdo {
  justify-content: center;
  align-items: center;
  display: flex;
}
.freeBoxstage__6___3ClDjmwi4C {
  justify-content: center;
  align-items: center;
  display: flex;
}
.freeBoxstage__7___3ClDjfs7M {
  justify-content: center;
  align-items: center;
  display: flex;
}
.svg__oAmBm {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 30px;
  height: 30px;
  flex-shrink: 0;
  display: none;
}
.svgstage__2__oAmBmbu8Sz {
  display: block;
}
.svgstage__3__oAmBm772Qm {
  display: block;
}
.svgstage__4__oAmBm012I3 {
  display: block;
}
.svgstage__5__oAmBmjtAdo {
  display: block;
}
.svgstage__6__oAmBmmwi4C {
  display: block;
}
.svgstage__7__oAmBmfs7M {
  display: block;
}
.subTextInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.text__ngmno {
  position: relative;
  display: none;
}
.textstage__2__ngmnoBu8Sz {
  width: auto;
  justify-self: flex-start;
  font-size: var(--token-agfFfkxgxH6d);
  display: none;
}
.textstage__3__ngmno772Qm {
  width: auto;
  justify-self: flex-start;
  font-size: var(--token-agfFfkxgxH6d);
  display: none;
}
.textstage__4__ngmno012I3 {
  width: auto;
  justify-self: flex-start;
  font-size: var(--token-agfFfkxgxH6d);
  display: none;
}
.textstage__5__ngmnoJtAdo {
  width: auto;
  justify-self: flex-start;
  font-size: var(--token-agfFfkxgxH6d);
  display: none;
}
.textstage__6__ngmnoMwi4C {
  width: auto;
  justify-self: flex-start;
  font-size: var(--token-agfFfkxgxH6d);
  display: none;
}
.textstage__7__ngmnoFs7M {
  width: auto;
  justify-self: flex-start;
  font-size: var(--token-agfFfkxgxH6d);
  display: none;
}
.subMultilinePlaceholderTextInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 75%;
  margin-top: 20px;
  margin-bottom: 0px;
  z-index: 20;
  display: none;
}
.subMultilinePlaceholderTextInputstage__8:global(.__wab_instance) {
  display: none;
}
