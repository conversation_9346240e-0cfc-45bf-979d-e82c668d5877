/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: cEGGrNaGJB_3

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSearchCoreFilterIcon.module.css"; // plasmic-import: cEGGrNaGJB_3/css

createPlasmicElementProxy;

export type PlasmicSearchCoreFilterIcon__VariantMembers = {};
export type PlasmicSearchCoreFilterIcon__VariantsArgs = {};
type VariantPropType = keyof PlasmicSearchCoreFilterIcon__VariantsArgs;
export const PlasmicSearchCoreFilterIcon__VariantProps =
  new Array<VariantPropType>();

export type PlasmicSearchCoreFilterIcon__ArgsType = {
  children?: React.ReactNode;
};
type ArgPropType = keyof PlasmicSearchCoreFilterIcon__ArgsType;
export const PlasmicSearchCoreFilterIcon__ArgProps = new Array<ArgPropType>(
  "children"
);

export type PlasmicSearchCoreFilterIcon__OverridesType = {
  formattingBox?: Flex__<"div">;
  textSlot?: Flex__<"div">;
};

export interface DefaultSearchCoreFilterIconProps {
  children?: React.ReactNode;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSearchCoreFilterIcon__RenderFunc(props: {
  variants: PlasmicSearchCoreFilterIcon__VariantsArgs;
  args: PlasmicSearchCoreFilterIcon__ArgsType;
  overrides: PlasmicSearchCoreFilterIcon__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"formattingBox"}
      data-plasmic-override={overrides.formattingBox}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formattingBox
      )}
    >
      <div
        data-plasmic-name={"textSlot"}
        data-plasmic-override={overrides.textSlot}
        className={classNames(projectcss.all, sty.textSlot)}
      >
        {renderPlasmicSlot({
          defaultContents: "Filter Tag",
          value: args.children
        })}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formattingBox: ["formattingBox", "textSlot"],
  textSlot: ["textSlot"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formattingBox: "div";
  textSlot: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSearchCoreFilterIcon__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSearchCoreFilterIcon__VariantsArgs;
    args?: PlasmicSearchCoreFilterIcon__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSearchCoreFilterIcon__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSearchCoreFilterIcon__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSearchCoreFilterIcon__ArgProps,
          internalVariantPropNames: PlasmicSearchCoreFilterIcon__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSearchCoreFilterIcon__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formattingBox") {
    func.displayName = "PlasmicSearchCoreFilterIcon";
  } else {
    func.displayName = `PlasmicSearchCoreFilterIcon.${nodeName}`;
  }
  return func;
}

export const PlasmicSearchCoreFilterIcon = Object.assign(
  // Top-level PlasmicSearchCoreFilterIcon renders the root element
  makeNodeComponent("formattingBox"),
  {
    // Helper components rendering sub-elements
    textSlot: makeNodeComponent("textSlot"),

    // Metadata about props expected for PlasmicSearchCoreFilterIcon
    internalVariantProps: PlasmicSearchCoreFilterIcon__VariantProps,
    internalArgProps: PlasmicSearchCoreFilterIcon__ArgProps
  }
);

export default PlasmicSearchCoreFilterIcon;
/* prettier-ignore-end */
