'use client';
/*
 * Documentation:
 * Tooltip — https://app.subframe.com/library?component=Tooltip_ccebd1e9-f6ac-4737-8376-0dfacd90c9f3
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface TooltipRootProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  className?: string;
}

const TooltipRoot = React.forwardRef<HTMLDivElement, TooltipRootProps>(
  function TooltipRoot(
    { children, className, ...otherProps }: TooltipRootProps,
    ref
  ) {
    return (
      <div
        className={SubframeUtils.twClassNames(
          'flex flex-col items-start gap-2 rounded-md border border-neutral-900 border-solid bg-neutral-800 px-2 py-1 shadow-lg',
          className
        )}
        ref={ref}
        {...otherProps}
      >
        {children ? (
          <span className="font-caption text-caption text-white">
            {children}
          </span>
        ) : null}
      </div>
    );
  }
);

export const Tooltip = TooltipRoot;
