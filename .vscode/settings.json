{"typescript.tsdk": "node_modules/typescript/lib", "editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never", "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "explicit", "source.fixAll.biome": "explicit"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript][typescript][javascriptreact][typescriptreact][json][jsonc][css][graphql]": {"editor.defaultFormatter": "biomejs.biome"}, "biome.requireConfiguration": true}